@extends('layouts.dashboard')

@section('title', 'Profil Saya')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Profil <PERSON></h1>
            <p class="text-muted">Kelola informasi profil dan pengaturan akun <PERSON></p>
        </div>
        <div>
            <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                <i class="bi bi-pencil me-1"></i> Edit Profil
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Profile Card -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    @if($user->avatar)
                        <img src="{{ Storage::url($user->avatar) }}" alt="Avatar" 
                             class="rounded-circle mb-3" width="150" height="150">
                    @else
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                             style="width: 150px; height: 150px; color: white; font-size: 3rem;">
                            {{ strtoupper(substr($user->name, 0, 1)) }}
                        </div>
                    @endif
                    
                    <h4 class="card-title">{{ $user->name }}</h4>
                    <p class="text-muted">{{ $user->email }}</p>
                    
                    <div class="mb-3">
                        <span class="badge {{ $user->role === 'admin' ? 'bg-danger' : 'bg-primary' }} fs-6">
                            {{ ucfirst($user->role) }}
                        </span>
                        
                        @if($user->email_verified_at)
                            <span class="badge bg-success fs-6">Email Terverifikasi</span>
                        @else
                            <span class="badge bg-warning fs-6">Email Belum Terverifikasi</span>
                        @endif
                    </div>
                    
                    @if($user->department || $user->position)
                        <div class="mt-3">
                            @if($user->position)
                                <p class="mb-1"><strong>{{ $user->position }}</strong></p>
                            @endif
                            @if($user->department)
                                <p class="text-muted">{{ $user->department }}</p>
                            @endif
                        </div>
                    @endif

                    @if(!$user->email_verified_at)
                        <div class="mt-3">
                            <button class="btn btn-warning btn-sm" onclick="resendVerification()">
                                <i class="bi bi-envelope me-1"></i> Kirim Ulang Verifikasi
                            </button>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistik Aktivitas</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="h4 text-primary">{{ $totalBookings ?? 0 }}</div>
                            <small class="text-muted">Total Pemesanan</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-success">{{ $approvedBookings ?? 0 }}</div>
                            <small class="text-muted">Disetujui</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <div class="h4 text-warning">{{ $pendingBookings ?? 0 }}</div>
                            <small class="text-muted">Menunggu</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-danger">{{ $rejectedBookings ?? 0 }}</div>
                            <small class="text-muted">Ditolak</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="col-xl-8 col-lg-7">
            <!-- Personal Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Personal</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nama Lengkap</label>
                                <p class="form-control-static">{{ $user->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="form-control-static">
                                    {{ $user->email }}
                                    @if($user->email_verified_at)
                                        <i class="bi bi-check-circle-fill text-success ms-1" title="Terverifikasi"></i>
                                    @else
                                        <i class="bi bi-x-circle-fill text-danger ms-1" title="Belum terverifikasi"></i>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nomor Telepon</label>
                                <p class="form-control-static">{{ $user->phone ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Departemen</label>
                                <p class="form-control-static">{{ $user->department ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Posisi</label>
                                <p class="form-control-static">{{ $user->position ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tanggal Bergabung</label>
                                <p class="form-control-static">{{ $user->created_at->format('d M Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Security -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Keamanan Akun</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Login Terakhir</label>
                                <p class="form-control-static">
                                    {{ $user->last_login_at ? $user->last_login_at->format('d M Y, H:i') : 'Belum pernah login' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">IP Address Terakhir</label>
                                <p class="form-control-static font-monospace">{{ $user->last_login_ip ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status Akun</label>
                                <p class="form-control-static">
                                    @if($user->account_locked)
                                        <span class="badge bg-danger">Terkunci</span>
                                    @else
                                        <span class="badge bg-success">Aktif</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Percobaan Login Gagal</label>
                                <p class="form-control-static">
                                    <span class="badge {{ $user->login_attempts > 3 ? 'bg-danger' : 'bg-success' }}">
                                        {{ $user->login_attempts }} kali
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="#" class="btn btn-outline-warning" onclick="changePassword()">
                            <i class="bi bi-key me-1"></i> Ubah Password
                        </a>
                        
                        @if($user->login_attempts > 0)
                            <button class="btn btn-outline-info ms-2" onclick="resetLoginAttempts()">
                                <i class="bi bi-arrow-clockwise me-1"></i> Reset Percobaan Login
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Pemesanan Terbaru</h6>
                    <a href="{{ route('bookings.index') }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @if(isset($recentBookings) && count($recentBookings) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Ruang</th>
                                        <th>Tanggal</th>
                                        <th>Waktu</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentBookings as $booking)
                                    <tr>
                                        <td>{{ $booking->meetingRoom->name }}</td>
                                        <td>{{ \Carbon\Carbon::parse($booking->date)->format('d M Y') }}</td>
                                        <td>{{ $booking->start_time }} - {{ $booking->end_time }}</td>
                                        <td>
                                            @if($booking->status === 'approved')
                                                <span class="badge bg-success">Disetujui</span>
                                            @elseif($booking->status === 'pending')
                                                <span class="badge bg-warning">Menunggu</span>
                                            @elseif($booking->status === 'rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($booking->status) }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('bookings.show', $booking->id) }}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">Belum ada pemesanan</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function resendVerification() {
    Swal.fire({
        title: 'Kirim Ulang Email Verifikasi?',
        text: 'Email verifikasi akan dikirim ke alamat email Anda.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Kirim!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/profile/resend-verification', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Email verifikasi telah dikirim.', 'success');
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function changePassword() {
    Swal.fire({
        title: 'Ubah Password',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label for="current_password" class="form-label">Password Saat Ini</label>
                    <input type="password" class="form-control" id="current_password" required>
                </div>
                <div class="mb-3">
                    <label for="new_password" class="form-label">Password Baru</label>
                    <input type="password" class="form-control" id="new_password" required>
                </div>
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                    <input type="password" class="form-control" id="confirm_password" required>
                </div>
            </div>
        `,
        focusConfirm: false,
        showCancelButton: true,
        confirmButtonText: 'Ubah Password',
        cancelButtonText: 'Batal',
        preConfirm: () => {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (!currentPassword || !newPassword || !confirmPassword) {
                Swal.showValidationMessage('Semua field harus diisi');
                return false;
            }
            
            if (newPassword !== confirmPassword) {
                Swal.showValidationMessage('Konfirmasi password tidak sesuai');
                return false;
            }
            
            if (newPassword.length < 8) {
                Swal.showValidationMessage('Password baru minimal 8 karakter');
                return false;
            }
            
            return {
                current_password: currentPassword,
                password: newPassword,
                password_confirmation: confirmPassword
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/profile/change-password', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Password berhasil diubah.', 'success');
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function resetLoginAttempts() {
    Swal.fire({
        title: 'Reset Percobaan Login?',
        text: 'Jumlah percobaan login gagal akan direset menjadi 0.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#17a2b8',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Reset!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/profile/reset-login-attempts', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Percobaan login telah direset.', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}
</script>
@endpush
@endsection
