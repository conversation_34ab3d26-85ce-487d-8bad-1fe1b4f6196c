<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\SecurityLog;
use App\Models\User;

class SecurityMiddleware
{
    protected $trustedProxies = [
        '127.0.0.1',
        '::1',
        '10.0.0.0/8',
        '**********/12',
        '***********/16'
    ];

    // Whitelist IP yang selalu diizinkan (tidak terkena security check)
    protected $whitelistedIPs = [
        '127.0.0.1',
        '::1',
        'localhost',
        '***********',      // Router lokal
        '***********',      // Router lokal alternatif
        '********',         // Network lokal
        '**********',       // Network lokal
        // Tambahkan IP lain yang perlu di-whitelist di sini
    ];

    // Development/local IP ranges yang tidak terkena security check
    protected $developmentRanges = [
        '*********/8',      // Localhost
        '10.0.0.0/8',       // Private network
        '**********/12',    // Private network
        '***********/16',   // Private network
    ];

    protected $dangerousPatterns = [
        // SQL Injection patterns
        '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b|\bCREATE\b|\bALTER\b)/i',
        '/(\bOR\s+\d+\s*=\s*\d+|\bAND\s+\d+\s*=\s*\d+)/i',
        '/(\b(exec|execute|sp_|xp_)\b)/i',
        '/(\'|\")\s*(;|--|\/\*|\*\/)/i',
        
        // XSS patterns
        '/<script[^>]*>.*?<\/script>/i',
        '/<iframe[^>]*>.*?<\/iframe>/i',
        '/javascript:/i',
        '/on\w+\s*=/i',
        '/<object[^>]*>.*?<\/object>/i',
        '/<embed[^>]*>/i',
        
        // Command Injection patterns
        '/(\||\&|\;|\$\(|\`)/i',
        '/(nc|netcat|wget|curl|ping|nslookup|dig)\s/i',
        
        // Path Traversal patterns
        '/(\.\.[\/\\]){2,}/i',
        '/(\/etc\/passwd|\/etc\/shadow|\/windows\/system32)/i',
        '/\.\.[\/\\].*?\.(conf|ini|log|sql|bak)/i',
        
        // File Inclusion patterns
        '/((http|https|ftp):\/\/.*?\.(php|asp|jsp|py))/i',
        '/(\binclude\b|\brequire\b).*?\.(php|asp|jsp)/i',
        
        // Web Shell patterns
        '/(\bc99|\bc100|\br57|\bp0wny|wso|b374k|\bshell|\bcmd)/i',
        '/(base64_decode|eval|exec|system|passthru|shell_exec)/i',
        
        // Directory Listing patterns
        '/(\bindex\s+of\b|\bdirectory\s+listing\b)/i',
        
        // Common attack tools signatures
        '/(\bburp\b|\bowasp[\s\-]?zap|\bsqlmap|\bnikto|\bwfuzz|\bdirb|\bgobuster|\bhydra|\bnmap)/i',
        '/(User-Agent.*?(burp|zap|sqlmap|nikto|wfuzz|dirb|gobuster|hydra|nmap))/i'
    ];

    protected $suspiciousHeaders = [
        'X-Forwarded-Host',
        'X-Original-URL',
        'X-Rewrite-URL',
        'Cluster-Client-IP',
        'Client-IP'
    ];

    public function handle(Request $request, Closure $next)
    {
        // 1. Check security mode
        $securityMode = Cache::get('security_mode', 'normal');
        
        if ($securityMode === 'development') {
            // Minimal security in development mode
            return $next($request);
        }

        // 2. Check if IP is whitelisted (bypass all security)
        if ($this->isWhitelistedIP($request->ip())) {
            return $next($request);
        }

        // 3. Check if user is admin - bypass all security for admin
        if (Auth::check() && Auth::user()->role === 'admin') {
            return $next($request);
        }

        // 4. Check if in development environment (bypass most security)
        if ($this->isDevelopmentEnvironment($request)) {
            // Only apply basic security in development
            $response = $next($request);
            $this->addResponseSecurityHeaders($response);
            return $response;
        }

        // Adjust security level based on mode
        $strictMode = ($securityMode === 'strict');

        // Full security checks for production
        // 5. Rate Limiting Protection
        $this->applyRateLimiting($request, $strictMode);

        // 6. IP Validation and Spoofing Protection (skip in normal mode for localhost)
        if ($strictMode || !$this->isLocalRequest($request)) {
            $this->validateIP($request);
        }

        // 7. Header Security Check
        $this->checkSuspiciousHeaders($request);

        // 8. Input Validation and Sanitization
        $this->validateAndSanitizeInput($request, $strictMode);

        // 9. File Upload Security
        $this->validateFileUploads($request);

        // 10. Session Security
        $this->enhanceSessionSecurity($request);

        // 11. CSRF Protection Enhancement
        $this->enhanceCSRFProtection($request);

        // 12. Clickjacking Protection
        $this->addSecurityHeaders($request);

        // 9. DNS and Proxy Security
        $this->validateProxyHeaders($request);

        // 10. Port Scanning Detection
        $this->detectPortScanning($request);

        $response = $next($request);

        // Add security headers to response
        $this->addResponseSecurityHeaders($response);

        return $response;
    }

    protected function applyRateLimiting(Request $request, bool $strictMode = false)
    {
        $key = $this->getRateLimitKey($request);
        
        // Adjust rate limiting based on mode
        if ($strictMode) {
            $maxAttempts = Auth::check() ? 60 : 20;
        } else {
            $maxAttempts = Auth::check() ? 200 : 60;
        }
        
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $this->logSecurityEvent('rate_limit_exceeded', $request, 'high');
            abort(429, 'Terlalu banyak permintaan. Silakan coba lagi nanti.');
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    protected function validateIP(Request $request)
    {
        $clientIP = $request->ip();
        $realIP = $this->getRealClientIP($request);

        // Check for IP spoofing
        if ($clientIP !== $realIP && !in_array($clientIP, $this->trustedProxies)) {
            $this->logSecurityEvent('ip_spoofing_detected', $request, 'critical', [
                'client_ip' => $clientIP,
                'real_ip' => $realIP
            ]);
        }

        // Block known malicious IPs
        if ($this->isBlacklistedIP($clientIP)) {
            $this->logSecurityEvent('blacklisted_ip_access', $request, 'critical');
            abort(403, 'Akses ditolak dari IP ini.');
        }
    }

    protected function checkSuspiciousHeaders(Request $request)
    {
        foreach ($this->suspiciousHeaders as $header) {
            if ($request->hasHeader($header)) {
                $this->logSecurityEvent('suspicious_header_detected', $request, 'medium', [
                    'header' => $header,
                    'value' => $request->header($header)
                ]);
            }
        }

        // Check for proxy bypass attempts
        $xForwardedFor = $request->header('X-Forwarded-For');
        if ($xForwardedFor && str_contains($xForwardedFor, '127.0.0.1')) {
            $this->logSecurityEvent('proxy_bypass_attempt', $request, 'high');
        }
    }

    protected function validateAndSanitizeInput(Request $request, bool $strictMode = false)
    {
        $allInput = $request->all();
        
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                // Check for dangerous patterns
                foreach ($this->dangerousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->logSecurityEvent('malicious_input_detected', $request, 'critical', [
                            'field' => $key,
                            'pattern' => $pattern,
                            'value' => substr($value, 0, 100) // Log first 100 chars only
                        ]);
                        
                        if ($strictMode) {
                            abort(403, 'Input berbahaya terdeteksi.');
                        } else {
                            // Log but don't block in normal mode for development
                            continue;
                        }
                    }
                }

                // Additional XSS protection
                if ($this->containsXSS($value)) {
                    $this->logSecurityEvent('xss_attempt_detected', $request, 'critical', [
                        'field' => $key,
                        'value' => substr($value, 0, 100)
                    ]);
                    
                    abort(403, 'Potensi serangan XSS terdeteksi.');
                }

                // Path traversal check
                if ($this->containsPathTraversal($value)) {
                    $this->logSecurityEvent('path_traversal_attempt', $request, 'critical', [
                        'field' => $key,
                        'value' => substr($value, 0, 100)
                    ]);
                    
                    abort(403, 'Percobaan path traversal terdeteksi.');
                }
            }
        }
    }

    protected function validateFileUploads(Request $request)
    {
        if ($request->hasFile('file') || $request->hasFile('upload') || $request->hasFile('image')) {
            foreach ($request->allFiles() as $key => $file) {
                if (is_array($file)) {
                    foreach ($file as $singleFile) {
                        $this->validateSingleFile($singleFile, $request);
                    }
                } else {
                    $this->validateSingleFile($file, $request);
                }
            }
        }
    }

    protected function validateSingleFile($file, Request $request)
    {
        if (!$file->isValid()) {
            $this->logSecurityEvent('invalid_file_upload', $request, 'medium');
            abort(400, 'File upload tidak valid.');
        }

        $filename = $file->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        
        // Check for dangerous file types
        $dangerousExtensions = [
            'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'jspx',
            'exe', 'com', 'bat', 'cmd', 'scr', 'pif', 'vbs', 'js', 'jar',
            'sh', 'pl', 'py', 'rb', 'go', 'bin', 'run'
        ];

        if (in_array($extension, $dangerousExtensions)) {
            $this->logSecurityEvent('dangerous_file_upload', $request, 'critical', [
                'filename' => $filename,
                'extension' => $extension
            ]);
            
            abort(403, 'Jenis file tidak diperbolehkan.');
        }

        // Check file content for web shells
        $content = file_get_contents($file->getPathname());
        if ($this->containsWebShell($content)) {
            $this->logSecurityEvent('webshell_upload_attempt', $request, 'critical', [
                'filename' => $filename
            ]);
            
            abort(403, 'File mengandung kode berbahaya.');
        }
    }

    protected function enhanceSessionSecurity(Request $request)
    {
        // Session fixation protection
        if (!$request->session()->has('session_verified')) {
            $request->session()->regenerate();
            $request->session()->put('session_verified', true);
        }

        // Session hijacking protection
        if (Auth::check()) {
            $currentFingerprint = $this->generateFingerprint($request);
            $storedFingerprint = $request->session()->get('user_fingerprint');

            if ($storedFingerprint && $storedFingerprint !== $currentFingerprint) {
                $this->logSecurityEvent('session_hijack_attempt', $request, 'critical', [
                    'user_id' => Auth::id(),
                    'stored_fingerprint' => $storedFingerprint,
                    'current_fingerprint' => $currentFingerprint
                ]);

                Auth::logout();
                $request->session()->invalidate();
                abort(403, 'Sesi tidak valid. Silakan login kembali.');
            }

            if (!$storedFingerprint) {
                $request->session()->put('user_fingerprint', $currentFingerprint);
            }
        }
    }

    protected function enhanceCSRFProtection(Request $request)
    {
        // Additional CSRF validation for sensitive operations
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $token = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');
            
            if (!$token) {
                $this->logSecurityEvent('missing_csrf_token', $request, 'high');
                abort(419, 'Token CSRF hilang.');
            }
        }
    }

    protected function addSecurityHeaders(Request $request)
    {
        // Headers will be added in response
    }

    protected function validateProxyHeaders(Request $request)
    {
        $proxyHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED_HOST',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_CLIENT_IP'
        ];

        foreach ($proxyHeaders as $header) {
            $value = $request->server($header);
            if ($value && $this->containsSuspiciousProxyValue($value)) {
                $this->logSecurityEvent('suspicious_proxy_header', $request, 'medium', [
                    'header' => $header,
                    'value' => $value
                ]);
            }
        }
    }

    protected function detectPortScanning(Request $request)
    {
        $userAgent = $request->userAgent();
        $ip = $request->ip();

        // Detect common scanning tools
        $scanningPatterns = [
            '/nmap/i', '/masscan/i', '/zmap/i', '/rustscan/i',
            '/nikto/i', '/sqlmap/i', '/burp/i', '/zap/i'
        ];

        foreach ($scanningPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $this->logSecurityEvent('scanning_tool_detected', $request, 'critical', [
                    'user_agent' => $userAgent,
                    'tool_pattern' => $pattern
                ]);
                
                abort(403, 'Aktivitas scanning terdeteksi.');
            }
        }
    }

    protected function addResponseSecurityHeaders($response)
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: *; connect-src 'self' ws: wss:",
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'camera=(), microphone=(), geolocation=()',
            'X-Permitted-Cross-Domain-Policies' => 'none',
            'X-Download-Options' => 'noopen'
        ];

        foreach ($headers as $key => $value) {
            $response->headers->set($key, $value);
        }

        return $response;
    }

    /**
     * Check if IP is whitelisted
     */
    protected function isWhitelistedIP($ip): bool
    {
        // Get whitelist from cache
        $cacheWhitelist = Cache::get('security_ip_whitelist', []);
        
        // Combine static and cached whitelist
        $allWhitelisted = array_merge($this->whitelistedIPs, $cacheWhitelist);

        // Check direct IP matches
        if (in_array($ip, $allWhitelisted)) {
            return true;
        }

        // Check if IP is in development ranges
        foreach ($this->developmentRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if running in development environment
     */
    protected function isDevelopmentEnvironment(Request $request): bool
    {
        // Check environment
        if (app()->environment('local', 'development', 'testing')) {
            return true;
        }

        // Check if accessing from localhost
        $ip = $request->ip();
        if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
            return true;
        }

        // Check if URL contains localhost or local development ports
        $host = $request->getHost();
        if (str_contains($host, 'localhost') || 
            str_contains($host, '127.0.0.1') || 
            str_contains($host, '.local') ||
            in_array($request->getPort(), [8000, 8080, 3000, 5000])) {
            return true;
        }

        return false;
    }

    /**
     * Check if request is from local/development environment
     */
    protected function isLocalRequest(Request $request): bool
    {
        $ip = $request->ip();
        $host = $request->getHost();
        
        return in_array($ip, ['127.0.0.1', '::1']) || 
               str_contains($host, 'localhost') ||
               str_contains($host, '127.0.0.1');
    }

    // Helper methods
    protected function getRateLimitKey(Request $request)
    {
        return 'security_rate_limit:' . $request->ip() . ':' . $request->getPathInfo();
    }

    protected function getRealClientIP(Request $request)
    {
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($headers as $header) {
            $ip = $request->server($header);
            if ($ip && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }

        return $request->ip();
    }

    protected function isBlacklistedIP($ip)
    {
        // Check against known malicious IP ranges or database
        $blacklistedRanges = [
            '0.0.0.0/8',
            '*********/8',
            '***********/16',
            '*********/4'
        ];

        foreach ($blacklistedRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    protected function ipInRange($ip, $range)
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    protected function containsXSS($value)
    {
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/on\w+\s*=/i',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>/i',
            '/expression\s*\(/i',
            '/@import/i',
            '/binding\s*:/i'
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    protected function containsPathTraversal($value)
    {
        $patterns = [
            '/(\.\.[\/\\\\]){2,}/',
            '/\.\.[\/\\\\].*?\.(conf|ini|log|sql|bak|passwd|shadow)/',
            '/(\/etc\/|\/windows\/|\/system32\/|\.\.\/)/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    protected function containsWebShell($content)
    {
        $shellPatterns = [
            '/(\$_GET|\$_POST|\$_REQUEST)\s*\[.*?\]\s*\)/i',
            '/(eval|exec|system|passthru|shell_exec|base64_decode)\s*\(/i',
            '/(c99|c100|r57|p0wny|wso|b374k)/i',
            '/php\s*\/\*.*?\*\/\s*\?>/is'
        ];

        foreach ($shellPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    protected function containsSuspiciousProxyValue($value)
    {
        return preg_match('/[<>"\'\(\);\\\\]/', $value);
    }

    protected function generateFingerprint(Request $request)
    {
        $data = [
            $request->userAgent(),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding'),
            $request->ip()
        ];

        return hash('sha256', implode('|', $data));
    }

    protected function logSecurityEvent($event, Request $request, $severity = 'medium', $additional_data = [])
    {
        $data = array_merge([
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => Auth::id()
        ], $additional_data);

        SecurityLog::logAction(
            $event,
            Auth::id(),
            $severity,
            $data,
            session()->getId()
        );

        Log::warning("Security Event: {$event}", $data);
    }
}
