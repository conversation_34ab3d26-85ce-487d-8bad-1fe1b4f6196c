<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;
use App\Services\NotificationService;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'email_verified_at',
        'password',
        'phone',
        'department',
        'position',
        'avatar',
        'role',
        'is_active',
        'theme_preference',
        'locked_until',
        'failed_login_attempts',
        'force_logout',
        'last_login_ip',
        'last_login_at',
        'session_id',
        'last_ip',
        'security_notes',
        'user_agent',
        'email_verification_token',
        'email_verification_expires_at',
        'password_reset_token',
        'password_reset_expires_at',
        // Security fields
        'last_password_change',
        'login_history',
        'failed_login_history',
        'account_status',
        'account_locked_at',
        'account_locked_reason',
        'security_score',
        'security_flags',
        'terms_accepted_at',
        'terms_version',
        'two_factor_enabled',
        'two_factor_secret',
        'trusted_devices',
        'concurrent_sessions_limit',
        'password_expires_at',
        'force_password_change',
        'security_questions',
        'last_security_check',
        'risk_level'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'password_reset_token',
        'email_verification_token',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'account_locked',
        'login_attempts',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'locked_until' => 'datetime',
            'last_login_at' => 'datetime',
            'password_reset_expires_at' => 'datetime',
            'email_verification_expires_at' => 'datetime',
            'is_active' => 'boolean',
            'force_logout' => 'boolean',
        ];
    }

    /**
     * Check if user account is locked
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Check if user has verified email
     */
    public function hasVerifiedEmail(): bool
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Accessor for account_locked attribute (compatibility)
     */
    public function getAccountLockedAttribute(): bool
    {
        return $this->isLocked();
    }

    /**
     * Mutator for account_locked attribute (compatibility)
     */
    public function setAccountLockedAttribute($value): void
    {
        if ($value) {
            $this->lockAccount();
        } else {
            $this->unlockAccount();
        }
    }

    /**
     * Lock the user account
     */
    public function lockAccount(int $minutes = 30): void
    {
        $lockUntil = Carbon::now()->addMinutes($minutes);
        
        \Log::info('lockAccount called', [
            'user_id' => $this->id,
            'current_locked_until' => $this->locked_until,
            'new_locked_until' => $lockUntil,
            'minutes' => $minutes
        ]);

        $result = $this->update([
            'locked_until' => $lockUntil,
            'force_logout' => true,
        ]);

        \Log::info('lockAccount update result', [
            'user_id' => $this->id,
            'update_result' => $result,
            'locked_until_after_update' => $this->fresh()->locked_until
        ]);

        // Create notification for user locked
        try {
            if (class_exists('\App\Services\NotificationService')) {
                \App\Services\NotificationService::notifyUserLocked($this);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to create user locked notification', ['error' => $e->getMessage()]);
        }

        // Create notification for user locked
        NotificationService::notifyUserLocked($this, 'Gagal login berulang kali');
    }

    /**
     * Unlock user account
     */
    public function unlockAccount(string $reason = null): void
    {
        \Log::info('unlockAccount called', [
            'user_id' => $this->id,
            'current_locked_until' => $this->locked_until,
            'reason' => $reason
        ]);

        $result = $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
            'force_logout' => false,
            'account_status' => 'active',
            'account_locked_at' => null,
            'account_locked_reason' => null
        ]);

        \Log::info('unlockAccount update result', [
            'user_id' => $this->id,
            'update_result' => $result,
            'locked_until_after_update' => $this->fresh()->locked_until
        ]);

        SecurityLog::logAction(
            'account_unlocked',
            $this->id,
            'medium',
            [
                'reason' => $reason ?: 'Manual unlock',
                'unlocked_by' => auth()->id() ?: 'system'
            ]
        );

        // Create notification for user unlocked
        try {
            if (class_exists('\App\Services\NotificationService')) {
                \App\Services\NotificationService::notifyUserUnlocked($this);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to create user unlocked notification', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Increment failed login attempts
     */
    public function incrementFailedAttempts(): void
    {
        $attempts = $this->failed_login_attempts + 1;
        
        $this->update([
            'failed_login_attempts' => $attempts,
        ]);

        // Lock account after 3 failed attempts
        if ($attempts >= 3) {
            $this->lockAccount();
        }
    }

    /**
     * Reset failed login attempts
     */
    public function resetFailedAttempts(): void
    {
        $this->update([
            'failed_login_attempts' => 0,
        ]);
    }

    /**
     * Update last login information
     */
    public function updateLastLogin(string $ipAddress, string $sessionId): void
    {
        $this->update([
            'last_login_ip' => $ipAddress,
            'last_login_at' => Carbon::now(),
            'session_id' => $sessionId,
            'force_logout' => false,
        ]);
    }

    /**
     * Security-related methods
     */
    
    /**
     * Check if user account is in good security standing
     */
    public function hasGoodSecurityStanding(): bool
    {
        return $this->security_score >= 70 && 
               $this->account_status === 'active' && 
               $this->risk_level !== 'critical';
    }

    /**
     * Update security score based on activity
     */
    public function updateSecurityScore(int $change, string $reason = null): void
    {
        $newScore = max(0, min(100, $this->security_score + $change));
        
        $this->update([
            'security_score' => $newScore,
            'last_security_check' => now()
        ]);

        // Log security score change
        if ($change !== 0) {
            SecurityLog::logAction(
                'security_score_changed',
                $this->id,
                $change < 0 ? 'medium' : 'low',
                [
                    'old_score' => $this->security_score,
                    'new_score' => $newScore,
                    'change' => $change,
                    'reason' => $reason
                ]
            );
        }

        // Auto-lock account if score too low
        if ($newScore <= 20 && $this->account_status === 'active') {
            $this->lockAccount(60 * 24); // Lock for 24 hours (in minutes)
        }
    }

    /**
     * Record login attempt
     */
    public function recordLoginAttempt(bool $successful, array $details = []): void
    {
        $loginHistory = $this->login_history ? json_decode($this->login_history, true) : [];
        $failedHistory = $this->failed_login_history ? json_decode($this->failed_login_history, true) : [];

        $attempt = array_merge([
            'timestamp' => now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'successful' => $successful
        ], $details);

        if ($successful) {
            // Add to login history (keep last 10)
            array_unshift($loginHistory, $attempt);
            $loginHistory = array_slice($loginHistory, 0, 10);
            
            $this->update([
                'login_history' => json_encode($loginHistory),
                'failed_login_attempts' => 0,
                'last_login_at' => now(),
                'last_login_ip' => request()->ip()
            ]);

            // Improve security score for successful login
            $this->updateSecurityScore(1, 'Successful login');
        } else {
            // Add to failed history (keep last 20)
            array_unshift($failedHistory, $attempt);
            $failedHistory = array_slice($failedHistory, 0, 20);
            
            $failedAttempts = $this->failed_login_attempts + 1;
            
            $this->update([
                'failed_login_history' => json_encode($failedHistory),
                'failed_login_attempts' => $failedAttempts
            ]);

            // Decrease security score for failed login
            $this->updateSecurityScore(-5, 'Failed login attempt');

            // Lock account after too many failed attempts
            if ($failedAttempts >= 5) {
                $this->lockAccount('Too many failed login attempts: ' . $failedAttempts, 2);
            }
        }
    }

    /**
     * Add security flag
     */
    public function addSecurityFlag(string $flag, array $data = []): void
    {
        $flags = $this->security_flags ? json_decode($this->security_flags, true) : [];
        
        $flags[] = [
            'flag' => $flag,
            'timestamp' => now()->toISOString(),
            'data' => $data
        ];

        // Keep only last 50 flags
        $flags = array_slice($flags, -50);

        $this->update(['security_flags' => json_encode($flags)]);

        // Decrease security score based on flag severity
        $scoreDecrease = $this->getSecurityFlagScore($flag);
        $this->updateSecurityScore(-$scoreDecrease, 'Security flag: ' . $flag);
    }

    /**
     * Get security flag score impact
     */
    protected function getSecurityFlagScore(string $flag): int
    {
        $scores = [
            'suspicious_login' => 10,
            'multiple_failed_attempts' => 15,
            'unusual_location' => 8,
            'suspicious_user_agent' => 5,
            'session_hijack_attempt' => 25,
            'malicious_input' => 20,
            'ddos_participation' => 30,
            'scanning_activity' => 15
        ];

        return $scores[$flag] ?? 5;
    }

    /**
     * Check if user needs password change
     */
    public function needsPasswordChange(): bool
    {
        if ($this->force_password_change) {
            return true;
        }

        if ($this->password_expires_at && $this->password_expires_at->isPast()) {
            return true;
        }

        // Check if password is older than 90 days
        if ($this->last_password_change && 
            $this->last_password_change->diffInDays(now()) > 90) {
            return true;
        }

        return false;
    }

    /**
     * Update risk level based on activities
     */
    public function updateRiskLevel(): void
    {
        $riskScore = 0;

        // Check security score
        if ($this->security_score < 30) $riskScore += 40;
        elseif ($this->security_score < 60) $riskScore += 20;
        elseif ($this->security_score < 80) $riskScore += 10;

        // Check failed login attempts
        $riskScore += min($this->failed_login_attempts * 5, 25);

        // Check security flags
        $flags = $this->security_flags ? json_decode($this->security_flags, true) : [];
        $recentFlags = array_filter($flags, function($flag) {
            $flagTime = Carbon::parse($flag['timestamp']);
            return $flagTime->diffInDays(now()) <= 7; // Last 7 days
        });
        $riskScore += count($recentFlags) * 3;

        // Check password age
        if ($this->needsPasswordChange()) $riskScore += 15;

        // Determine risk level
        if ($riskScore >= 70) {
            $newRiskLevel = 'critical';
        } elseif ($riskScore >= 40) {
            $newRiskLevel = 'high';
        } elseif ($riskScore >= 20) {
            $newRiskLevel = 'medium';
        } else {
            $newRiskLevel = 'low';
        }

        if ($this->risk_level !== $newRiskLevel) {
            $this->update(['risk_level' => $newRiskLevel]);
            
            SecurityLog::logAction(
                'risk_level_changed',
                $this->id,
                $newRiskLevel === 'critical' ? 'critical' : 'medium',
                [
                    'old_level' => $this->risk_level,
                    'new_level' => $newRiskLevel,
                    'risk_score' => $riskScore
                ]
            );

            // Auto-actions based on risk level
            if ($newRiskLevel === 'critical') {
                $this->lockAccount('Critical risk level detected', 48);
            } elseif ($newRiskLevel === 'high') {
                $this->update(['force_password_change' => true]);
            }
        }
    }

    /**
     * Check if user is admin (bypass security)
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Get concurrent sessions limit (admin = unlimited)
     */
    public function getConcurrentSessionsLimit(): int
    {
        return $this->isAdmin() ? 999 : $this->concurrent_sessions_limit;
    }

    /**
     * Eloquent Relationships
     */
    
    /**
     * Bookings relationship
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Approved bookings relationship
     */
    public function approvedBookings(): HasMany
    {
        return $this->hasMany(Booking::class)->where('status', 'approved');
    }

    /**
     * Pending bookings relationship
     */
    public function pendingBookings(): HasMany
    {
        return $this->hasMany(Booking::class)->where('status', 'pending');
    }

    /**
     * Notifications relationship
     */
    public function notifications()
    {
        // Relasi benar: hasMany ke user_id
        return $this->hasMany(\App\Models\Notification::class, 'user_id');
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadNotificationsCountAttribute(): int
    {
        return $this->notifications()->unread()->count();
    }

    /**
     * Accessor untuk login_attempts agar tidak error jika dipanggil
     */
    public function getLoginAttemptsAttribute()
    {
        // Gunakan failed_login_attempts jika ada, default 0
        return $this->failed_login_attempts ?? 0;
    }
}
