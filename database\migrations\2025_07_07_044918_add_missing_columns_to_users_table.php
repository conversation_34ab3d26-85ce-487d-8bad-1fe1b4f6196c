<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Kolom account_locked dan login_attempts sudah ada, tidak perlu ditambah lagi
            
            // Kolom yang belum ada dan perlu ditambahkan:
            if (!Schema::hasColumn('users', 'last_ip')) {
                $table->string('last_ip')->nullable()->after('session_id');
            }
            if (!Schema::hasColumn('users', 'security_notes')) {
                $table->text('security_notes')->nullable()->after('last_ip');
            }
            if (!Schema::hasColumn('users', 'user_agent')) {
                $table->text('user_agent')->nullable()->after('security_notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'account_locked',
                'login_attempts', 
                'locked_until',
                'last_ip',
                'last_login_at',
                'failed_login_attempts',
                'last_failed_login',
                'security_notes',
                'session_id',
                'user_agent'
            ]);
        });
    }
};
