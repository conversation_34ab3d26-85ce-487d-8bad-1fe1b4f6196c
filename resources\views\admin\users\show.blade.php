@extends('layouts.dashboard')

@section('title', 'Detail Pengguna')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Kelola Pengguna</a></li>
                    <li class="breadcrumb-item active">Detail Pengguna</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Detail Pengguna</h1>
        </div>
        <div>
            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-warning">
                <i class="bi bi-pencil me-1"></i> Edit
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Profile -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    @if($user->avatar)
                        <img src="{{ Storage::url($user->avatar) }}" alt="Avatar" 
                             class="rounded-circle mb-3" width="150" height="150">
                    @else
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                             style="width: 150px; height: 150px; color: white; font-size: 3rem;">
                            {{ strtoupper(substr($user->name, 0, 1)) }}
                        </div>
                    @endif
                    
                    <h4 class="card-title">{{ $user->name }}</h4>
                    <p class="text-muted">{{ $user->email }}</p>
                    
                    <div class="mb-3">
                        <span class="badge {{ $user->role === 'admin' ? 'bg-danger' : 'bg-primary' }} fs-6">
                            {{ ucfirst($user->role) }}
                        </span>
                        
                        @if($user->account_locked)
                            <span class="badge bg-danger fs-6">Terkunci</span>
                        @else
                            <span class="badge bg-success fs-6">Aktif</span>
                        @endif
                    </div>
                    
                    @if($user->email_verified_at)
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-1"></i>
                            Email terverifikasi pada {{ $user->email_verified_at->format('d M Y, H:i') }}
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            Email belum terverifikasi
                        </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="d-grid gap-2">
                        @if($user->account_locked)
                            <button class="btn btn-success" onclick="unlockUser({{ $user->id }})">
                                <i class="bi bi-unlock me-1"></i> Buka Kunci Akun
                            </button>
                        @else
                            <button class="btn btn-danger" onclick="lockUser({{ $user->id }})">
                                <i class="bi bi-lock me-1"></i> Kunci Akun
                            </button>
                        @endif
                        
                        @if(!$user->email_verified_at)
                            <button class="btn btn-warning" onclick="resendVerification({{ $user->id }})">
                                <i class="bi bi-envelope me-1"></i> Kirim Ulang Verifikasi
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- User Details -->
        <div class="col-xl-8 col-lg-7">
            <!-- Personal Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Personal</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nama Lengkap</label>
                                <p class="form-control-static">{{ $user->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="form-control-static">{{ $user->email }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nomor Telepon</label>
                                <p class="form-control-static">{{ $user->phone ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Departemen</label>
                                <p class="form-control-static">{{ $user->department ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Posisi</label>
                                <p class="form-control-static">{{ $user->position ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tanggal Bergabung</label>
                                <p class="form-control-static">{{ $user->created_at->format('d M Y, H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Keamanan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Login Terakhir</label>
                                <p class="form-control-static">
                                    {{ $user->last_login_at ? $user->last_login_at->format('d M Y, H:i') : 'Belum pernah login' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">IP Address Terakhir</label>
                                <p class="form-control-static">{{ $user->last_login_ip ?? '-' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Percobaan Login Gagal</label>
                                <p class="form-control-static">
                                    <span class="badge {{ $user->login_attempts > 3 ? 'bg-danger' : 'bg-success' }}">
                                        {{ $user->login_attempts }} kali
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Login Gagal Terakhir</label>
                                <p class="form-control-static">
                                    {{ $user->last_failed_login ? $user->last_failed_login->format('d M Y, H:i') : '-' }}
                                </p>
                            </div>
                        </div>
                        @if($user->security_notes)
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Catatan Keamanan</label>
                                <div class="alert alert-info">{{ $user->security_notes }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Pemesanan Terbaru</h6>
                    <a href="{{ route('admin.bookings.index', ['user_id' => $user->id]) }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @if(isset($recentBookings) && count($recentBookings) > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Ruang</th>
                                        <th>Tanggal</th>
                                        <th>Waktu</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentBookings as $booking)
                                    <tr>
                                        <td>{{ $booking->meetingRoom->name }}</td>
                                        <td>{{ \Carbon\Carbon::parse($booking->date)->format('d M Y') }}</td>
                                        <td>{{ $booking->start_time }} - {{ $booking->end_time }}</td>
                                        <td>
                                            @if($booking->status === 'approved')
                                                <span class="badge bg-success">Disetujui</span>
                                            @elseif($booking->status === 'pending')
                                                <span class="badge bg-warning">Menunggu</span>
                                            @elseif($booking->status === 'rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($booking->status) }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">Belum ada pemesanan</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function lockUser(userId) {
    Swal.fire({
        title: 'Kunci Akun Pengguna?',
        text: 'Pengguna tidak akan bisa login setelah akun dikunci.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Kunci!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/users/${userId}/lock`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Akun pengguna telah dikunci.', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function unlockUser(userId) {
    Swal.fire({
        title: 'Buka Kunci Akun Pengguna?',
        text: 'Pengguna akan bisa login kembali setelah akun dibuka.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Buka Kunci!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/users/${userId}/unlock`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Akun pengguna telah dibuka.', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function resendVerification(userId) {
    Swal.fire({
        title: 'Kirim Ulang Email Verifikasi?',
        text: 'Email verifikasi akan dikirim ke alamat email pengguna.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Kirim!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/users/${userId}/resend-verification`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Email verifikasi telah dikirim.', 'success');
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}
</script>
@endpush
@endsection
