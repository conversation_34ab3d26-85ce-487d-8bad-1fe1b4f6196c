<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Http\Controllers\Auth\AuthController;
use Illuminate\Http\Request;
use Carbon\Carbon;

echo "=== TEST VERIFIKASI EMAIL ===\n";

// Ambil user yang belum diverifikasi
$user = User::whereNull('email_verified_at')->first();

if (!$user) {
    echo "Tidak ada user yang belum diverifikasi.\n";
    exit;
}

echo "User: {$user->email}\n";
echo "Token: {$user->email_verification_token}\n";
echo "Expires: {$user->email_verification_expires_at}\n";
echo "Token expired: " . ($user->email_verification_expires_at->isPast() ? 'YA' : 'TIDAK') . "\n";

// Simulasi verifikasi
$controller = new AuthController();
$request = new Request();

echo "\n=== SIMULASI VERIFIKASI ===\n";

// Test pencarian user dengan token
$foundUser = User::where('email_verification_token', $user->email_verification_token)
                 ->where('email_verification_expires_at', '>', Carbon::now())
                 ->first();

echo "User ditemukan: " . ($foundUser ? 'YA' : 'TIDAK') . "\n";

if ($foundUser) {
    echo "User ID: {$foundUser->id}\n";
    echo "Email: {$foundUser->email}\n";
    echo "Email verified sebelum: " . ($foundUser->email_verified_at ? 'YA' : 'TIDAK') . "\n";
    
    // Update email verified
    $foundUser->update([
        'email_verified_at' => Carbon::now(),
        'email_verification_token' => null,
        'email_verification_expires_at' => null,
    ]);
    
    echo "Email verified sesudah: " . ($foundUser->fresh()->email_verified_at ? 'YA' : 'TIDAK') . "\n";
    echo "Verifikasi berhasil!\n";
} else {
    echo "Verifikasi gagal - user tidak ditemukan dengan token dan belum expired.\n";
}

echo "\n=== SELESAI ===\n";
