@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON> Rapat - Sistem Pemesanan Ruang Rapat')
@section('page-title', '<PERSON>uang Rapat')

@push('styles')
<style>
    .rooms-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
    }

    .rooms-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .rooms-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .view-toggle {
        display: flex;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .view-btn {
        padding: 0.5rem 1rem;
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s;
    }

    .view-btn.active {
        background: var(--primary-color);
        color: white;
    }

    .rooms-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
    }

    .room-card {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 4px 20px var(--shadow);
        transition: all 0.3s;
    }

    .room-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px var(--shadow);
    }

    .room-image {
        height: 200px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: relative;
    }

    .room-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-available {
        background: rgba(34, 197, 94, 0.9);
        color: white;
    }

    .status-occupied {
        background: rgba(239, 68, 68, 0.9);
        color: white;
    }

    .room-content {
        padding: 1.5rem;
    }

    .room-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .room-location {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .room-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .info-icon {
        color: var(--primary-color);
    }

    .room-facilities {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .facility-tag {
        background: rgba(37, 99, 235, 0.1);
        color: var(--primary-color);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        border: 1px solid rgba(37, 99, 235, 0.2);
    }

    .room-current {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .current-title {
        font-size: 0.8rem;
        font-weight: 600;
        color: #ef4444;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
    }

    .current-info {
        font-size: 0.9rem;
        color: var(--text-primary);
    }

    .room-next {
        background: rgba(251, 191, 36, 0.1);
        border: 1px solid rgba(251, 191, 36, 0.2);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .next-title {
        font-size: 0.8rem;
        font-weight: 600;
        color: #f59e0b;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
    }

    .room-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        flex: 1;
        padding: 0.75rem;
        border: none;
        border-radius: 0.5rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: var(--primary-color);
        color: white;
        text-decoration: none;
    }

    .rooms-list {
        display: none;
    }

    .rooms-list.active {
        display: block;
    }

    .room-list-item {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: 1.5rem;
        align-items: center;
    }

    .room-list-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .room-list-info {
        display: grid;
        gap: 0.5rem;
    }

    .room-list-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    @media (max-width: 768px) {
        .rooms-grid {
            grid-template-columns: 1fr;
        }

        .rooms-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .room-list-item {
            grid-template-columns: 1fr;
            text-align: center;
        }
    }
</style>
@endpush

@section('content')
<div class="rooms-container">
    <div class="rooms-header">
        <h1 class="rooms-title">Ruang Rapat</h1>
        <div class="view-toggle">
            <button class="view-btn active" onclick="toggleView('grid')" id="grid-btn">
                <i class="bi bi-grid-3x3-gap"></i>
                Grid
            </button>
            <button class="view-btn" onclick="toggleView('list')" id="list-btn">
                <i class="bi bi-list-ul"></i>
                List
            </button>
        </div>
    </div>

    <!-- Grid View -->
    <div class="rooms-grid active" id="grid-view">
        @forelse($rooms as $room)
        <div class="room-card">
            <div class="room-image">
                @if($room->image)
                    <img src="{{ Storage::url($room->image) }}" alt="{{ $room->name }}" style="width: 100%; height: 100%; object-fit: cover;">
                @else
                    <i class="bi bi-door-open"></i>
                @endif
                
                <div class="room-status {{ $room->current_booking ? 'status-occupied' : 'status-available' }}">
                    @if($room->current_booking)
                        <i class="bi bi-lock"></i> Terpakai
                    @else
                        <i class="bi bi-check-circle"></i> Tersedia
                    @endif
                </div>
            </div>

            <div class="room-content">
                <h3 class="room-name">{{ $room->name }}</h3>
                <div class="room-location">
                    <i class="bi bi-geo-alt"></i>
                    {{ $room->location }}
                </div>

                <div class="room-info">
                    <div class="info-item">
                        <i class="bi bi-people info-icon"></i>
                        <span>{{ $room->capacity }} orang</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-calendar-event info-icon"></i>
                        <span>{{ $room->today_schedule->count() }} jadwal hari ini</span>
                    </div>
                </div>

                @if($room->roomFacilities && $room->roomFacilities->count() > 0)
                <div class="room-facilities">
                    @foreach($room->roomFacilities as $facility)
                    <span class="facility-tag">{{ $facility->name }}</span>
                    @endforeach
                </div>
                @endif

                @if($room->current_booking)
                <div class="room-current">
                    <div class="current-title">Sedang Digunakan</div>
                    <div class="current-info">
                        {{ $room->current_booking->title }}<br>
                        <small>{{ $room->current_booking->start_time->format('H:i') }} - {{ $room->current_booking->end_time->format('H:i') }}</small>
                    </div>
                </div>
                @elseif($room->next_booking)
                <div class="room-next">
                    <div class="next-title">Selanjutnya</div>
                    <div class="current-info">
                        {{ $room->next_booking->title }}<br>
                        <small>{{ $room->next_booking->start_time->format('d/m H:i') }} - {{ $room->next_booking->end_time->format('H:i') }}</small>
                    </div>
                </div>
                @endif

                <div class="room-actions">
                    <a href="{{ route('rooms.show', $room) }}" class="action-btn btn-secondary">
                        <i class="bi bi-eye"></i>
                        Detail
                    </a>
                    <a href="{{ route('bookings.create', ['room' => $room->id]) }}" class="action-btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Pesan
                    </a>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-full text-center py-8">
            <div class="text-4xl text-gray-400 mb-4">
                <i class="bi bi-door-closed"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">Tidak Ada Ruang Rapat</h3>
            <p class="text-gray-500">Belum ada ruang rapat yang tersedia.</p>
        </div>
        @endforelse
    </div>

    <!-- List View -->
    <div class="rooms-list" id="list-view">
        @foreach($rooms as $room)
        <div class="room-list-item">
            <div class="room-list-icon">
                <i class="bi bi-door-open"></i>
            </div>
            
            <div class="room-list-info">
                <div>
                    <h3 class="room-name">{{ $room->name }}</h3>
                    <div class="room-location">
                        <i class="bi bi-geo-alt"></i>
                        {{ $room->location }}
                    </div>
                </div>
                
                <div class="room-info">
                    <div class="info-item">
                        <i class="bi bi-people info-icon"></i>
                        <span>{{ $room->capacity }} orang</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-calendar-event info-icon"></i>
                        <span>{{ $room->today_schedule->count() }} jadwal hari ini</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-clock info-icon"></i>
                        <span class="{{ $room->current_booking ? 'text-red-500' : 'text-green-500' }}">
                            {{ $room->current_booking ? 'Terpakai' : 'Tersedia' }}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="room-list-actions">
                <a href="{{ route('rooms.show', $room) }}" class="action-btn btn-secondary">
                    <i class="bi bi-eye"></i>
                    Detail
                </a>
                <a href="{{ route('bookings.create', ['room' => $room->id]) }}" class="action-btn btn-primary">
                    <i class="bi bi-plus-circle"></i>
                    Pesan
                </a>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleView(viewType) {
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const gridBtn = document.getElementById('grid-btn');
    const listBtn = document.getElementById('list-btn');

    if (viewType === 'grid') {
        gridView.classList.add('active');
        listView.classList.remove('active');
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        listView.classList.add('active');
        gridView.classList.remove('active');
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }

    // Save preference
    localStorage.setItem('roomViewPreference', viewType);
}

// Load saved preference
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('roomViewPreference') || 'grid';
    toggleView(savedView);
});
</script>
@endpush
