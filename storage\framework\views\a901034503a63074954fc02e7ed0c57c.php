

<?php $__env->startSection('title', $room->name . ' - Detail Ruang Rapat'); ?>

<?php $__env->startSection('content'); ?>
<div class="room-detail-container">
    <!-- Header Section -->
    <div class="room-header">
        <div class="room-header-content">
            <div class="room-breadcrumb">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('dashboard')); ?>">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('rooms.index')); ?>">
                                <i class="bi bi-building"></i> Ruang Rapat
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <?php echo e($room->name); ?>

                        </li>
                    </ol>
                </nav>
            </div>
            
            <div class="room-title-section">
                <div class="room-title-main">
                    <h1 class="room-title"><?php echo e($room->name); ?></h1>
                    <div class="room-status">
                        <?php if($room->is_active): ?>
                            <span class="status-badge status-active">
                                <i class="bi bi-check-circle"></i> Aktif
                            </span>
                        <?php else: ?>
                            <span class="status-badge status-inactive">
                                <i class="bi bi-x-circle"></i> Tidak Aktif
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="room-actions">
                    <a href="<?php echo e(route('bookings.create', ['room' => $room->id])); ?>" class="btn btn-primary">
                        <i class="bi bi-calendar-plus"></i>
                        Pesan Ruangan
                    </a>
                    <button type="button" class="btn btn-outline-secondary" id="shareRoomBtn">
                        <i class="bi bi-share"></i>
                        Bagikan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Room Information Grid -->
    <div class="room-info-grid">
        <!-- Room Details Card -->
        <div class="room-info-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-info-circle"></i>
                    Informasi Ruangan
                </h3>
            </div>
            <div class="card-content">
                <div class="room-specs">
                    <div class="spec-item">
                        <div class="spec-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="spec-details">
                            <div class="spec-label">Kapasitas</div>
                            <div class="spec-value"><?php echo e($room->capacity); ?> orang</div>
                        </div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="spec-details">
                            <div class="spec-label">Lokasi</div>
                            <div class="spec-value"><?php echo e($room->location ?? 'Belum ditentukan'); ?></div>
                        </div>
                    </div>
                    
                    <?php if($room->description): ?>
                    <div class="spec-item">
                        <div class="spec-icon">
                            <i class="bi bi-file-text"></i>
                        </div>
                        <div class="spec-details">
                            <div class="spec-label">Deskripsi</div>
                            <div class="spec-value"><?php echo e($room->description); ?></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Facilities Card -->
        <div class="room-info-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-gear"></i>
                    Fasilitas
                </h3>
            </div>
            <div class="card-content">
                <?php if($room->roomFacilities && $room->roomFacilities->count() > 0): ?>
                    <div class="facilities-grid">
                        <?php $__currentLoopData = $room->roomFacilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="facility-item">
                            <div class="facility-icon">
                                <?php if($facility->icon): ?>
                                    <i class="bi <?php echo e($facility->icon); ?>"></i>
                                <?php else: ?>
                                    <i class="bi bi-check-circle"></i>
                                <?php endif; ?>
                            </div>
                            <div class="facility-name">
                                <?php echo e($facility->name); ?>

                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="bi bi-tools"></i>
                        <p>Belum ada informasi fasilitas</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Current Status Card -->
        <div class="room-info-card status-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-clock"></i>
                    Status Saat Ini
                </h3>
                <div class="status-time" id="currentTime"></div>
            </div>
            <div class="card-content">
                <?php
                    $currentBooking = $room->bookings()
                        ->where('status', 'approved')
                        ->where('start_time', '<=', now())
                        ->where('end_time', '>=', now())
                        ->with('user')
                        ->first();
                        
                    $nextBooking = $room->bookings()
                        ->where('status', 'approved')
                        ->where('start_time', '>', now())
                        ->orderBy('start_time')
                        ->with('user')
                        ->first();
                ?>
                
                <?php if($currentBooking): ?>
                    <div class="current-status status-occupied">
                        <div class="status-indicator">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-title">Sedang Digunakan</div>
                            <div class="status-details">
                                <div class="booking-title"><?php echo e($currentBooking->title); ?></div>
                                <div class="booking-user">oleh <?php echo e($currentBooking->user->name); ?></div>
                                <div class="booking-time">
                                    <?php echo e($currentBooking->start_time->format('H:i')); ?> - 
                                    <?php echo e($currentBooking->end_time->format('H:i')); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="current-status status-available">
                        <div class="status-indicator">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-title">Tersedia</div>
                            <div class="status-details">
                                Ruangan dapat digunakan sekarang
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if($nextBooking): ?>
                    <div class="next-booking">
                        <div class="next-booking-label">Booking Selanjutnya:</div>
                        <div class="next-booking-info">
                            <div class="next-booking-title"><?php echo e($nextBooking->title); ?></div>
                            <div class="next-booking-time">
                                <?php echo e($nextBooking->start_time->format('d/m/Y H:i')); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Schedule Section -->
    <div class="schedule-section">
        <!-- Today's Schedule -->
        <div class="schedule-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-calendar-day"></i>
                    Jadwal Hari Ini
                </h3>
                <div class="card-subtitle">
                    <?php echo e(Carbon\Carbon::now()->translatedFormat('l, d F Y')); ?>

                </div>
            </div>
            <div class="card-content">
                <?php if($todayBookings->count() > 0): ?>
                    <div class="schedule-timeline">
                        <?php $__currentLoopData = $todayBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="timeline-item status-<?php echo e($booking->status); ?>" data-booking-id="<?php echo e($booking->id); ?>">
                            <div class="timeline-time">
                                <div class="time-start"><?php echo e($booking->start_time->format('H:i')); ?></div>
                                <div class="time-end"><?php echo e($booking->end_time->format('H:i')); ?></div>
                            </div>
                            <div class="timeline-content">
                                <div class="booking-header">
                                    <h4 class="booking-title"><?php echo e($booking->title); ?></h4>
                                    <span class="status-badge status-<?php echo e($booking->status); ?>">
                                        <?php switch($booking->status):
                                            case ('approved'): ?>
                                                <i class="bi bi-check-circle"></i> Disetujui
                                                <?php break; ?>
                                            <?php case ('pending'): ?>
                                                <i class="bi bi-clock"></i> Menunggu
                                                <?php break; ?>
                                            <?php case ('rejected'): ?>
                                                <i class="bi bi-x-circle"></i> Ditolak
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </span>
                                </div>
                                <div class="booking-details">
                                    <div class="booking-user">
                                        <i class="bi bi-person"></i>
                                        <?php echo e($booking->user->name); ?>

                                    </div>
                                    <div class="booking-attendees">
                                        <i class="bi bi-people"></i>
                                        <?php echo e($booking->attendees_count); ?> peserta
                                    </div>
                                    <?php if($booking->description): ?>
                                    <div class="booking-description">
                                        <i class="bi bi-file-text"></i>
                                        <?php echo e(Str::limit($booking->description, 100)); ?>

                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="bi bi-calendar-x"></i>
                        <h4>Tidak Ada Jadwal Hari Ini</h4>
                        <p>Ruangan kosong sepanjang hari</p>
                        <a href="<?php echo e(route('bookings.create', ['room' => $room->id])); ?>" class="btn btn-primary">
                            Pesan Sekarang
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Upcoming Schedule -->
        <div class="schedule-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-calendar-event"></i>
                    Jadwal Mendatang
                </h3>
                <div class="card-subtitle">
                    7 hari ke depan
                </div>
            </div>
            <div class="card-content">
                <?php if($upcomingBookings->count() > 0): ?>
                    <div class="upcoming-list">
                        <?php $__currentLoopData = $upcomingBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="upcoming-item status-<?php echo e($booking->status); ?>">
                            <div class="upcoming-date">
                                <div class="date-day"><?php echo e($booking->start_time->format('d')); ?></div>
                                <div class="date-month"><?php echo e($booking->start_time->format('M')); ?></div>
                            </div>
                            <div class="upcoming-content">
                                <div class="upcoming-header">
                                    <h4 class="upcoming-title"><?php echo e($booking->title); ?></h4>
                                    <span class="status-badge status-<?php echo e($booking->status); ?>">
                                        <?php echo e($booking->status); ?>

                                    </span>
                                </div>
                                <div class="upcoming-details">
                                    <div class="upcoming-time">
                                        <i class="bi bi-clock"></i>
                                        <?php echo e($booking->start_time->format('H:i')); ?> - <?php echo e($booking->end_time->format('H:i')); ?>

                                    </div>
                                    <div class="upcoming-user">
                                        <i class="bi bi-person"></i>
                                        <?php echo e($booking->user->name); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="bi bi-calendar-plus"></i>
                        <h4>Belum Ada Jadwal</h4>
                        <p>Ruangan tersedia untuk semua hari mendatang</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">
                    <i class="bi bi-share"></i>
                    Bagikan Ruangan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="share-options">
                    <div class="share-link">
                        <label for="roomUrl" class="form-label">Link Ruangan:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="roomUrl" value="<?php echo e(url()->current()); ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="copyUrlBtn">
                                <i class="bi bi-clipboard"></i>
                                Copy
                            </button>
                        </div>
                    </div>
                    
                    <div class="share-social">
                        <p class="mb-2">Atau bagikan via:</p>
                        <div class="social-buttons">
                            <a href="mailto:?subject=Ruang Rapat <?php echo e($room->name); ?>&body=Lihat detail ruang rapat: <?php echo e(url()->current()); ?>" 
                               class="btn btn-outline-primary">
                                <i class="bi bi-envelope"></i>
                                Email
                            </a>
                            <a href="https://wa.me/?text=Ruang Rapat <?php echo e($room->name); ?> - <?php echo e(url()->current()); ?>" 
                               target="_blank" class="btn btn-outline-success">
                                <i class="bi bi-whatsapp"></i>
                                WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Room Detail Styles */
.room-detail-container {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.room-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
}

.room-breadcrumb .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.room-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.room-breadcrumb .breadcrumb-item.active {
    color: white;
}

.room-title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
}

.room-title-main {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.room-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.room-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.room-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.room-info-card {
    background: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all 0.3s ease;
}

.room-info-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--header-bg);
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.card-content {
    padding: 1.5rem;
}

.room-specs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.spec-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.spec-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.spec-details {
    flex: 1;
}

.spec-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.spec-value {
    color: var(--text-secondary);
    line-height: 1.5;
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.facility-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.facility-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.facility-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.facility-name {
    font-size: 0.85rem;
    font-weight: 500;
}

.status-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-time {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
}

.current-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
}

.status-available {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-occupied {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-indicator {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.status-available .status-indicator {
    background: #16a34a;
    color: white;
}

.status-occupied .status-indicator {
    background: #dc2626;
    color: white;
}

.status-info {
    flex: 1;
}

.status-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.status-available .status-title {
    color: #16a34a;
}

.status-occupied .status-title {
    color: #dc2626;
}

.status-details {
    color: var(--text-secondary);
}

.booking-title {
    font-weight: 600;
    color: var(--text-primary);
}

.booking-user, .booking-time {
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.next-booking {
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
}

.next-booking-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.next-booking-title {
    font-weight: 600;
    color: var(--text-primary);
}

.next-booking-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.schedule-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.schedule-card {
    background: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.schedule-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    border-left: 4px solid var(--primary-color);
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.timeline-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.timeline-item.status-pending {
    border-left-color: #f59e0b;
}

.timeline-item.status-rejected {
    border-left-color: #dc2626;
}

.timeline-time {
    text-align: center;
    min-width: 4rem;
}

.time-start {
    font-weight: 600;
    font-size: 1.1rem;
}

.time-end {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.timeline-content {
    flex: 1;
}

.booking-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.booking-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.booking-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.booking-details > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.upcoming-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.upcoming-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.upcoming-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.upcoming-date {
    text-align: center;
    min-width: 3rem;
}

.date-day {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.date-month {
    font-size: 0.8rem;
    text-transform: uppercase;
    color: var(--text-secondary);
}

.upcoming-content {
    flex: 1;
}

.upcoming-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.upcoming-title {
    font-weight: 600;
    margin: 0;
}

.upcoming-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.upcoming-details > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.social-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Status Badge Colors */
.status-badge.status-approved {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.status-rejected {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .room-detail-container {
        padding: 0.5rem;
    }
    
    .room-header {
        padding: 1.5rem;
    }
    
    .room-title {
        font-size: 2rem;
    }
    
    .room-info-grid {
        grid-template-columns: 1fr;
    }
    
    .schedule-section {
        grid-template-columns: 1fr;
    }
    
    .facilities-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .upcoming-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update current time
    function updateCurrentTime() {
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }
    
    // Update time every second
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Share functionality
    const shareBtn = document.getElementById('shareRoomBtn');
    const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
    const copyBtn = document.getElementById('copyUrlBtn');
    const urlInput = document.getElementById('roomUrl');
    
    shareBtn?.addEventListener('click', function() {
        shareModal.show();
    });
    
    copyBtn?.addEventListener('click', function() {
        urlInput.select();
        document.execCommand('copy');
        
        // Show toast notification
        if (window.Toast) {
            Toast.fire({
                icon: 'success',
                title: 'Link berhasil disalin!'
            });
        }
        
        // Change button text temporarily
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="bi bi-check"></i> Copied!';
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
        }, 2000);
    });
    
    // Auto-refresh room status every 30 seconds
    setInterval(function() {
        fetch(window.location.href, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Update current status
            const newStatus = doc.querySelector('.current-status');
            const currentStatus = document.querySelector('.current-status');
            if (newStatus && currentStatus) {
                currentStatus.innerHTML = newStatus.innerHTML;
            }
            
            // Update next booking
            const newNext = doc.querySelector('.next-booking');
            const currentNext = document.querySelector('.next-booking');
            if (newNext && currentNext) {
                currentNext.innerHTML = newNext.innerHTML;
            } else if (newNext && !currentNext) {
                // Add next booking if it didn't exist before
                const statusCard = document.querySelector('.status-card .card-content');
                statusCard?.appendChild(newNext);
            } else if (!newNext && currentNext) {
                // Remove next booking if it no longer exists
                currentNext.remove();
            }
        })
        .catch(error => {
            console.log('Failed to refresh room status:', error);
        });
    }, 30000);
    
    // Add animation to timeline items
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, index * 100);
    });
    
    // Add animation to upcoming items
    const upcomingItems = document.querySelectorAll('.upcoming-item');
    upcomingItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100 + 200);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Rapat\resources\views/meeting-rooms/show.blade.php ENDPATH**/ ?>