@extends('layouts.dashboard')

@section('title', 'Edit Profil')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('profile.show') }}">Profil</a></li>
                    <li class="breadcrumb-item active">Edit Profil</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Edit Profil</h1>
        </div>
        <div>
            <a href="{{ route('profile.show') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i> <PERSON><PERSON><PERSON>
            </a>
        </div>
    </div>

    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Profile Picture -->
            <div class="col-xl-4 col-lg-5">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Foto Profil</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            @if($user->avatar)
                                <img src="{{ Storage::url($user->avatar) }}" alt="Avatar" 
                                     class="rounded-circle mb-3" width="150" height="150" id="previewImage">
                            @else
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                     style="width: 150px; height: 150px; color: white; font-size: 3rem;" id="previewDiv">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="mb-3">
                            <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*" onchange="previewFile()">
                            @error('avatar')
                                <div class="text-danger small mt-1">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Format: JPG, JPEG, PNG. Maksimal: 2MB</small>
                        </div>
                        
                        @if($user->avatar)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remove_avatar" name="remove_avatar">
                                <label class="form-check-label" for="remove_avatar">
                                    Hapus foto profil
                                </label>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Form Fields -->
            <div class="col-xl-8 col-lg-7">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Informasi Dasar</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($user->email !== old('email', $user->email))
                                        <small class="text-warning">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            Mengubah email akan memerlukan verifikasi ulang
                                        </small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Nomor Telepon</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $user->phone) }}"
                                           placeholder="Contoh: 08123456789">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">Departemen</label>
                                    <input type="text" class="form-control @error('department') is-invalid @enderror" 
                                           id="department" name="department" value="{{ old('department', $user->department) }}"
                                           placeholder="Contoh: IT, HR, Finance">
                                    @error('department')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="position" class="form-label">Posisi</label>
                                    <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                           id="position" name="position" value="{{ old('position', $user->position) }}"
                                           placeholder="Contoh: Manager, Staff, Supervisor">
                                    @error('position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Ubah Password</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-1"></i>
                            Kosongkan jika tidak ingin mengubah password
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Password Saat Ini</label>
                                    <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                           id="current_password" name="current_password">
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password Baru</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Minimal 8 karakter</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Preferensi</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" 
                                           name="email_notifications" {{ old('email_notifications', $user->email_notifications ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="email_notifications">
                                        Notifikasi Email
                                    </label>
                                    <div class="form-text">Terima notifikasi pemesanan via email</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="push_notifications" 
                                           name="push_notifications" {{ old('push_notifications', $user->push_notifications ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="push_notifications">
                                        Notifikasi Push
                                    </label>
                                    <div class="form-text">Terima notifikasi real-time di browser</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">Zona Waktu</label>
                                    <select class="form-select @error('timezone') is-invalid @enderror" id="timezone" name="timezone">
                                        <option value="Asia/Jakarta" {{ old('timezone', $user->timezone ?? 'Asia/Jakarta') === 'Asia/Jakarta' ? 'selected' : '' }}>WIB (GMT+7)</option>
                                        <option value="Asia/Makassar" {{ old('timezone', $user->timezone ?? 'Asia/Jakarta') === 'Asia/Makassar' ? 'selected' : '' }}>WITA (GMT+8)</option>
                                        <option value="Asia/Jayapura" {{ old('timezone', $user->timezone ?? 'Asia/Jakarta') === 'Asia/Jayapura' ? 'selected' : '' }}>WIT (GMT+9)</option>
                                    </select>
                                    @error('timezone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="language" class="form-label">Bahasa</label>
                                    <select class="form-select @error('language') is-invalid @enderror" id="language" name="language">
                                        <option value="id" {{ old('language', $user->language ?? 'id') === 'id' ? 'selected' : '' }}>Bahasa Indonesia</option>
                                        <option value="en" {{ old('language', $user->language ?? 'id') === 'en' ? 'selected' : '' }}>English</option>
                                    </select>
                                    @error('language')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Informasi Keamanan</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Login Terakhir</label>
                                    <p class="form-control-static">
                                        {{ $user->last_login_at ? $user->last_login_at->format('d M Y, H:i') : 'Belum pernah login' }}
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">IP Address Terakhir</label>
                                    <p class="form-control-static font-monospace">{{ $user->last_login_ip ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Percobaan Login Gagal</label>
                                    <p class="form-control-static">
                                        <span class="badge {{ $user->login_attempts > 3 ? 'bg-danger' : 'bg-success' }}">
                                            {{ $user->login_attempts }} kali
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status Email</label>
                                    <p class="form-control-static">
                                        @if($user->email_verified_at)
                                            <span class="badge bg-success">Terverifikasi</span>
                                        @else
                                            <span class="badge bg-warning">Belum Terverifikasi</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('profile.show') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i> Simpan Perubahan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
function previewFile() {
    const input = document.getElementById('avatar');
    const previewImage = document.getElementById('previewImage');
    const previewDiv = document.getElementById('previewDiv');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            if (previewImage) {
                previewImage.src = e.target.result;
            } else if (previewDiv) {
                previewDiv.innerHTML = `<img src="${e.target.result}" class="rounded-circle" width="150" height="150">`;
            }
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Update preview div when name changes
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const previewDiv = document.getElementById('previewDiv');
    const avatarInput = document.getElementById('avatar');
    
    if (!avatarInput.files || !avatarInput.files[0]) {
        const initial = name ? name.charAt(0).toUpperCase() : '';
        if (previewDiv && !document.getElementById('previewImage')) {
            previewDiv.innerHTML = `<span style="font-size: 3rem;">${initial}</span>`;
        }
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('password').value;
    const confirmPassword = document.getElementById('password_confirmation').value;
    
    // Jika ada salah satu field password yang diisi, maka semua harus diisi
    if (currentPassword || newPassword || confirmPassword) {
        if (!currentPassword) {
            e.preventDefault();
            Swal.fire('Error!', 'Password saat ini harus diisi jika ingin mengubah password.', 'error');
            return;
        }
        
        if (!newPassword) {
            e.preventDefault();
            Swal.fire('Error!', 'Password baru harus diisi.', 'error');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            Swal.fire('Error!', 'Konfirmasi password tidak sesuai.', 'error');
            return;
        }
        
        if (newPassword.length < 8) {
            e.preventDefault();
            Swal.fire('Error!', 'Password baru minimal 8 karakter.', 'error');
            return;
        }
    }
});

// Show confirmation when email is changed
document.getElementById('email').addEventListener('change', function() {
    const originalEmail = '{{ $user->email }}';
    const newEmail = this.value;
    
    if (originalEmail !== newEmail) {
        Swal.fire({
            title: 'Peringatan!',
            text: 'Mengubah email akan memerlukan verifikasi ulang. Email verifikasi akan dikirim ke alamat baru.',
            icon: 'warning',
            confirmButtonText: 'Mengerti'
        });
    }
});
</script>
@endpush
@endsection
