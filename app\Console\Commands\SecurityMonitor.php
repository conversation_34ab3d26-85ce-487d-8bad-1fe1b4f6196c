<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\SecurityLog;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SecurityMonitor extends Command
{
    protected $signature = 'security:monitor';
    protected $description = 'Monitor security threats and update user risk levels';

    public function handle()
    {
        $this->info('Starting basic security monitoring...');

        try {
            // Simple statistics only
            $this->info('Collecting security statistics...');
            
            $stats = [
                'total_users' => User::count(),
                'locked_users' => User::where('locked_until', '>', now())->count(),
                'security_events_today' => SecurityLog::whereDate('created_at', today())->count(),
            ];

            $this->info('Security Statistics:');
            $this->info("- Total Users: {$stats['total_users']}");
            $this->info("- Locked Users: {$stats['locked_users']}");
            $this->info("- Security Events Today: {$stats['security_events_today']}");

            Cache::put('security_stats', $stats, 3600);
            
            $this->info('Security monitoring completed successfully.');
            
        } catch (\Exception $e) {
            $this->error('Security monitoring failed: ' . $e->getMessage());
        }
    }

    protected function updateUserRiskLevels()
    {
        $this->info('Updating user risk levels...');
        
        $count = 0;
        User::where('role', '!=', 'admin')->chunk(50, function ($users) use (&$count) {
            foreach ($users as $user) {
                try {
                    // Skip if user doesn't exist or is admin
                    if (!$user || $user->role === 'admin') {
                        continue;
                    }
                    
                    // Simple risk update without heavy operations
                    $riskScore = $this->calculateSimpleRiskScore($user);
                    $user->update(['risk_level' => $this->getRiskLevel($riskScore)]);
                    $count++;
                } catch (\Exception $e) {
                    $this->warn("Failed to update risk for user {$user->id}: " . $e->getMessage());
                }
            }
        });

        $this->info("Updated risk levels for {$count} users.");
    }

    protected function calculateSimpleRiskScore($user): int
    {
        $score = 0;
        
        // Basic risk factors
        if ($user->failed_login_attempts > 3) $score += 20;
        if ($user->account_status === 'locked') $score += 30;
        if ($user->force_logout) $score += 15;
        
        return $score;
    }

    protected function getRiskLevel(int $score): string
    {
        if ($score >= 50) return 'critical';
        if ($score >= 30) return 'high';
        if ($score >= 15) return 'medium';
        return 'low';
    }

    protected function checkSuspiciousActivities()
    {
        $this->info('Checking for suspicious activities...');

        // Check for multiple failed logins from same IP
        $suspiciousIPs = SecurityLog::where('action', 'login_failed')
            ->where('created_at', '>=', now()->subHours(1))
            ->get()
            ->groupBy(function($log) {
                return $log->additional_data['ip'] ?? 'unknown';
            })
            ->filter(function($logs) {
                return $logs->count() >= 10; // 10+ failed logins in 1 hour
            });

        foreach ($suspiciousIPs as $ip => $logs) {
            $this->warn("Suspicious activity detected from IP: {$ip} ({$logs->count()} failed logins)");
            
            // Block IP temporarily
            Cache::put("ip_blocked:{$ip}", true, 3600); // 1 hour block
            
            SecurityLog::logAction(
                'ip_auto_blocked',
                null,
                'critical',
                [
                    'ip' => $ip,
                    'failed_login_count' => $logs->count(),
                    'time_window' => '1 hour',
                    'blocked_until' => now()->addHour()->toISOString()
                ]
            );
        }

        // Check for rapid user creation
        $rapidUsers = User::where('created_at', '>=', now()->subMinutes(30))
            ->where('role', '!=', 'admin')
            ->count();

        if ($rapidUsers >= 5) {
            $this->warn("Rapid user creation detected: {$rapidUsers} users in 30 minutes");
            
            SecurityLog::logAction(
                'rapid_user_creation_detected',
                null,
                'high',
                [
                    'user_count' => $rapidUsers,
                    'time_window' => '30 minutes'
                ]
            );
        }
    }

    protected function cleanupOldLogs()
    {
        $this->info('Cleaning up old security logs...');

        $deletedCount = SecurityLog::where('created_at', '<', now()->subDays(90))->delete();
        
        if ($deletedCount > 0) {
            $this->info("Deleted {$deletedCount} old security logs (older than 90 days).");
        }
    }

    protected function checkCompromisedAccounts()
    {
        $this->info('Checking for potentially compromised accounts...');

        // Check for accounts with multiple logins from different countries
        $users = User::where('role', '!=', 'admin')
            ->whereNotNull('login_history')
            ->get();

        foreach ($users as $user) {
            $loginHistory = json_decode($user->login_history, true) ?? [];
            
            if (count($loginHistory) >= 3) {
                $countries = [];
                foreach ($loginHistory as $login) {
                    $ip = $login['ip'] ?? '';
                    $country = $this->getCountryFromIP($ip);
                    $countries[] = $country;
                }

                $uniqueCountries = array_unique($countries);
                
                if (count($uniqueCountries) >= 3) {
                    $this->warn("Potential account compromise: User {$user->username} logged in from {count($uniqueCountries)} different countries");
                    
                    $user->addSecurityFlag('multiple_country_logins', [
                        'countries' => $uniqueCountries,
                        'login_count' => count($loginHistory)
                    ]);
                }
            }
        }
    }

    protected function updateSecurityStats()
    {
        $this->info('Updating security statistics...');

        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'locked_users' => User::where('account_status', 'locked')->count(),
            'high_risk_users' => User::where('risk_level', 'high')->count(),
            'critical_risk_users' => User::where('risk_level', 'critical')->count(),
            'failed_logins_today' => SecurityLog::where('action', 'login_failed')
                ->whereDate('created_at', today())
                ->count(),
            'security_events_today' => SecurityLog::whereDate('created_at', today())->count(),
            'updated_at' => now()
        ];

        Cache::put('security_stats', $stats, 3600); // Cache for 1 hour

        $this->info('Security statistics updated.');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Users', $stats['total_users']],
                ['Active Users', $stats['active_users']],
                ['Locked Users', $stats['locked_users']],
                ['High Risk Users', $stats['high_risk_users']],
                ['Critical Risk Users', $stats['critical_risk_users']],
                ['Failed Logins Today', $stats['failed_logins_today']],
                ['Security Events Today', $stats['security_events_today']]
            ]
        );
    }

    protected function getCountryFromIP($ip)
    {
        // Simple country detection (enhance with proper GeoIP service)
        $indonesianRanges = ['103.', '118.', '125.', '180.', '202.', '203.'];
        
        foreach ($indonesianRanges as $range) {
            if (str_starts_with($ip, $range)) {
                return 'Indonesia';
            }
        }

        return 'Unknown';
    }
}
