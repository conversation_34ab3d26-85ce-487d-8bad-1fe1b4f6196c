@extends('layouts.dashboard')

@section('title', 'Manajemen Ruang Rapat - Admin')

@section('content')
<div class="container-fluid">
    <!-- Main Content -->
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">Manajemen Ruang Rapat</h1>
                <p class="text-muted">Kelola dan monitor ruang rapat</p>
            </div>
            <div>
                <a href="{{ route('admin.rooms.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> Tambah Ruangan
                </a>
            </div>
        </div>
    </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Ruangan
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $rooms->count() }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-door-closed fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Ruangan Aktif
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $rooms->where('is_active', true)->count() }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Kapasitas Total
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $rooms->sum('capacity') }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Booking Bulan Ini
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $rooms->sum('this_month_bookings') }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rooms Table -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Daftar Ruang Rapat</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="roomsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Ruangan</th>
                                    <th>Lokasi</th>
                                    <th>Kapasitas</th>
                                    <th>Tarif/Jam</th>
                                    <th>Status</th>
                                    <th>Booking Bulan Ini</th>
                                    <th>Booking Hari Ini</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($rooms as $room)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($room->image)
                                            <img src="{{ Storage::url($room->image) }}" alt="{{ $room->name }}" 
                                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="bi bi-door-closed text-muted"></i>
                                            </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-0">{{ $room->name }}</h6>
                                                @if($room->description)
                                                <small class="text-muted">{{ Str::limit($room->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $room->location }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $room->capacity }} orang</span>
                                    </td>
                                    <td>
                                        @if($room->hourly_rate)
                                        Rp {{ number_format($room->hourly_rate, 0, ',', '.') }}
                                        @else
                                        <span class="text-muted">Gratis</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="status{{ $room->id }}" 
                                                   {{ $room->is_active ? 'checked' : '' }}
                                                   onchange="toggleRoomStatus({{ $room->id }})">
                                            <label class="form-check-label" for="status{{ $room->id }}">
                                                <span class="badge bg-{{ $room->is_active ? 'success' : 'secondary' }}">
                                                    {{ $room->is_active ? 'Aktif' : 'Nonaktif' }}
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $room->this_month_bookings }}</span>
                                    </td>
                                    <td>
                                        @php
                                        $todayBookings = $room->bookings->where('start_time', '>=', today())->where('start_time', '<', today()->addDay())->count();
                                        @endphp
                                        <span class="badge bg-{{ $todayBookings > 0 ? 'warning' : 'light' }}">{{ $todayBookings }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.rooms.show', $room) }}" 
                                               class="btn btn-sm btn-outline-info" title="Detail">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.rooms.edit', $room) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteRoom({{ $room->id }}, '{{ $room->name }}')" title="Hapus">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="bi bi-door-closed fs-1 text-muted"></i>
                                        <p class="mt-2 text-muted">Belum ada ruang rapat yang terdaftar</p>
                                        <a href="{{ route('admin.rooms.create') }}" class="btn btn-primary">
                                            <i class="bi bi-plus-lg"></i> Tambah Ruangan Pertama
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
</div>

@endsection

@push('scripts')
<script>
// Global function for toggle room status - declare before DOMContentLoaded
window.toggleRoomStatus = function(roomId) {
    const checkbox = document.getElementById(`status${roomId}`);
    const isActive = checkbox.checked;
    
    fetch(`/admin/rooms/${roomId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            Toast.fire({
                icon: 'success',
                title: data.message
            });
            
            // Update badge
            const badge = checkbox.parentElement.querySelector('.badge');
            if (badge) {
                badge.className = `badge bg-${data.is_active ? 'success' : 'secondary'}`;
                badge.textContent = data.status_text || (data.is_active ? 'Aktif' : 'Nonaktif');
            }
            
            // Update checkbox state
            checkbox.checked = data.is_active;
        } else {
            // Revert checkbox state
            checkbox.checked = !isActive;
            Toast.fire({
                icon: 'error',
                title: data.message || 'Gagal mengubah status ruangan'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Revert checkbox state
        checkbox.checked = !isActive;
        Toast.fire({
            icon: 'error',
            title: 'Terjadi kesalahan saat mengubah status'
        });
    });
};

// Alias for global access
function toggleRoomStatus(roomId) {
    return window.toggleRoomStatus(roomId);
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable if available
    if (typeof DataTable !== 'undefined' && document.getElementById('roomsTable')) {
        new DataTable('#roomsTable', {
            pageLength: 10,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json'
            }
        });
    }
});

function deleteRoom(roomId, roomName) {
    Swal.fire({
        title: 'Konfirmasi Hapus Ruangan',
        html: `
            <p>Apakah Anda yakin ingin menghapus ruangan <strong>${roomName}</strong>?</p>
            <p class="text-warning mt-3">
                <i class="bi bi-exclamation-triangle"></i>
                Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait ruangan.
            </p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Hapus',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Create form data
            const formData = new FormData();
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            formData.append('_method', 'DELETE');

            fetch(`/admin/rooms/${roomId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text(); // Laravel might return redirect or JSON
            })
            .then(data => {
                // Redirect might happen or we get JSON response
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: jsonData.message || 'Ruangan berhasil dihapus',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: jsonData.message || 'Terjadi kesalahan'
                        });
                    }
                } catch (e) {
                    // If not JSON, likely a redirect response, just reload
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Ruangan berhasil dihapus',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        window.location.reload();
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem. Silakan coba lagi.'
                });
            });
        }
    });
}
</script>
@endpush
