<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Carbon\Carbon;

echo "=== CEK STATUS VERIFIKASI ===\n";

// Ambil semua user
$users = User::all();

foreach ($users as $user) {
    echo "\n--- User: {$user->email} ---\n";
    echo "ID: {$user->id}\n";
    echo "Email verified at: " . ($user->email_verified_at ? $user->email_verified_at->toDateTimeString() : 'NULL') . "\n";
    echo "Verification token: " . ($user->email_verification_token ? $user->email_verification_token : 'NULL') . "\n";
    echo "Token expires at: " . ($user->email_verification_expires_at ? $user->email_verification_expires_at->toDateTimeString() : 'NULL') . "\n";
    echo "Status: " . ($user->hasVerifiedEmail() ? 'TERVERIFIKASI' : 'BELUM TERVERIFIKASI') . "\n";
}

echo "\n=== RINGKASAN ===\n";
$verified = User::whereNotNull('email_verified_at')->count();
$unverified = User::whereNull('email_verified_at')->count();

echo "Total user: " . User::count() . "\n";
echo "Terverifikasi: $verified\n";
echo "Belum terverifikasi: $unverified\n";

echo "\n=== SELESAI ===\n";
