<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reset Password</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }
        .warning {
            background: #fee2e2;
            border: 1px solid #fca5a5;
            color: #991b1b;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .security-tips {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0c4a6e;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐</div>
            <h1>Reset Password</h1>
            <p>Sistem Pemesanan Ruang Rapat</p>
        </div>
        
        <div class="content">
            <h2>Halo, {{ $user->name }}!</h2>
            
            <p>Kami menerima permintaan untuk mereset password akun Anda. Jika Anda yang meminta reset password, silakan klik tombol di bawah ini:</p>
            
            <div style="text-align: center;">
                <a href="{{ $resetUrl }}" class="btn">Reset Password</a>
            </div>
            
            <p>Atau copy dan paste link berikut di browser Anda:</p>
            <p style="word-break: break-all; color: #dc2626;">{{ $resetUrl }}</p>
            
            <div class="warning">
                <strong>Penting:</strong> Link reset password ini akan expired dalam 1 jam. Jika Anda tidak meminta reset password, abaikan email ini dan segera hubungi administrator.
            </div>
            
            <div class="security-tips">
                <h4>Tips Keamanan Password:</h4>
                <ul>
                    <li>Gunakan minimal 8 karakter</li>
                    <li>Kombinasi huruf besar, huruf kecil, angka, dan simbol</li>
                    <li>Jangan gunakan informasi personal</li>
                    <li>Jangan gunakan password yang sama dengan aplikasi lain</li>
                    <li>Ganti password secara berkala</li>
                </ul>
            </div>
            
            <h3>Detail Akun:</h3>
            <ul>
                <li><strong>Nama:</strong> {{ $user->name }}</li>
                <li><strong>Username:</strong> {{ $user->username }}</li>
                <li><strong>Email:</strong> {{ $user->email }}</li>
                <li><strong>Waktu Permintaan:</strong> {{ now()->format('d/m/Y H:i:s') }}</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
            <p>Jika Anda tidak meminta reset password, segera hubungi administrator sistem.</p>
            <p>&copy; {{ date('Y') }} Sistem Pemesanan Ruang Rapat. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
