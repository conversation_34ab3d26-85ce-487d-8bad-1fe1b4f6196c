<!DOCTYPE html>
<html lang="id" data-theme="{{ auth()->check() ? auth()->user()->theme_preference : 'light' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Dashboard - Sistem Pemesanan Ruang Rapat')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            /* Light Theme */
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --info-color: #0891b2;
            
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow: rgba(0, 0, 0, 0.1);
            
            --sidebar-width: 280px;
            --header-height: 70px;
        }

        [data-theme="solarized-dark"] {
            --primary-color: #268bd2;
            --primary-hover: #2aa198;
            --secondary-color: #93a1a1;
            --success-color: #859900;
            --warning-color: #b58900;
            --error-color: #dc322f;
            --info-color: #2aa198;
            
            --bg-primary: #002b36;
            --bg-secondary: #073642;
            --bg-tertiary: #586e75;
            --text-primary: #839496;
            --text-secondary: #93a1a1;
            --text-muted: #586e75;
            --border-color: #073642;
            --shadow: rgba(0, 0, 0, 0.5);
        }

        [data-theme="synth-wave"] {
            --primary-color: #ff006e;
            --primary-hover: #8338ec;
            --secondary-color: #fb5607;
            --success-color: #3a86ff;
            --warning-color: #ffbe0b;
            --error-color: #ff006e;
            --info-color: #8338ec;
            
            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --text-muted: #a0a0a0;
            --border-color: #3a3a5c;
            --shadow: rgba(255, 0, 110, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Dashboard Layout */
        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            transform: translateX(0);
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            padding: 0 1.5rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-muted);
            letter-spacing: 0.05em;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.2s;
            font-weight: 500;
        }

        .nav-link:hover {
            background: var(--bg-tertiary);
            color: var(--primary-color);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .nav-link.active:hover {
            background: var(--primary-hover);
            transform: translateX(0);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Navigation Badge */
        .nav-badge {
            background: var(--error-color);
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: auto;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }

        .nav-badge-success {
            background: var(--success-color);
        }

        .nav-badge-warning {
            background: var(--warning-color);
        }

        .nav-badge-info {
            background: var(--info-color);
        }

        .nav-badge-secondary {
            background: var(--secondary-color);
        }

        .nav-badge-danger {
            background: var(--error-color);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Header */
        .header {
            height: var(--header-height);
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 0 2rem;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-primary);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .sidebar-toggle:hover {
            background: var(--bg-tertiary);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        /* Notification Center */
        .notification-center {
            position: relative;
            display: inline-block;
            margin-right: 1rem;
        }

        .notification-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem;
            color: var(--text-primary);
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
        }

        .notification-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--error-color);
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }

        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 30px var(--shadow);
            width: 350px;
            max-height: 400px;
            overflow: hidden;
            z-index: 1000;
            display: none;
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
        }

        .btn-mark-all-read {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 0.75rem;
        }

        .notification-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification-empty {
            padding: 2rem;
            text-align: center;
            color: var(--text-muted);
        }

        .notification-empty i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .notification-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background 0.2s;
        }

        .notification-item:hover {
            background: var(--bg-secondary);
        }

        .notification-item.unread {
            background: var(--bg-tertiary);
        }

        .notification-footer {
            padding: 0.75rem;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }

        .btn-view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Theme Switcher Dropdown */
        .theme-switcher {
            position: relative;
            display: inline-block;
        }

        .theme-dropdown-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.2s;
        }

        .theme-dropdown-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 20px var(--shadow);
            padding: 0.5rem 0;
            min-width: 180px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s;
        }

        .theme-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .theme-option {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .theme-option:hover {
            background: var(--bg-secondary);
        }

        .theme-option.active {
            background: var(--primary-color);
            color: white;
        }

        .theme-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--border-color);
        }

        .theme-light-indicator { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .theme-solarized-indicator { background: linear-gradient(135deg, #268bd2, #2aa198); }
        .theme-synth-indicator { background: linear-gradient(135deg, #ff006e, #8338ec); }

        /* User Menu */
        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px var(--shadow);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 0.5rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px var(--shadow);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .dropdown-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .dropdown-email {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background: var(--bg-tertiary);
            color: var(--primary-color);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--border-color);
            margin: 0.5rem 0;
        }

        /* Content Area */
        .content {
            padding: 2rem;
            min-height: calc(100vh - var(--header-height));
        }

        /* Dashboard Specific Styles */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .welcome-section {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .welcome-content h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .welcome-stats {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 0.75rem;
            text-align: center;
            min-width: 120px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        /* Quick Actions */
        .quick-actions {
            margin-bottom: 2rem;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .action-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px var(--shadow);
            border-color: var(--primary-color);
        }

        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .action-primary .action-icon { background: var(--primary-color); }
        .action-secondary .action-icon { background: var(--secondary-color); }
        .action-info .action-icon { background: var(--info-color); }
        .action-warning .action-icon { background: var(--warning-color); }

        .action-content h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .action-content p {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .dashboard-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-action {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .card-action:hover {
            color: var(--primary-hover);
        }

        .card-content {
            padding: 1.5rem;
        }

        /* Bootstrap Card Enhancements */
        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px var(--shadow);
            margin-bottom: 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .row {
            margin: 0 -0.75rem;
        }

        .col, .col-auto, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-sm, .col-sm-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
        .col-md, .col-md-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
        .col-lg, .col-lg-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
        .col-xl, .col-xl-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
            padding: 0 0.75rem;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        .py-2 {
            padding-top: 0.5rem !important;
            padding-bottom: 0.5rem !important;
        }

        .h-100 {
            height: 100% !important;
        }

        .shadow {
            box-shadow: 0 0.15rem 1.75rem 0 var(--shadow) !important;
        }

        .border-left-primary {
            border-left: 0.25rem solid var(--primary-color) !important;
        }

        .border-left-success {
            border-left: 0.25rem solid var(--success-color) !important;
        }

        .border-left-info {
            border-left: 0.25rem solid var(--info-color) !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid var(--warning-color) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .text-success {
            color: var(--success-color) !important;
        }

        .text-info {
            color: var(--info-color) !important;
        }

        .text-warning {
            color: var(--warning-color) !important;
        }

        .text-gray-800 {
            color: var(--text-primary) !important;
        }

        .text-gray-300 {
            color: var(--text-muted) !important;
        }

        .text-xs {
            font-size: 0.75rem;
        }

        .font-weight-bold {
            font-weight: 700 !important;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        .no-gutters {
            margin: 0;
        }

        .no-gutters > .col,
        .no-gutters > [class*="col-"] {
            padding: 0;
        }

        .align-items-center {
            align-items: center !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }

        .d-flex {
            display: flex !important;
        }

        .fa-2x {
            font-size: 2em;
        }

        /* Container & Layout Classes */
        .container-fluid {
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
        }

        .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
            color: var(--text-primary);
        }

        .h3, h3 {
            font-size: 1.75rem;
        }

        .h5, h5 {
            font-size: 1.25rem;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .mb-1 {
            margin-bottom: 0.25rem !important;
        }

        .mb-2 {
            margin-bottom: 0.5rem !important;
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        .mb-5 {
            margin-bottom: 3rem !important;
        }

        .mr-2 {
            margin-right: 0.5rem !important;
        }

        .text-end {
            text-align: right !important;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        small {
            font-size: 0.875em;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        /* Badge Classes */
        .badge {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.375rem;
            color: #fff;
        }

        .bg-success {
            background-color: var(--success-color) !important;
        }

        .bg-warning {
            background-color: var(--warning-color) !important;
        }

        .bg-danger {
            background-color: var(--error-color) !important;
        }

        .bg-info {
            background-color: var(--info-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        /* Margin classes */
        .m-0 {
            margin: 0 !important;
        }

        .mt-1 {
            margin-top: 0.25rem !important;
        }

        .mt-2 {
            margin-top: 0.5rem !important;
        }

        .mt-3 {
            margin-top: 1rem !important;
        }

        .mt-4 {
            margin-top: 1.5rem !important;
        }

        .py-3 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .col-xl-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }

        @media (max-width: 576px) {
            .col-xl-3 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h4 {
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        /* Status Badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-approved { background: rgba(5, 150, 105, 0.1); color: var(--success-color); }
        .status-pending { background: rgba(217, 119, 6, 0.1); color: var(--warning-color); }
        .status-rejected { background: rgba(220, 38, 38, 0.1); color: var(--error-color); }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }

            .header {
                padding: 0 1rem;
            }

            .content {
                padding: 1rem;
            }

            .welcome-section {
                flex-direction: column;
                text-align: center;
            }

            .welcome-stats {
                justify-content: center;
            }

            .action-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth transitions */
        *, *::before, *::after {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* Notification Modal Styles */
        .notification-modal .notification-item-modal:hover {
            background-color: var(--bg-secondary) !important;
        }

        .notification-modal .notification-item-modal.unread {
            background-color: var(--bg-tertiary) !important;
            border-left: 3px solid var(--primary-color) !important;
        }

        .notification-modal .notification-item-modal {
            transition: background-color 0.2s ease !important;
        }

        .notification-modal .swal2-html-container {
            padding: 0 !important;
        }

        .notification-modal .swal2-popup {
            background: var(--bg-primary) !important;
            color: var(--text-primary) !important;
        }

        .notification-modal .swal2-title {
            color: var(--text-primary) !important;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="{{ route('dashboard') }}" class="sidebar-logo">
                    <i class="bi bi-calendar-check"></i>
                    <span>RuangRapat</span>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Menu Utama</div>
                    <div class="nav-item">
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-house"></i></span>
                            <span>Dashboard</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('bookings.create') }}" class="nav-link {{ request()->routeIs('bookings.create') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-plus-circle"></i></span>
                            <span>Buat Pemesanan</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('bookings.index') }}" class="nav-link {{ request()->routeIs('bookings.*') && !request()->routeIs('bookings.create') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-calendar-event"></i></span>
                            <span>Pemesanan Saya</span>
                            <span class="nav-badge" id="pending-bookings-badge" style="display: none;">0</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('rooms.index') }}" class="nav-link {{ request()->routeIs('rooms.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-door-open"></i></span>
                            <span>Ruang Rapat</span>
                            <span class="nav-badge nav-badge-success" id="available-rooms-badge">{{ App\Models\MeetingRoom::active()->count() }}</span>
                        </a>
                    </div>
                </div>

                @if(auth()->user()->isAdmin())
                <div class="nav-section">
                    <div class="nav-section-title">Administrator</div>
                    <div class="nav-item">
                        <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-shield-shaded"></i></span>
                            <span>Panel Admin</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-people"></i></span>
                            <span>Kelola User</span>
                            <span class="nav-badge nav-badge-info" id="total-users-badge">{{ App\Models\User::where('is_active', true)->count() }}</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('admin.rooms.index') }}" class="nav-link {{ request()->routeIs('admin.rooms.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-door-closed"></i></span>
                            <span>Kelola Ruangan</span>
                            <span class="nav-badge nav-badge-secondary" id="total-rooms-badge">{{ App\Models\MeetingRoom::count() }}</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('admin.bookings.index') }}" class="nav-link {{ request()->routeIs('admin.bookings.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-calendar-check"></i></span>
                            <span>Kelola Booking</span>
                            <span class="nav-badge nav-badge-warning" id="pending-approvals-badge">{{ App\Models\Booking::where('status', 'pending')->count() }}</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('admin.security.logs') }}" class="nav-link {{ request()->routeIs('admin.security.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-shield-check"></i></span>
                            <span>Log Keamanan</span>
                            @php
                                $securityAlerts = App\Models\SecurityLog::where('severity', 'warning')
                                    ->orWhere('severity', 'error')
                                    ->where('created_at', '>=', now()->subDays(7))
                                    ->count();
                            @endphp
                            @if($securityAlerts > 0)
                                <span class="nav-badge nav-badge-danger" id="security-alerts-badge">{{ $securityAlerts }}</span>
                            @endif
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="{{ route('admin.reports') }}" class="nav-link {{ request()->routeIs('admin.reports') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-graph-up"></i></span>
                            <span>Laporan</span>
                        </a>
                    </div>
                </div>
                @endif

                <div class="nav-section">
                    <div class="nav-section-title">Akun</div>
                    <div class="nav-item">
                        <a href="{{ route('profile.show') }}" class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}">
                            <span class="nav-icon"><i class="bi bi-person-gear"></i></span>
                            <span>Profil Saya</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                            @csrf
                            <button type="submit" class="nav-link" style="background: none; border: none; width: 100%; text-align: left;">
                                <span class="nav-icon"><i class="bi bi-box-arrow-right"></i></span>
                                <span>Keluar</span>
                            </button>
                        </form>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1 class="header-title">@yield('page-title', 'Dashboard')</h1>
                </div>
                
                <div class="header-right">
                    <!-- Real-time Notifications -->
                    <div class="notification-center">
                        <button class="notification-btn" id="notification-btn" onclick="toggleNotifications()">
                            <i class="bi bi-bell"></i>
                            <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                        </button>
                        <div class="notification-dropdown" id="notification-dropdown">
                            <div class="notification-header">
                                <h6>Notifikasi</h6>
                                <button class="btn-mark-all-read" onclick="markAllAsRead()">
                                    <small>Tandai Semua Dibaca</small>
                                </button>
                            </div>
                            <div class="notification-list" id="notification-list">
                                <div class="notification-empty">
                                    <i class="bi bi-bell-slash"></i>
                                    <p>Tidak ada notifikasi</p>
                                </div>
                            </div>
                            <div class="notification-footer">
                                <a href="#" class="btn-view-all" onclick="showAllNotifications()">Lihat Semua Notifikasi</a>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Switcher -->
                    <div class="theme-switcher">
                        <button class="theme-dropdown-btn" id="theme-dropdown-btn">
                            <i class="bi bi-palette"></i>
                            <span id="current-theme">Light</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="theme-dropdown" id="theme-dropdown">
                            <button class="theme-option active" onclick="setTheme('light')" data-theme="light">
                                <div class="theme-indicator theme-light-indicator"></div>
                                <span>Light Theme</span>
                            </button>
                            <button class="theme-option" onclick="setTheme('solarized-dark')" data-theme="solarized-dark">
                                <div class="theme-indicator theme-solarized-indicator"></div>
                                <span>Solarized Dark</span>
                            </button>
                            <button class="theme-option" onclick="setTheme('synth-wave')" data-theme="synth-wave">
                                <div class="theme-indicator theme-synth-indicator"></div>
                                <span>Synth Wave</span>
                            </button>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="user-menu">
                        <div class="user-avatar" onclick="toggleUserDropdown()">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <div class="user-dropdown" id="user-dropdown">
                            <div class="dropdown-header">
                                <div class="dropdown-name">{{ auth()->user()->name }}</div>
                                <div class="dropdown-email">{{ auth()->user()->email }}</div>
                            </div>
                            <a href="{{ route('profile.show') }}" class="dropdown-item">
                                <i class="bi bi-person"></i>
                                <span>Profil Saya</span>
                            </a>
                            <a href="{{ route('bookings.index') }}" class="dropdown-item">
                                <i class="bi bi-calendar-event"></i>
                                <span>Pemesanan Saya</span>
                            </a>
                            @if(auth()->user()->isAdmin())
                            <div class="dropdown-divider"></div>
                            <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                <i class="bi bi-shield-shaded"></i>
                                <span>Panel Admin</span>
                            </a>
                            @endif
                            <div class="dropdown-divider"></div>
                            <form action="{{ route('logout') }}" method="POST" style="display: inline; width: 100%;">
                                @csrf
                                <button type="submit" class="dropdown-item" style="background: none; border: none; width: 100%; text-align: left;">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>Keluar</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.min.js"></script>
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    
    <script>
        // CSRF Token setup for AJAX
        window.Laravel = {
            csrfToken: '{{ csrf_token() }}'
        };

        // Theme management
        function setTheme(theme, saveToDatabase = true) {
            console.log('Dashboard setTheme called with:', theme, 'saveToDatabase:', saveToDatabase);
            
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update active theme option
            document.querySelectorAll('.theme-option').forEach(option => option.classList.remove('active'));
            document.querySelector(`[data-theme="${theme}"]`).classList.add('active');
            
            // Update current theme display
            const themeNames = {
                'light': 'Light',
                'solarized-dark': 'Solarized Dark',
                'synth-wave': 'Synth Wave'
            };
            document.getElementById('current-theme').textContent = themeNames[theme];
            
            // Close dropdown
            document.getElementById('theme-dropdown').classList.remove('show');
            
            // Save to database if user is authenticated
            if (saveToDatabase && document.querySelector('meta[name="csrf-token"]')) {
                console.log('Saving theme to database:', theme);
                
                fetch('{{ route("theme.update") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        theme: theme
                    })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Success response:', data);
                    if (data.success) {
                        Toast.fire({
                            icon: 'success',
                            title: data.message
                        });
                    }
                })
                .catch(error => {
                    console.error('Error saving theme:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Gagal menyimpan tema: ' + error.message
                    });
                });
            } else {
                console.log('Not saving to database - saveToDatabase:', saveToDatabase, 'has CSRF token:', !!document.querySelector('meta[name="csrf-token"]'));
            }
        }

        // Theme dropdown toggle
        function toggleThemeDropdown() {
            const dropdown = document.getElementById('theme-dropdown');
            dropdown.classList.toggle('show');
        }

        // Load saved theme (prioritize database over localStorage for authenticated users)
        @auth
            const savedTheme = '{{ auth()->user()->theme_preference }}';
        @else
            const savedTheme = localStorage.getItem('theme') || 'light';
        @endauth
        setTheme(savedTheme, false); // Don't save to database on initial load

        // Theme dropdown events
        document.getElementById('theme-dropdown-btn').addEventListener('click', toggleThemeDropdown);

        // Sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('expanded');
        }

        // User dropdown toggle
        function toggleUserDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        // Notification functions
        function toggleNotifications() {
            const dropdown = document.getElementById('notification-dropdown');
            dropdown.classList.toggle('show');
            
            if (dropdown.classList.contains('show')) {
                loadNotifications();
            }
        }

        function loadNotifications() {
            fetch('/api/notifications', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                displayNotifications(data.notifications || []);
                updateNotificationBadge(data.unread_count || 0);
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
        }

        function displayNotifications(notifications) {
            const notificationList = document.getElementById('notification-list');
            
            if (notifications.length === 0) {
                notificationList.innerHTML = `
                    <div class="notification-empty">
                        <i class="bi bi-bell-slash"></i>
                        <p>Tidak ada notifikasi</p>
                    </div>
                `;
                return;
            }

            notificationList.innerHTML = notifications.map(notification => `
                <div class="notification-item ${notification.is_unread ? 'unread' : ''}" 
                     onclick="markAsRead('${notification.id}')">
                    <div class="notification-content">
                        <div class="d-flex justify-content-between align-items-start">
                            <strong style="color: var(--text-primary);">${notification.title}</strong>
                            ${notification.is_unread ? '<span class="badge bg-primary">Baru</span>' : ''}
                        </div>
                        <p style="color: var(--text-secondary); margin: 0.25rem 0;">${notification.message}</p>
                        <small class="text-muted">${notification.time_ago}</small>
                    </div>
                </div>
            `).join('');
        }

        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-badge');
            if (count !== undefined) {
                // Direct count provided
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'inline-block';
                } else {
                    badge.style.display = 'none';
                }
            } else {
                // Fetch count from API
                fetch('/api/notifications/unread-count', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    const unreadCount = data.unread_count || 0;
                    if (unreadCount > 0) {
                        badge.textContent = unreadCount;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error updating notification badge:', error);
                });
            }
        }

        function markAsRead(notificationId) {
            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        function markAllAsRead() {
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        }

        // Show all notifications in a modal
        function showAllNotifications() {
            // Close notification dropdown
            document.getElementById('notification-dropdown').classList.remove('show');
            
            // Fetch all notifications
            fetch('/api/notifications?limit=50', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                const notifications = data.notifications || [];
                
                let notificationsHtml = '';
                if (notifications.length === 0) {
                    notificationsHtml = `
                        <div class="text-center py-4" style="color: var(--text-muted);">
                            <i class="bi bi-bell-slash" style="font-size: 3rem; opacity: 0.5;"></i>
                            <p class="mt-2">Tidak ada notifikasi</p>
                        </div>
                    `;
                } else {
                    notificationsHtml = notifications.map(notification => `
                        <div class="notification-item-modal ${notification.is_unread ? 'unread' : ''}" 
                             onclick="markAsReadModal('${notification.id}')" 
                             style="padding: 1rem; border-bottom: 1px solid #eee; cursor: pointer; transition: background 0.2s;">
                            <div class="d-flex justify-content-between align-items-start">
                                <div style="flex: 1;">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <strong style="color: var(--text-primary);">${notification.title}</strong>
                                        ${notification.is_unread ? '<span class="badge bg-primary">Baru</span>' : ''}
                                    </div>
                                    <p style="color: var(--text-secondary); margin: 0.5rem 0; font-size: 0.9rem;">${notification.message}</p>
                                    <small style="color: var(--text-muted);">${notification.time_ago}</small>
                                </div>
                                <button class="btn btn-sm btn-outline-danger ms-2" onclick="deleteNotificationModal(event, '${notification.id}')" title="Hapus">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('');
                }

                Swal.fire({
                    title: 'Semua Notifikasi',
                    html: `
                        <div style="max-height: 400px; overflow-y: auto; text-align: left;">
                            ${notificationsHtml}
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary me-2" onclick="markAllAsReadModal()">Tandai Semua Dibaca</button>
                            <button class="btn btn-outline-secondary" onclick="refreshAllNotifications()">Refresh</button>
                        </div>
                    `,
                    showConfirmButton: false,
                    width: '600px',
                    customClass: {
                        container: 'notification-modal'
                    }
                });
            })
            .catch(error => {
                console.error('Error loading all notifications:', error);
                Toast.fire({
                    icon: 'error',
                    title: 'Gagal memuat notifikasi'
                });
            });
        }

        // Mark as read from modal
        function markAsReadModal(notificationId) {
            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh the modal
                    showAllNotifications();
                    // Update badges
                    updateNotificationBadge();
                    updateMenuBadges();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // Delete notification from modal
        function deleteNotificationModal(event, notificationId) {
            event.stopPropagation(); // Prevent marking as read
            
            Swal.fire({
                title: 'Hapus Notifikasi?',
                text: 'Notifikasi yang dihapus tidak dapat dikembalikan',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya, Hapus',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/api/notifications/${notificationId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAllNotifications(); // Refresh modal
                            updateNotificationBadge();
                            updateMenuBadges();
                            Toast.fire({
                                icon: 'success',
                                title: 'Notifikasi berhasil dihapus'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting notification:', error);
                        Toast.fire({
                            icon: 'error',
                            title: 'Gagal menghapus notifikasi'
                        });
                    });
                }
            });
        }

        // Mark all as read from modal
        function markAllAsReadModal() {
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAllNotifications(); // Refresh modal
                    updateNotificationBadge();
                    updateMenuBadges();
                    Toast.fire({
                        icon: 'success',
                        title: 'Semua notifikasi ditandai sebagai dibaca'
                    });
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        }

        // Refresh all notifications modal
        function refreshAllNotifications() {
            showAllNotifications();
        }

        // Format time ago function
        function formatTimeAgo(dateTime) {
            const now = new Date();
            const time = new Date(dateTime);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'Baru saja';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lalu`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lalu`;
            
            return time.toLocaleDateString('id-ID');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const userDropdown = document.getElementById('user-dropdown');
            const themeDropdown = document.getElementById('theme-dropdown');
            const themeSwitcher = document.querySelector('.theme-switcher');
            const notificationCenter = document.querySelector('.notification-center');
            const notificationDropdown = document.getElementById('notification-dropdown');
            
            if (!userMenu.contains(event.target)) {
                userDropdown.classList.remove('show');
            }
            
            if (!themeSwitcher.contains(event.target)) {
                themeDropdown.classList.remove('show');
            }
            
            if (!notificationCenter.contains(event.target)) {
                notificationDropdown.classList.remove('show');
            }
        });

        // Sidebar toggle event
        document.getElementById('sidebar-toggle').addEventListener('click', toggleSidebar);

        // SweetAlert configuration
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        // Show flash messages
        @if(session('success'))
            Toast.fire({
                icon: 'success',
                title: '{{ session('success') }}'
            });
        @endif

        @if(session('error'))
            Toast.fire({
                icon: 'error',
                title: '{{ session('error') }}'
            });
        @endif

        @if(session('warning'))
            Toast.fire({
                icon: 'warning',
                title: '{{ session('warning') }}'
            });
        @endif

        @if(session('info'))
            Toast.fire({
                icon: 'info',
                title: '{{ session('info') }}'
            });
        @endif

        // Validation errors
        @if($errors->any())
            let errorMessages = @json($errors->all());
            let errorText = errorMessages.join('<br>');
            Swal.fire({
                icon: 'error',
                title: 'Kesalahan Validasi',
                html: errorText,
                confirmButtonText: 'OK'
            });
        @endif

        // Pusher configuration
        @auth
        const pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
            cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
            forceTLS: true
        });

        console.log('Pusher initialized with key:', '{{ config('broadcasting.connections.pusher.key') }}');
        console.log('Pusher cluster:', '{{ config('broadcasting.connections.pusher.options.cluster') }}');

        // Subscribe to user-specific channel for real-time notifications
        const channel = pusher.subscribe('user.{{ auth()->id() }}');
        
        console.log('Subscribed to channel: user.{{ auth()->id() }}');
        
        // Debug Pusher connection
        pusher.connection.bind('connected', function() {
            console.log('Pusher connected successfully');
        });
        
        pusher.connection.bind('error', function(err) {
            console.error('Pusher connection error:', err);
        });
        
        channel.bind('pusher:subscription_succeeded', function() {
            console.log('Successfully subscribed to user channel');
        });
        
        channel.bind('pusher:subscription_error', function(err) {
            console.error('Channel subscription error:', err);
        });
        
        channel.bind('notification', function(data) {
            console.log('Legacy notification event received:', data);
            Toast.fire({
                icon: data.type || 'info',
                title: data.message
            });
        });
        @endauth

        // Real-time Badge Updates
        function updateMenuBadges() {
            fetch('/dashboard/data', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Update pending bookings badge for regular users
                const pendingBadge = document.getElementById('pending-bookings-badge');
                if (pendingBadge) {
                    if (data.pendingBookings > 0) {
                        pendingBadge.textContent = data.pendingBookings;
                        pendingBadge.style.display = 'inline-block';
                    } else {
                        pendingBadge.style.display = 'none';
                    }
                }

                // Update available rooms badge
                const roomsBadge = document.getElementById('available-rooms-badge');
                if (roomsBadge) {
                    roomsBadge.textContent = data.availableRooms;
                }
            })
            .catch(error => {
                console.error('Error updating badges:', error);
            });

            // Update admin badges if user is admin
            @if(auth()->check() && auth()->user()->isAdmin())
            fetch('/admin/dashboard/data', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Update admin badges
                const totalUsersBadge = document.getElementById('total-users-badge');
                if (totalUsersBadge && data.activeUsers) {
                    totalUsersBadge.textContent = data.activeUsers;
                }

                const totalRoomsBadge = document.getElementById('total-rooms-badge');
                if (totalRoomsBadge && data.totalRooms) {
                    totalRoomsBadge.textContent = data.totalRooms;
                }

                const pendingApprovalsBadge = document.getElementById('pending-approvals-badge');
                if (pendingApprovalsBadge) {
                    pendingApprovalsBadge.textContent = data.pendingApprovals || 0;
                    if (data.pendingApprovals > 0) {
                        pendingApprovalsBadge.style.display = 'inline-block';
                    }
                }

                const securityAlertsBadge = document.getElementById('security-alerts-badge');
                if (securityAlertsBadge && data.securityAlerts !== undefined) {
                    if (data.securityAlerts > 0) {
                        securityAlertsBadge.textContent = data.securityAlerts;
                        securityAlertsBadge.style.display = 'inline-block';
                    } else {
                        securityAlertsBadge.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error updating admin badges:', error);
            });
            @endif
        }

        // Update badges on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateMenuBadges();
            
            // Load initial notification count
            updateNotificationBadge();
            
            // Update badges every 30 seconds
            setInterval(updateMenuBadges, 30000);
            
            // Update notification badge every 15 seconds
            setInterval(function() {
                updateNotificationBadge();
            }, 15000);
        });

        // Listen for Pusher events to update badges
        @auth
        @if(config('broadcasting.default') === 'pusher')
        // Listen for booking events
        channel.bind('App\\Events\\BookingCreated', function(data) {
            updateMenuBadges();
        });

        channel.bind('App\\Events\\BookingStatusUpdated', function(data) {
            updateMenuBadges();
        });

        // Listen for real-time notifications
        channel.bind('notification.created', function(data) {
            console.log('New notification received:', data);
            
            // Show toast notification
            Toast.fire({
                icon: getNotificationIcon(data.type),
                title: data.title,
                text: data.message,
                timer: 5000
            });
            
            // Update notification badge immediately
            updateNotificationBadge();
            
            // Update menu badges
            updateMenuBadges();
        });

        // Also listen for the default Laravel broadcast event format
        channel.bind('App\\Events\\NotificationCreated', function(data) {
            console.log('NotificationCreated event received:', data);
            
            // Show toast notification
            Toast.fire({
                icon: getNotificationIcon(data.type),
                title: data.title,
                text: data.message,
                timer: 5000
            });
            
            // Update notification badge immediately
            updateNotificationBadge();
            
            // Update menu badges
            updateMenuBadges();
        });
        @endif
        @endauth

        // Get notification icon based on type
        function getNotificationIcon(type) {
            const iconMap = {
                'booking_created': 'info',
                'booking_submitted': 'success', 
                'booking_status_updated': 'info',
                'booking_approved': 'success',
                'booking_rejected': 'warning',
                'booking_cancelled': 'warning',
                'account_locked': 'error',
                'account_unlocked': 'success',
                'user_locked': 'warning',
                'user_unlocked': 'info',
                'security_alert': 'error'
            };
            return iconMap[type] || 'info';
        }
    </script>
    
    @stack('scripts')
</body>
</html>
