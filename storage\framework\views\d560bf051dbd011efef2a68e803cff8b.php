

<?php $__env->startSection('title', 'Detail Ruang Rapat - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.rooms.index')); ?>">Manajemen Ruang</a></li>
                    <li class="breadcrumb-item active"><?php echo e($room->name); ?></li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Detail Ruang Rapat</h1>
        </div>
        <div>
            <a href="<?php echo e(route('admin.rooms.edit', $room->id)); ?>" class="btn btn-warning me-2">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.rooms.index')); ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Room Information -->
        <div class="col-xl-8 col-lg-7">
            <!-- Basic Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Ruang</h6>
                    <div class="dropdown no-arrow">
                        <span class="badge <?php echo e($room->is_active ? 'bg-success' : 'bg-danger'); ?>">
                            <?php echo e($room->is_active ? 'Aktif' : 'Non-aktif'); ?>

                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Room Image -->
                        <?php if($room->image): ?>
                        <div class="col-md-4 mb-3">
                            <img src="<?php echo e(asset('storage/' . $room->image)); ?>" 
                                 alt="<?php echo e($room->name); ?>" 
                                 class="img-fluid rounded shadow-sm">
                        </div>
                        <?php endif; ?>
                        
                        <div class="col-md-<?php echo e($room->image ? '8' : '12'); ?>">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%" class="fw-bold">Nama Ruang:</td>
                                    <td><?php echo e($room->name); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Lokasi:</td>
                                    <td><?php echo e($room->location); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Kapasitas:</td>
                                    <td><?php echo e($room->capacity); ?> orang</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Tarif per Jam:</td>
                                    <td>
                                        <?php if($room->hourly_rate > 0): ?>
                                            Rp <?php echo e(number_format($room->hourly_rate, 0, ',', '.')); ?>

                                        <?php else: ?>
                                            Gratis
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge <?php echo e($room->is_active ? 'bg-success' : 'bg-danger'); ?>">
                                            <?php echo e($room->is_active ? 'Aktif' : 'Non-aktif'); ?>

                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if($room->description): ?>
                    <div class="mt-3">
                        <h6 class="fw-bold">Deskripsi:</h6>
                        <p class="text-muted"><?php echo e($room->description); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Facilities Card -->
            <?php if($room->facilities && $room->facilities->count() > 0): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Fasilitas</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $room->facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 col-sm-6 mb-2">
                            <span class="badge bg-info me-1">
                                <?php if($facility->icon): ?>
                                    <i class="bi <?php echo e($facility->icon); ?>"></i>
                                <?php else: ?>
                                    <i class="bi bi-check-circle"></i>
                                <?php endif; ?>
                                <?php echo e($facility->name); ?>

                            </span>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Bookings Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">10 Pemesanan Terakhir</h6>
                </div>
                <div class="card-body">
                    <?php if($room->bookings && $room->bookings->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Waktu</th>
                                    <th>Acara</th>
                                    <th>Pemesan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $room->bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($booking->start_time->format('d/m/Y')); ?></td>
                                    <td><?php echo e($booking->start_time->format('H:i')); ?> - <?php echo e($booking->end_time->format('H:i')); ?></td>
                                    <td>
                                        <strong><?php echo e($booking->title); ?></strong>
                                        <?php if($booking->description): ?>
                                        <br><small class="text-muted"><?php echo e(Str::limit($booking->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($booking->user->name); ?></td>
                                    <td>
                                        <?php switch($booking->status):
                                            case ('approved'): ?>
                                                <span class="badge bg-success">Disetujui</span>
                                                <?php break; ?>
                                            <?php case ('pending'): ?>
                                                <span class="badge bg-warning">Menunggu</span>
                                                <?php break; ?>
                                            <?php case ('rejected'): ?>
                                                <span class="badge bg-danger">Ditolak</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge bg-secondary"><?php echo e($booking->status); ?></span>
                                        <?php endswitch; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-calendar-x text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">Belum ada pemesanan untuk ruang ini</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Statistics Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistik Pemesanan</h6>
                </div>
                <div class="card-body">
                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pemesanan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_bookings']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['this_month']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-month text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Disetujui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['approved']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Menunggu Persetujuan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['pending']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <?php if($stats['revenue'] > 0): ?>
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Pendapatan Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($stats['revenue'], 0, ',', '.')); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-dollar text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aksi Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.rooms.edit', $room->id)); ?>" class="btn btn-warning btn-sm">
                            <i class="bi bi-pencil"></i> Edit Ruang
                        </a>
                        
                        <?php if($room->is_active): ?>
                        <form action="<?php echo e(route('admin.rooms.toggle-status', $room->id)); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100" 
                                    onclick="return confirm('Yakin ingin menonaktifkan ruang ini?')">
                                <i class="bi bi-x-circle"></i> Nonaktifkan
                            </button>
                        </form>
                        <?php else: ?>
                        <form action="<?php echo e(route('admin.rooms.toggle-status', $room->id)); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                <i class="bi bi-check-circle"></i> Aktifkan
                            </button>
                        </form>
                        <?php endif; ?>

                        <a href="<?php echo e(route('admin.bookings.index', ['room_id' => $room->id])); ?>" class="btn btn-info btn-sm">
                            <i class="bi bi-list-ul"></i> Lihat Semua Pemesanan
                        </a>
                        
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteRoom(<?php echo e($room->id); ?>)">
                            <i class="bi bi-trash"></i> Hapus Ruang
                        </button>
                    </div>
                </div>
            </div>

            <!-- Room Info Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Sistem</h6>
                </div>
                <div class="card-body">
                    <div class="small text-muted">
                        <div class="mb-1">
                            <strong>Dibuat:</strong> <?php echo e($room->created_at->format('d/m/Y H:i')); ?>

                        </div>
                        <div>
                            <strong>Terakhir diperbarui:</strong> <?php echo e($room->updated_at->format('d/m/Y H:i')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<script>
function deleteRoom(roomId) {
    Swal.fire({
        title: 'Hapus Ruang Rapat?',
        text: 'Tindakan ini tidak dapat dibatalkan. Semua data pemesanan terkait juga akan terhapus.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/rooms/${roomId}`;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '<?php echo e(csrf_token()); ?>';
            
            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            
            form.submit();
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Rapat\resources\views/admin/rooms/show.blade.php ENDPATH**/ ?>