<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

echo "=== PERBAIKAN USER VERIFIKASI ===\n";

// Cek user yang belum diverifikasi dan belum punya token
$unverifiedUsers = User::whereNull('email_verified_at')
                      ->whereNull('email_verification_token')
                      ->get();

echo "Jumlah user yang perlu diperbaiki: " . $unverifiedUsers->count() . "\n";

foreach ($unverifiedUsers as $user) {
    echo "\nMemperbarui user: {$user->email}\n";
    
    $verificationToken = Str::random(64);
    $expiresAt = Carbon::now()->addHours(24);
    
    $user->update([
        'email_verification_token' => $verificationToken,
        'email_verification_expires_at' => $expiresAt,
    ]);
    
    echo "Token baru: $verificationToken\n";
    echo "Expires: " . $expiresAt->toDateTimeString() . "\n";
    echo "URL verifikasi: " . url('/email/verify/' . $verificationToken) . "\n";
}

echo "\n=== SELESAI ===\n";
