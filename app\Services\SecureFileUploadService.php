<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SecureFileUploadService
{
    protected $allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    protected $allowedExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'txt', 'csv', 'xls', 'xlsx'
    ];

    protected $maxFileSize = 10 * 1024 * 1024; // 10MB

    protected $virusSignatures = [
        // Common virus/malware signatures
        '4D5A90', // PE executable header
        '504B03', // ZIP file (potential zip bomb)
        '377ABCAF271C', // 7-Zip
        'CAFEBABE', // Java class file
        '89504E47', // PNG with potential payload
    ];

    protected $dangerousPatterns = [
        // PHP patterns
        '/<\?php/i',
        '/<script/i',
        '/eval\s*\(/i',
        '/exec\s*\(/i',
        '/system\s*\(/i',
        '/passthru\s*\(/i',
        '/shell_exec\s*\(/i',
        '/base64_decode\s*\(/i',
        
        // ASP patterns
        '/<%/i',
        '/<script\s+runat\s*=\s*["\']server["\']/i',
        
        // JSP patterns
        '/<%@/i',
        '/<%!/i',
        '/<%=/i',
        
        // General dangerous patterns
        '/javascript:/i',
        '/vbscript:/i',
        '/on\w+\s*=/i',
        '/<iframe/i',
        '/<object/i',
        '/<embed/i'
    ];

    public function validateAndStore(UploadedFile $file, string $path = 'uploads'): array
    {
        // 1. Basic validation
        $this->validateBasics($file);

        // 2. MIME type validation
        $this->validateMimeType($file);

        // 3. File extension validation
        $this->validateExtension($file);

        // 4. File size validation
        $this->validateSize($file);

        // 5. Content validation
        $this->validateContent($file);

        // 6. Virus/malware scanning
        $this->scanForMalware($file);

        // 7. Generate secure filename
        $filename = $this->generateSecureFilename($file);

        // 8. Store file securely
        $storedPath = $this->storeSecurely($file, $path, $filename);

        return [
            'original_name' => $file->getClientOriginalName(),
            'stored_name' => $filename,
            'path' => $storedPath,
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension()
        ];
    }

    protected function validateBasics(UploadedFile $file): void
    {
        if (!$file->isValid()) {
            throw new \InvalidArgumentException('File upload gagal atau rusak.');
        }

        if ($file->getError() !== UPLOAD_ERR_OK) {
            throw new \InvalidArgumentException('Terjadi kesalahan saat upload file: ' . $file->getErrorMessage());
        }
    }

    protected function validateMimeType(UploadedFile $file): void
    {
        $mimeType = $file->getMimeType();
        
        if (!in_array($mimeType, $this->allowedMimeTypes)) {
            throw new \InvalidArgumentException("Tipe file tidak diperbolehkan: {$mimeType}");
        }

        // Additional MIME type validation using finfo
        $realMimeType = finfo_file(finfo_open(FILEINFO_MIME_TYPE), $file->getPathname());
        
        if ($realMimeType !== $mimeType) {
            throw new \InvalidArgumentException('File header tidak sesuai dengan ekstensi file.');
        }
    }

    protected function validateExtension(UploadedFile $file): void
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $this->allowedExtensions)) {
            throw new \InvalidArgumentException("Ekstensi file tidak diperbolehkan: {$extension}");
        }

        // Check for double extensions
        $filename = $file->getClientOriginalName();
        if (preg_match('/\.(php|asp|jsp|pl|py|sh|exe|bat|cmd|scr|vbs|js)\./i', $filename)) {
            throw new \InvalidArgumentException('File dengan ekstensi ganda tidak diperbolehkan.');
        }
    }

    protected function validateSize(UploadedFile $file): void
    {
        if ($file->getSize() > $this->maxFileSize) {
            $maxSizeMB = $this->maxFileSize / (1024 * 1024);
            throw new \InvalidArgumentException("Ukuran file terlalu besar. Maksimal {$maxSizeMB}MB.");
        }

        if ($file->getSize() === 0) {
            throw new \InvalidArgumentException('File kosong tidak diperbolehkan.');
        }
    }

    protected function validateContent(UploadedFile $file): void
    {
        $content = file_get_contents($file->getPathname());
        
        // Check for dangerous patterns in content
        foreach ($this->dangerousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new \InvalidArgumentException('File mengandung konten berbahaya.');
            }
        }

        // Check for null bytes (path traversal attempts)
        if (strpos($content, "\0") !== false) {
            throw new \InvalidArgumentException('File mengandung karakter null byte.');
        }

        // Check for excessive data (potential zip bomb)
        $compression_ratio = strlen($content) / $file->getSize();
        if ($compression_ratio > 50) { // If decompressed size is 50x original
            throw new \InvalidArgumentException('File terdeteksi sebagai zip bomb.');
        }
    }

    protected function scanForMalware(UploadedFile $file): void
    {
        $content = file_get_contents($file->getPathname());
        $hexContent = bin2hex($content);

        // Check for known virus signatures
        foreach ($this->virusSignatures as $signature) {
            if (stripos($hexContent, $signature) !== false) {
                throw new \InvalidArgumentException('File terdeteksi mengandung malware.');
            }
        }

        // Check for suspicious binary patterns
        if ($this->containsSuspiciousBinary($content)) {
            throw new \InvalidArgumentException('File mengandung pola binary yang mencurigakan.');
        }
    }

    protected function containsSuspiciousBinary(string $content): bool
    {
        // Check for high entropy (potential encrypted payload)
        $entropy = $this->calculateEntropy($content);
        if ($entropy > 7.5) { // Very high entropy
            return true;
        }

        // Check for suspicious strings in binary
        $suspiciousStrings = [
            'eval', 'exec', 'system', 'shell', 'cmd', 'powershell',
            'wget', 'curl', 'download', 'payload', 'exploit'
        ];

        foreach ($suspiciousStrings as $string) {
            if (stripos($content, $string) !== false) {
                return true;
            }
        }

        return false;
    }

    protected function calculateEntropy(string $data): float
    {
        $counts = array_count_values(str_split($data));
        $entropy = 0;
        $length = strlen($data);

        foreach ($counts as $count) {
            $probability = $count / $length;
            $entropy -= $probability * log($probability, 2);
        }

        return $entropy;
    }

    protected function generateSecureFilename(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        // Generate secure random filename
        $randomName = Str::random(32);
        
        // Add timestamp for uniqueness
        $timestamp = time();
        
        return "{$randomName}_{$timestamp}.{$extension}";
    }

    protected function storeSecurely(UploadedFile $file, string $path, string $filename): string
    {
        // Create secure directory structure
        $yearMonth = date('Y/m');
        $fullPath = "{$path}/{$yearMonth}";

        // Store with generated filename
        $storedPath = $file->storeAs($fullPath, $filename, 'public');

        // Set secure permissions
        $fullFilePath = storage_path("app/public/{$storedPath}");
        if (file_exists($fullFilePath)) {
            chmod($fullFilePath, 0644); // Read-write for owner, read-only for others
        }

        return $storedPath;
    }

    public function deleteFile(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }

        return false;
    }

    public function getSecureUrl(string $path): string
    {
        // Return URL through secure controller that validates access
        return route('secure.file', ['path' => encrypt($path)]);
    }
}
