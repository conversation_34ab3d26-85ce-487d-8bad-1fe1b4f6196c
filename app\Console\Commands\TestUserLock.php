<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class TestUserLock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:user-lock {user_id} {action}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test user lock/unlock functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $action = $this->argument('action');
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        $this->info("Before {$action}:");
        $this->info("locked_until: " . ($user->locked_until ?: 'null'));
        $this->info("isLocked(): " . ($user->isLocked() ? 'true' : 'false'));
        $this->info("account_locked: " . ($user->account_locked ? 'true' : 'false'));
        
        if ($action === 'lock') {
            $user->lockAccount();
            $this->info("User locked!");
        } elseif ($action === 'unlock') {
            $user->unlockAccount();
            $this->info("User unlocked!");
        } else {
            $this->error("Invalid action. Use 'lock' or 'unlock'");
            return;
        }
        
        $user = $user->fresh();
        
        $this->info("After {$action}:");
        $this->info("locked_until: " . ($user->locked_until ?: 'null'));
        $this->info("isLocked(): " . ($user->isLocked() ? 'true' : 'false'));
        $this->info("account_locked: " . ($user->account_locked ? 'true' : 'false'));
    }
}
