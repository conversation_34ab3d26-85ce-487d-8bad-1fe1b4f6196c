<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifikasi Email - Sistem Pemesanan Ruang Rapat</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 0;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .email-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .email-body {
            padding: 40px 30px;
        }
        
        .email-body h2 {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .email-body p {
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s ease;
        }
        
        .verify-button:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .email-footer p {
            margin: 0;
            font-size: 14px;
            color: #6c757d;
        }
        
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .security-notice h3 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .security-notice p {
            color: #856404;
            margin: 0;
            font-size: 14px;
        }
        
        .alternative-link {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-header, .email-body, .email-footer {
                padding: 20px;
            }
            
            .verify-button {
                display: block;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1>🏢 Sistem Pemesanan Ruang Rapat</h1>
            <p>Verifikasi Email Anda</p>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <h2>Halo, {{ $user->name }}!</h2>
            
            <p>Terima kasih telah mendaftar di Sistem Pemesanan Ruang Rapat. Untuk mengaktifkan akun Anda, silakan verifikasi alamat email Anda dengan mengklik tombol di bawah ini:</p>
            
            <div class="button-container">
                <a href="{{ $verificationUrl }}" class="verify-button">
                    ✅ Verifikasi Email Sekarang
                </a>
            </div>
            
            <p>Jika tombol di atas tidak bekerja, Anda dapat menyalin dan menempelkan tautan berikut ke browser Anda:</p>
            
            <div class="alternative-link">
                {{ $verificationUrl }}
            </div>
            
            <div class="security-notice">
                <h3>🔒 Pemberitahuan Keamanan</h3>
                <p>
                    • Tautan verifikasi ini akan kedaluwarsa dalam 24 jam<br>
                    • Jangan bagikan tautan ini kepada siapa pun<br>
                    • Jika Anda tidak mendaftar di sistem kami, abaikan email ini
                </p>
            </div>
            
            <p>Setelah verifikasi berhasil, Anda dapat:</p>
            <ul>
                <li>Login ke sistem pemesanan ruang rapat</li>
                <li>Melihat jadwal ruang rapat yang tersedia</li>
                <li>Melakukan pemesanan ruang rapat</li>
                <li>Mengelola jadwal rapat Anda</li>
            </ul>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <p>
                <strong>Sistem Pemesanan Ruang Rapat</strong><br>
                Email ini dikirim secara otomatis, mohon jangan membalas email ini.<br>
                © {{ date('Y') }} Sistem Pemesanan Ruang Rapat. Semua hak dilindungi.
            </p>
        </div>
    </div>
</body>
</html>
