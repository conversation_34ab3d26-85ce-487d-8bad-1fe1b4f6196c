@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON>')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><PERSON><PERSON></h1>
            <p class="text-muted">Monitor aktivitas keamanan sistem</p>
        </div>
        <div>
            <button class="btn btn-warning" onclick="clearOldLogs()">
                <i class="bi bi-trash me-1"></i> Be<PERSON><PERSON><PERSON>g <PERSON>
            </button>
        </div>
    </div>

    <!-- Filter & Search -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.security.logs') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Cari</label>
                        <input type="text" class="form-control" name="search" 
                               value="{{ request('search') }}" placeholder="IP, user agent, deskripsi...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Aksi</label>
                        <select class="form-select" name="action">
                            <option value="">Semua Aksi</option>
                            <option value="login" {{ request('action') === 'login' ? 'selected' : '' }}>Login</option>
                            <option value="logout" {{ request('action') === 'logout' ? 'selected' : '' }}>Logout</option>
                            <option value="booking" {{ request('action') === 'booking' ? 'selected' : '' }}>Booking</option>
                            <option value="failed_login" {{ request('action') === 'failed_login' ? 'selected' : '' }}>Login Gagal</option>
                            <option value="account_locked" {{ request('action') === 'account_locked' ? 'selected' : '' }}>Akun Dikunci</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Severity</label>
                        <select class="form-select" name="severity">
                            <option value="">Semua Severity</option>
                            <option value="info" {{ request('severity') === 'info' ? 'selected' : '' }}>Info</option>
                            <option value="warning" {{ request('severity') === 'warning' ? 'selected' : '' }}>Warning</option>
                            <option value="error" {{ request('severity') === 'error' ? 'selected' : '' }}>Error</option>
                            <option value="critical" {{ request('severity') === 'critical' ? 'selected' : '' }}>Critical</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Tanggal Dari</label>
                        <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Tanggal Sampai</label>
                        <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="d-flex justify-content-between align-items-center mb-2">
        <h5 class="mb-0">Statistik Real-time</h5>
        <small class="text-muted">
            <i class="bi bi-clock me-1"></i>
            Terakhir diperbarui: <span id="last-updated">{{ now()->format('H:i:s') }}</span>
        </small>
    </div>
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Log Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-logs-today">{{ $stats['total_logs_today'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Login Gagal Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="failed-logins-today">{{ $stats['failed_logins_today'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Akun Terkunci
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="locked-accounts">{{ $stats['locked_accounts'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-lock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                IP Unik Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="unique-ips-today">{{ $stats['unique_ips_today'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Logs Table -->
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Waktu</th>
                            <th>Pengguna</th>
                            <th>Aksi</th>
                            <th>Severity</th>
                            <th>IP Address</th>
                            <th>Details</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($securityLogs as $log)
                        <tr>
                            <td>
                                <small>{{ $log->created_at->format('d/m/Y') }}</small><br>
                                <small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                            </td>
                            <td>
                                @if($log->user)
                                    <div>
                                        <strong>{{ $log->user->name }}</strong><br>
                                        <small class="text-muted">{{ $log->user->email }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">Guest</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge 
                                    @if($log->action === 'login') bg-success
                                    @elseif($log->action === 'logout') bg-info
                                    @elseif($log->action === 'failed_login') bg-danger
                                    @elseif($log->action === 'account_locked') bg-warning
                                    @elseif($log->action === 'booking') bg-primary
                                    @else bg-secondary
                                    @endif">
                                    {{ ucfirst(str_replace('_', ' ', $log->action)) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge 
                                    @if($log->severity === 'info') bg-info
                                    @elseif($log->severity === 'warning') bg-warning
                                    @elseif($log->severity === 'error') bg-danger
                                    @elseif($log->severity === 'critical') bg-dark
                                    @else bg-secondary
                                    @endif">
                                    {{ ucfirst($log->severity) }}
                                </span>
                            </td>
                            <td>
                                <span class="font-monospace">{{ $log->ip_address }}</span>
                                @if($log->ip_address && $log->ip_address !== '127.0.0.1')
                                    <br><small class="text-muted">{{ $log->location ?? 'Unknown' }}</small>
                                @endif
                            </td>
                            <td>
                                <div style="max-width: 300px;">
                                    @if($log->details && is_array($log->details))
                                        {{ Str::limit(json_encode($log->details), 100) }}
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="showLogDetail({{ $log->id }})" title="Lihat Detail">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-shield-exclamation fs-1"></i>
                                    <p class="mt-2">Tidak ada log keamanan ditemukan</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            @if($securityLogs->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $securityLogs->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<style>
/* Sembunyikan icon panah pada pagination */
.page-link i.bi, .pagination i.bi {
    display: none !important;
}

/* Sembunyikan icon panah SVG Tailwind pada pagination */
svg.w-5.h-5 {
    display: none !important;
}
</style>

@push('scripts')
<script>
function showLogDetail(logId) {
    console.log('showLogDetail called with logId:', logId);
    
    // Check if SweetAlert2 is available
    if (typeof Swal === 'undefined') {
        console.error('SweetAlert2 is not loaded!');
        alert('Error: SweetAlert2 library tidak tersedia');
        return;
    }
    
    // Show loading dengan SweetAlert2
    Swal.fire({
        title: 'Memuat Detail Log...',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    console.log('Fetching log detail for ID:', logId);
    
    // Fetch detail
    fetch(`/admin/security/logs/${logId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
                const log = data.log;
                const createdAt = new Date(log.created_at).toLocaleString('id-ID', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                
                // Determine badge color based on severity
                let severityBadge;
                switch(log.severity) {
                    case 'critical':
                        severityBadge = '<span class="badge bg-danger">Critical</span>';
                        break;
                    case 'warning':
                        severityBadge = '<span class="badge bg-warning">Warning</span>';
                        break;
                    case 'info':
                        severityBadge = '<span class="badge bg-info">Info</span>';
                        break;
                    default:
                        severityBadge = '<span class="badge bg-secondary">' + log.severity + '</span>';
                }
                
                Swal.fire({
                    title: 'Detail Log Keamanan',
                    html: `
                        <div class="text-start">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>ID:</strong></div>
                                <div class="col-sm-8">#${log.id}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Waktu:</strong></div>
                                <div class="col-sm-8">${createdAt}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>User:</strong></div>
                                <div class="col-sm-8">${log.user ? log.user.name + ' (' + log.user.email + ')' : 'System'}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Aksi:</strong></div>
                                <div class="col-sm-8"><code>${log.action}</code></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Level:</strong></div>
                                <div class="col-sm-8">${severityBadge}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>IP Address:</strong></div>
                                <div class="col-sm-8"><code>${log.ip_address || 'N/A'}</code></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>User Agent:</strong></div>
                                <div class="col-sm-8"><small class="text-muted">${log.user_agent || 'N/A'}</small></div>
                            </div>
                            ${log.details ? `
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Details:</strong></div>
                                <div class="col-sm-8">
                                    <div class="bg-light p-2 rounded">
                                        <pre style="white-space: pre-wrap; font-size: 0.875em;">${typeof log.details === 'object' ? JSON.stringify(log.details, null, 2) : log.details}</pre>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    `,
                    width: '600px',
                    confirmButtonText: 'Tutup',
                    confirmButtonColor: '#6c757d'
                });
        } else {
            console.error('Response success false:', data);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: data.message || 'Gagal memuat detail log'
            });
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Terjadi kesalahan sistem: ' + error.message
        });
    });
}

function clearOldLogs() {
    // Ambil filter dari form
    const search = document.querySelector('input[name="search"]').value;
    const action = document.querySelector('select[name="action"]').value;
    const severity = document.querySelector('select[name="severity"]').value;
    const date_from = document.querySelector('input[name="date_from"]').value;
    const date_to = document.querySelector('input[name="date_to"]').value;

    Swal.fire({
        title: 'Bersihkan Log',
        html: `
            <div class="mb-3">
                <label class="form-label">Hapus log berdasarkan filter saat ini?</label>
                <div class="alert alert-warning small mt-2">Seluruh log yang sesuai filter pencarian saat ini akan dihapus <b>permanen</b>!</div>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal',
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Menghapus Log...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Kirim filter ke backend
            const formData = new FormData();
            formData.append('search', search);
            formData.append('action', action);
            formData.append('severity', severity);
            formData.append('date_from', date_from);
            formData.append('date_to', date_to);

            fetch('/admin/security/logs/cleanup', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

// Update statistik secara real-time
function updateStats() {
    fetch('/admin/security/logs/data', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.stats) {
            // Update statistik cards
            document.getElementById('total-logs-today').textContent = data.stats.total_logs_today;
            document.getElementById('failed-logins-today').textContent = data.stats.failed_logins_today;
            document.getElementById('locked-accounts').textContent = data.stats.locked_accounts;
            document.getElementById('unique-ips-today').textContent = data.stats.unique_ips_today;
            
            // Update last updated time
            const now = new Date().toLocaleTimeString('id-ID');
            document.getElementById('last-updated').textContent = now;
            console.log('Stats updated at:', now);
        }
    })
    .catch(error => {
        console.error('Error updating stats:', error);
        // Show error indicator
        document.getElementById('last-updated').innerHTML = '<span class="text-danger">Error</span>';
    });
}

// Auto refresh stats setiap 30 detik
setInterval(updateStats, 30000);

// Initial stats update after page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateStats, 2000); // Wait 2 seconds after page load
});
</script>
@endpush
@endsection
