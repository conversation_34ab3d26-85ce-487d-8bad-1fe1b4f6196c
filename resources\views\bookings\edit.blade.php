@extends('layouts.dashboard')

@section('title', '<PERSON>')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.index') }}">P<PERSON><PERSON><PERSON></a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.show', $booking->id) }}">Detail</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Edit <PERSON></h1>
        </div>
        <div>
            <a href="{{ route('bookings.show', $booking->id) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i> Kembali
            </a>
        </div>
    </div>

    @if($booking->status !== 'pending')
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-1"></i>
            Pemesanan yang sudah disetujui atau ditolak tidak dapat diedit. Silakan hubungi admin jika perlu melakukan perubahan.
        </div>
    @else
        <form action="{{ route('bookings.update', $booking->id) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row">
                <!-- Form Fields -->
                <div class="col-xl-8 col-lg-7">
                    <!-- Basic Information -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Informasi Acara</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Judul Acara <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                               id="title" name="title" value="{{ old('title', $booking->title) }}" 
                                               placeholder="Contoh: Rapat Evaluasi Bulanan" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('date') is-invalid @enderror" 
                                               id="date" name="date" value="{{ old('date', $booking->date) }}" 
                                               min="{{ date('Y-m-d') }}" required>
                                        @error('date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label">Waktu Mulai <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                               id="start_time" name="start_time" value="{{ old('start_time', $booking->start_time) }}" 
                                               required>
                                        @error('start_time')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="end_time" class="form-label">Waktu Selesai <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                               id="end_time" name="end_time" value="{{ old('end_time', $booking->end_time) }}" 
                                               required>
                                        @error('end_time')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="attendees" class="form-label">Jumlah Peserta <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('attendees') is-invalid @enderror" 
                                               id="attendees" name="attendees" value="{{ old('attendees', $booking->attendees) }}" 
                                               min="1" max="{{ $booking->meetingRoom->capacity }}" required>
                                        @error('attendees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Maksimal {{ $booking->meetingRoom->capacity }} orang</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="meeting_room_id" class="form-label">Ruang Rapat <span class="text-danger">*</span></label>
                                        <select class="form-select @error('meeting_room_id') is-invalid @enderror" 
                                                id="meeting_room_id" name="meeting_room_id" required>
                                            @foreach($meetingRooms as $room)
                                                <option value="{{ $room->id }}" 
                                                        {{ old('meeting_room_id', $booking->meeting_room_id) == $room->id ? 'selected' : '' }}
                                                        data-capacity="{{ $room->capacity }}">
                                                    {{ $room->name }} ({{ $room->capacity }} orang)
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('meeting_room_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Deskripsi Acara</label>
                                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                                  id="description" name="description" rows="4" 
                                                  placeholder="Jelaskan tujuan dan agenda acara...">{{ old('description', $booking->description) }}</textarea>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="card shadow mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('bookings.show', $booking->id) }}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-1"></i> Batal
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-1"></i> Simpan Perubahan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Side Information -->
                <div class="col-xl-4 col-lg-5">
                    <!-- Current Room Info -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Ruang Saat Ini</h6>
                        </div>
                        <div class="card-body" id="currentRoomInfo">
                            <div class="text-center">
                                @if($booking->meetingRoom->image)
                                    <img src="{{ Storage::url($booking->meetingRoom->image) }}" 
                                         alt="{{ $booking->meetingRoom->name }}" 
                                         class="img-fluid rounded mb-3" style="max-height: 150px;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                                         style="height: 150px;">
                                        <i class="bi bi-building text-muted" style="font-size: 2rem;"></i>
                                    </div>
                                @endif
                                
                                <h6>{{ $booking->meetingRoom->name }}</h6>
                                <p class="text-muted small">{{ $booking->meetingRoom->description }}</p>
                                
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <div class="h5 text-primary">{{ $booking->meetingRoom->capacity }}</div>
                                            <small class="text-muted">Kapasitas</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="h5 text-info">{{ $booking->meetingRoom->location }}</div>
                                        <small class="text-muted">Lokasi</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Availability Check -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Cek Ketersediaan</h6>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-outline-info w-100" onclick="checkAvailability()">
                                <i class="bi bi-search me-1"></i> Cek Ketersediaan
                            </button>
                            <div id="availabilityResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tips</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="bi bi-lightbulb text-warning me-2"></i>
                                    <small>Pastikan jumlah peserta tidak melebihi kapasitas ruang</small>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-clock text-info me-2"></i>
                                    <small>Pemesanan minimal 1 hari sebelum acara</small>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-calendar-check text-success me-2"></i>
                                    <small>Cek ketersediaan ruang sebelum menyimpan</small>
                                </li>
                                <li class="mb-0">
                                    <i class="bi bi-pencil text-primary me-2"></i>
                                    <small>Berikan deskripsi yang jelas untuk mempermudah persetujuan</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    @endif
</div>

@push('scripts')
<script>
// Update attendees max when room changes
document.getElementById('meeting_room_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const capacity = selectedOption.dataset.capacity;
    const attendeesInput = document.getElementById('attendees');
    
    attendeesInput.max = capacity;
    attendeesInput.parentElement.querySelector('.form-text').textContent = `Maksimal ${capacity} orang`;
    
    // Validate current attendees value
    if (parseInt(attendeesInput.value) > parseInt(capacity)) {
        attendeesInput.value = capacity;
    }
});

// Validate time
document.getElementById('start_time').addEventListener('change', validateTime);
document.getElementById('end_time').addEventListener('change', validateTime);

function validateTime() {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    
    if (startTime && endTime) {
        if (startTime >= endTime) {
            Swal.fire('Error!', 'Waktu selesai harus lebih besar dari waktu mulai.', 'error');
            document.getElementById('end_time').value = '';
        }
    }
}

// Check availability
function checkAvailability() {
    const date = document.getElementById('date').value;
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const roomId = document.getElementById('meeting_room_id').value;
    
    if (!date || !startTime || !endTime || !roomId) {
        Swal.fire('Error!', 'Silakan lengkapi tanggal, waktu, dan ruang terlebih dahulu.', 'error');
        return;
    }
    
    const resultDiv = document.getElementById('availabilityResult');
    resultDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2"><small>Mengecek ketersediaan...</small></div>
        </div>
    `;
    
    fetch('/bookings/check-availability', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            date: date,
            start_time: startTime,
            end_time: endTime,
            meeting_room_id: roomId,
            exclude_booking_id: {{ $booking->id }}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.available) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-1"></i>
                    <strong>Tersedia!</strong><br>
                    Ruang dapat dipesan pada waktu tersebut.
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle me-1"></i>
                    <strong>Tidak Tersedia!</strong><br>
                    ${data.message || 'Ruang sudah dipesan pada waktu tersebut.'}
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-1"></i>
                Gagal mengecek ketersediaan.
            </div>
        `;
    });
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const attendees = parseInt(document.getElementById('attendees').value);
    const capacity = parseInt(document.getElementById('meeting_room_id').options[document.getElementById('meeting_room_id').selectedIndex].dataset.capacity);
    
    if (startTime >= endTime) {
        e.preventDefault();
        Swal.fire('Error!', 'Waktu selesai harus lebih besar dari waktu mulai.', 'error');
        return;
    }
    
    if (attendees > capacity) {
        e.preventDefault();
        Swal.fire('Error!', 'Jumlah peserta melebihi kapasitas ruang.', 'error');
        return;
    }
    
    // Show confirmation
    e.preventDefault();
    Swal.fire({
        title: 'Simpan Perubahan?',
        text: 'Perubahan akan menunggu persetujuan admin kembali.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, Simpan!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            this.submit();
        }
    });
});

// Auto-check availability when time changes
document.getElementById('date').addEventListener('change', delayedAvailabilityCheck);
document.getElementById('start_time').addEventListener('change', delayedAvailabilityCheck);
document.getElementById('end_time').addEventListener('change', delayedAvailabilityCheck);
document.getElementById('meeting_room_id').addEventListener('change', delayedAvailabilityCheck);

let availabilityTimeout;
function delayedAvailabilityCheck() {
    clearTimeout(availabilityTimeout);
    document.getElementById('availabilityResult').innerHTML = '';
    
    availabilityTimeout = setTimeout(() => {
        const date = document.getElementById('date').value;
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;
        const roomId = document.getElementById('meeting_room_id').value;
        
        if (date && startTime && endTime && roomId) {
            checkAvailability();
        }
    }, 1000);
}
</script>
@endpush
@endsection
