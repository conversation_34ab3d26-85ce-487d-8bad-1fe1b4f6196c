<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Terverifikasi - Sistem Pemesanan Ruang <PERSON>at</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON>goe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .success-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
            padding: 40px;
            text-align: center;
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 2rem;
            animation: bounceIn 1s ease-out 0.3s both;
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .checkmark-animation {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }
        
        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }
        
        .countdown-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="bi bi-check-lg"></i>
            </div>
            
            <h2 class="mb-4 fw-bold text-success">Email Berhasil Diverifikasi!</h2>
            
            <p class="text-muted mb-4">
                Selamat! Email Anda telah berhasil diverifikasi. 
                Sekarang Anda dapat menggunakan semua fitur sistem pemesanan ruang rapat.
            </p>
            
            <div class="alert alert-success" role="alert">
                <i class="bi bi-shield-check me-2"></i>
                Akun Anda sekarang telah terverifikasi dan aman
            </div>
            
            <div class="d-grid gap-3 mt-4">
                <a href="{{ route('login') }}" class="btn btn-gradient">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    Login Sekarang
                </a>
            </div>
            
            <div class="countdown-text">
                Anda akan dialihkan ke halaman login dalam <span id="countdown">5</span> detik...
            </div>
            
            <div class="mt-4 pt-4 border-top">
                <p class="text-muted small mb-0">
                    <i class="bi bi-shield-check me-1"></i>
                    Sistem Pemesanan Ruang Rapat
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Success animation
        Swal.fire({
            icon: 'success',
            title: 'Email Terverifikasi!',
            text: 'Akun Anda telah berhasil diverifikasi.',
            showConfirmButton: false,
            timer: 2000,
            toast: true,
            position: 'top-end'
        });

        // Countdown timer
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '{{ route("login") }}';
            }
        }, 1000);

        // Allow user to cancel auto-redirect by clicking anywhere
        document.addEventListener('click', () => {
            clearInterval(timer);
            countdownElement.parentElement.style.display = 'none';
        });
    </script>
</body>
</html>
