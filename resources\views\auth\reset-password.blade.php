@extends('layouts.app')

@section('title', 'Reset Password - Sistem Pemesanan Ruang Rapat')

@push('styles')
<style>
    .reset-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        background: linear-gradient(135deg, var(--error-color), #b91c1c);
        position: relative;
        overflow: hidden;
    }

    .reset-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="300" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    .reset-card {
        max-width: 500px;
        width: 100%;
        border-radius: 2rem;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        z-index: 1;
        position: relative;
        background: var(--bg-primary);
    }

    .reset-header {
        background: linear-gradient(135deg, var(--error-color), #b91c1c);
        color: white;
        padding: 3rem 2rem 2rem;
        text-align: center;
        position: relative;
    }

    .reset-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
        animation: pulse 2s ease-in-out infinite;
    }

    .reset-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .reset-subtitle {
        opacity: 0.9;
        font-size: 1rem;
    }

    .reset-content {
        padding: 2rem;
    }

    .security-info {
        background: var(--bg-secondary);
        border: 2px solid var(--border-color);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .security-info h4 {
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .security-info ul {
        margin: 0;
        padding-left: 1.5rem;
        color: var(--text-secondary);
    }

    .security-info li {
        margin-bottom: 0.5rem;
    }

    .password-strength {
        margin-top: 1rem;
    }

    .strength-meter {
        height: 4px;
        background: var(--border-color);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .strength-fill {
        height: 100%;
        border-radius: 2px;
        transition: all 0.3s;
        width: 0%;
    }

    .strength-weak { 
        background: var(--error-color); 
        width: 25%; 
    }

    .strength-medium { 
        background: var(--warning-color); 
        width: 50%; 
    }

    .strength-strong { 
        background: var(--success-color); 
        width: 75%; 
    }

    .strength-very-strong { 
        background: var(--success-color); 
        width: 100%; 
    }

    .strength-text {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .requirements-list {
        margin-top: 1rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
    }

    .requirement {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        padding: 0.25rem 0;
    }

    .requirement-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
    }

    .requirement.met .requirement-icon {
        background: var(--success-color);
    }

    .requirement:not(.met) .requirement-icon {
        background: var(--border-color);
        color: var(--text-muted);
    }

    .requirement.met {
        color: var(--success-color);
    }

    .btn-reset {
        width: 100%;
        height: 3.5rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 1rem;
        background: linear-gradient(135deg, var(--error-color), #b91c1c);
        border: none;
        color: white;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        margin-top: 1rem;
    }

    .btn-reset:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px rgba(220, 38, 38, 0.4);
    }

    .btn-reset:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .back-link {
        text-align: center;
        margin-top: 1.5rem;
    }

    .back-link a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s;
    }

    .back-link a:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .reset-container {
            padding: 1rem;
        }
        
        .reset-header {
            padding: 2rem 1.5rem 1.5rem;
        }
        
        .reset-content {
            padding: 1.5rem;
        }
        
        .reset-title {
            font-size: 1.5rem;
        }
        
        .requirements-list {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="reset-container">
    <div class="reset-card">
        <div class="reset-header">
            <div class="reset-icon">
                <i class="bi bi-shield-lock"></i>
            </div>
            <h1 class="reset-title">Reset Password</h1>
            <p class="reset-subtitle">Buat password baru yang aman untuk akun Anda</p>
        </div>

        <div class="reset-content">
            <div class="security-info">
                <h4>
                    <i class="bi bi-info-circle"></i>
                    Persyaratan Password Baru
                </h4>
                <ul>
                    <li>Minimal 8 karakter</li>
                    <li>Mengandung huruf besar (A-Z)</li>
                    <li>Mengandung huruf kecil (a-z)</li>
                    <li>Mengandung angka (0-9)</li>
                    <li>Mengandung simbol (@$!%*?&)</li>
                </ul>
            </div>

            <form action="{{ route('password.update') }}" method="POST" id="reset-form">
                @csrf
                <input type="hidden" name="token" value="{{ $token }}">

                <div class="input-group">
                    <i class="bi bi-lock input-icon"></i>
                    <input type="password" class="form-control" name="password" 
                           placeholder="Password Baru" required id="password"
                           autocomplete="new-password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password', this)">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>

                <div class="password-strength">
                    <div class="strength-meter">
                        <div class="strength-fill" id="strength-fill"></div>
                    </div>
                    <div class="strength-text" id="strength-text">Masukkan password</div>
                    
                    <div class="requirements-list">
                        <div class="requirement" id="req-length">
                            <span class="requirement-icon">✓</span>
                            <span>Minimal 8 karakter</span>
                        </div>
                        <div class="requirement" id="req-upper">
                            <span class="requirement-icon">✓</span>
                            <span>Huruf besar</span>
                        </div>
                        <div class="requirement" id="req-lower">
                            <span class="requirement-icon">✓</span>
                            <span>Huruf kecil</span>
                        </div>
                        <div class="requirement" id="req-number">
                            <span class="requirement-icon">✓</span>
                            <span>Angka</span>
                        </div>
                        <div class="requirement" id="req-symbol">
                            <span class="requirement-icon">✓</span>
                            <span>Simbol</span>
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <i class="bi bi-lock input-icon"></i>
                    <input type="password" class="form-control" name="password_confirmation" 
                           placeholder="Konfirmasi Password Baru" required id="password-confirm"
                           autocomplete="new-password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password-confirm', this)">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>

                <button type="submit" class="btn-reset" id="submit-btn" disabled>
                    <span class="btn-text">Reset Password</span>
                    <div class="loading" style="display: none;"></div>
                </button>
            </form>

            <div class="back-link">
                <a href="{{ route('login') }}">
                    <i class="bi bi-arrow-left"></i>
                    Kembali ke Login
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function togglePassword(inputId, button) {
        const input = document.getElementById(inputId);
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }
    }

    // Password strength checker
    const passwordInput = document.getElementById('password');
    const confirmInput = document.getElementById('password-confirm');
    const strengthFill = document.getElementById('strength-fill');
    const strengthText = document.getElementById('strength-text');
    const submitBtn = document.getElementById('submit-btn');

    const requirements = {
        length: { element: document.getElementById('req-length'), test: (p) => p.length >= 8 },
        upper: { element: document.getElementById('req-upper'), test: (p) => /[A-Z]/.test(p) },
        lower: { element: document.getElementById('req-lower'), test: (p) => /[a-z]/.test(p) },
        number: { element: document.getElementById('req-number'), test: (p) => /\d/.test(p) },
        symbol: { element: document.getElementById('req-symbol'), test: (p) => /[!@#$%^&*(),.?":{}|<>]/.test(p) }
    };

    function checkPasswordStrength() {
        const password = passwordInput.value;
        const confirm = confirmInput.value;
        
        let score = 0;
        let metRequirements = 0;

        // Check each requirement
        Object.values(requirements).forEach(req => {
            const met = req.test(password);
            req.element.classList.toggle('met', met);
            if (met) {
                score += 20;
                metRequirements++;
            }
        });

        // Update strength meter
        strengthFill.className = 'strength-fill';
        if (score >= 80) {
            strengthFill.classList.add('strength-very-strong');
            strengthText.textContent = 'Password sangat kuat';
            strengthText.style.color = 'var(--success-color)';
        } else if (score >= 60) {
            strengthFill.classList.add('strength-strong');
            strengthText.textContent = 'Password kuat';
            strengthText.style.color = 'var(--success-color)';
        } else if (score >= 40) {
            strengthFill.classList.add('strength-medium');
            strengthText.textContent = 'Password sedang';
            strengthText.style.color = 'var(--warning-color)';
        } else if (score > 0) {
            strengthFill.classList.add('strength-weak');
            strengthText.textContent = 'Password lemah';
            strengthText.style.color = 'var(--error-color)';
        } else {
            strengthText.textContent = 'Masukkan password';
            strengthText.style.color = 'var(--text-muted)';
        }

        // Enable submit button if all requirements met and passwords match
        const allRequirementsMet = metRequirements === 5;
        const passwordsMatch = password === confirm && password.length > 0;
        
        submitBtn.disabled = !(allRequirementsMet && passwordsMatch);
    }

    passwordInput.addEventListener('input', checkPasswordStrength);
    confirmInput.addEventListener('input', checkPasswordStrength);

    // Form submission with loading
    document.getElementById('reset-form').addEventListener('submit', function(e) {
        const btnText = submitBtn.querySelector('.btn-text');
        const loading = submitBtn.querySelector('.loading');

        btnText.style.display = 'none';
        loading.style.display = 'block';
        submitBtn.disabled = true;
    });
</script>
@endpush
