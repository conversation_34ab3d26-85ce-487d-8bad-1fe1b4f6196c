<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\MeetingRoom;
use App\Models\SecurityLog;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display dashboard
     */
    public function index()
    {
        $user = auth()->user();
        
        // Get user's bookings
        $userBookings = Booking::where('user_id', $user->id)
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'desc')
            ->take(5)
            ->get();

        // Get upcoming bookings
        $upcomingBookings = Booking::where('user_id', $user->id)
            ->where('start_time', '>', Carbon::now())
            ->where('status', 'approved')
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'asc')
            ->take(3)
            ->get();

        // Get today's bookings
        $todayBookings = Booking::where('user_id', $user->id)
            ->whereDate('start_time', Carbon::today())
            ->where('status', 'approved')
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'asc')
            ->get();

        // Get available rooms count
        $availableRooms = MeetingRoom::active()->count();

        // Get pending bookings count (for user)
        $pendingBookings = Booking::where('user_id', $user->id)
            ->where('status', 'pending')
            ->count();

        // Admin statistics
        $adminStats = [];
        if ($user->isAdmin()) {
            $adminStats = [
                'total_bookings' => Booking::count(),
                'pending_approvals' => Booking::where('status', 'pending')->count(),
                'total_rooms' => MeetingRoom::count(),
                'active_users' => \App\Models\User::where('is_active', true)->count(),
                'today_bookings' => Booking::whereDate('start_time', Carbon::today())->count(),
                'security_alerts' => SecurityLog::where('severity', 'high')
                    ->where('created_at', '>=', Carbon::now()->subDays(7))
                    ->count(),
            ];
        }

        // Recent security activity for current user
        $recentActivity = SecurityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('dashboard.index', compact(
            'user',
            'userBookings',
            'upcomingBookings', 
            'todayBookings',
            'availableRooms',
            'pendingBookings',
            'adminStats',
            'recentActivity'
        ));
    }

    /**
     * Get dashboard data for AJAX updates
     */
    public function getDashboardData()
    {
        $user = auth()->user();
        
        // Get user's bookings
        $userBookings = Booking::where('user_id', $user->id)
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'desc')
            ->take(5)
            ->get();

        // Get upcoming bookings
        $upcomingBookings = Booking::where('user_id', $user->id)
            ->where('start_time', '>', Carbon::now())
            ->where('status', 'approved')
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'asc')
            ->take(3)
            ->get();

        // Get today's bookings
        $todayBookings = Booking::where('user_id', $user->id)
            ->whereDate('start_time', Carbon::today())
            ->where('status', 'approved')
            ->with(['meetingRoom'])
            ->orderBy('start_time', 'asc')
            ->get();

        // Get available rooms count
        $availableRooms = MeetingRoom::active()->count();

        // Get pending bookings count (for user)
        $pendingBookings = Booking::where('user_id', $user->id)
            ->where('status', 'pending')
            ->count();

        $data = [
            'upcomingBookings' => $upcomingBookings,
            'upcomingBookingsCount' => $upcomingBookings->count(),
            'pendingBookings' => $pendingBookings,
            'availableRooms' => $availableRooms,
            'userBookings' => $userBookings,
            'todayBookings' => $todayBookings
        ];

        // Admin statistics
        if ($user->isAdmin()) {
            $adminStats = [
                'total_bookings' => Booking::count(),
                'pending_approvals' => Booking::where('status', 'pending')->count(),
                'total_rooms' => MeetingRoom::count(),
                'active_users' => \App\Models\User::where('is_active', true)->count(),
                'today_bookings' => Booking::whereDate('start_time', Carbon::today())->count(),
                'security_alerts' => SecurityLog::where('severity', 'high')
                    ->where('created_at', '>=', Carbon::now()->subDays(7))
                    ->count(),
            ];
            $data['adminStats'] = $adminStats;
        }

        return response()->json($data);
    }
}
