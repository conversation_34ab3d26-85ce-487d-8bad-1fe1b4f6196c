@extends('layouts.dashboard')

@section('title', 'Laporan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><PERSON><PERSON><PERSON> Sistem</h1>
            <p class="text-muted"><PERSON><PERSON><PERSON> dan laporan komprehensif sistem pemesanan</p>
        </div>
        <div>
            <button class="btn btn-success" onclick="exportReport()">
                <i class="bi bi-download me-1"></i> Export Laporan
            </button>
        </div>
    </div>

    <!-- Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Periode</label>
                        <select class="form-select" name="period" onchange="toggleCustomDate()">
                            <option value="today" {{ request('period', 'month') === 'today' ? 'selected' : '' }}>Hari Ini</option>
                            <option value="week" {{ request('period', 'month') === 'week' ? 'selected' : '' }}>Minggu Ini</option>
                            <option value="month" {{ request('period', 'month') === 'month' ? 'selected' : '' }}>Bulan Ini</option>
                            <option value="quarter" {{ request('period', 'month') === 'quarter' ? 'selected' : '' }}>Kuartal Ini</option>
                            <option value="year" {{ request('period', 'month') === 'year' ? 'selected' : '' }}>Tahun Ini</option>
                            <option value="custom" {{ request('period', 'month') === 'custom' ? 'selected' : '' }}>Custom</option>
                        </select>
                    </div>
                    <div class="col-md-3" id="customDateFrom" style="display: {{ request('period') === 'custom' ? 'block' : 'none' }};">
                        <label class="form-label">Dari Tanggal</label>
                        <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-3" id="customDateTo" style="display: {{ request('period') === 'custom' ? 'block' : 'none' }};">
                        <label class="form-label">Sampai Tanggal</label>
                        <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-graph-up me-1"></i> Generate
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pemesanan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_bookings'] ?? 0 }}</div>
                            <div class="text-xs text-success">
                                <i class="bi bi-arrow-up"></i> {{ $stats['booking_growth'] ?? 0 }}% dari periode sebelumnya
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Pemesanan Disetujui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['approved_bookings'] ?? 0 }}</div>
                            <div class="text-xs text-success">
                                {{ $stats['approval_rate'] ?? 0 }}% tingkat persetujuan
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pengguna Aktif
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_users'] ?? 0 }}</div>
                            <div class="text-xs text-info">
                                {{ $stats['new_users'] ?? 0 }} pengguna baru
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tingkat Utilisasi
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['utilization_rate'] ?? 0 }}%</div>
                            <div class="text-xs text-warning">
                                Rata-rata penggunaan ruang
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Booking Trends Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tren Pemesanan</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="bookingTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Room Utilization -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Utilisasi Ruang</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="roomUtilizationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Popular Rooms -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ruang Terpopuler</h6>
                </div>
                <div class="card-body">
                    @if(isset($popularRooms) && count($popularRooms) > 0)
                        @foreach($popularRooms as $room)
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <img src="{{ $room->image ? Storage::url($room->image) : '/images/room-default.jpg' }}" 
                                     alt="{{ $room->name }}" class="rounded" width="60" height="60">
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">{{ $room->name }}</h6>
                                <small class="text-muted">{{ $room->bookings_count }} pemesanan</small>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ ($popularRooms->first()->bookings_count ?? 0) > 0 ? ($room->bookings_count / $popularRooms->first()->bookings_count) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">{{ $room->capacity }} orang</span>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">Belum ada data pemesanan</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pengguna Teraktif</h6>
                </div>
                <div class="card-body">
                    @if(isset($activeUsers) && count($activeUsers) > 0)
                        @foreach($activeUsers as $user)
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                @if($user->avatar)
                                    <img src="{{ Storage::url($user->avatar) }}" alt="Avatar" 
                                         class="rounded-circle" width="50" height="50">
                                @else
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px; color: white;">
                                        {{ strtoupper(substr($user->name, 0, 1)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">{{ $user->name }}</h6>
                                <small class="text-muted">{{ $user->department ?? 'N/A' }}</small>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ ($activeUsers->first()->bookings_count ?? 0) > 0 ? ($user->bookings_count / $activeUsers->first()->bookings_count) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">{{ $user->bookings_count }} booking</span>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">Belum ada data pengguna</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Laporan Detail</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Ruang</th>
                                    <th>Pemesanan</th>
                                    <th>Disetujui</th>
                                    <th>Ditolak</th>
                                    <th>Tingkat Utilisasi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($dailyReports) && count($dailyReports) > 0)
                                    @foreach($dailyReports as $report)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($report->date)->format('d M Y') }}</td>
                                        <td>{{ $report->room_name ?? 'Semua Ruang' }}</td>
                                        <td><span class="badge bg-primary">{{ $report->total_bookings }}</span></td>
                                        <td><span class="badge bg-success">{{ $report->approved_bookings }}</span></td>
                                        <td><span class="badge bg-danger">{{ $report->rejected_bookings }}</span></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ $report->utilization_rate }}%">
                                                    {{ $report->utilization_rate }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-graph-down fs-1"></i>
                                                <p class="mt-2">Belum ada data laporan untuk periode ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function toggleCustomDate() {
    const period = document.querySelector('select[name="period"]').value;
    const customDateFrom = document.getElementById('customDateFrom');
    const customDateTo = document.getElementById('customDateTo');
    
    if (period === 'custom') {
        customDateFrom.style.display = 'block';
        customDateTo.style.display = 'block';
    } else {
        customDateFrom.style.display = 'none';
        customDateTo.style.display = 'none';
    }
}

// Booking Trends Chart
const trendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
const bookingTrendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($trendLabels ?? ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']) !!},
        datasets: [{
            label: 'Pemesanan',
            data: {!! json_encode($trendData ?? [12, 19, 3, 5, 2, 3]) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }, {
            label: 'Disetujui',
            data: {!! json_encode($approvedTrendData ?? [10, 15, 2, 4, 1, 2]) !!},
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Room Utilization Chart
const utilizationCtx = document.getElementById('roomUtilizationChart').getContext('2d');
const roomUtilizationChart = new Chart(utilizationCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($roomNames ?? ['Ruang A', 'Ruang B', 'Ruang C']) !!},
        datasets: [{
            data: {!! json_encode($roomUtilizationData ?? [30, 45, 25]) !!},
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(34, 197, 94)',
                'rgb(251, 191, 36)',
                'rgb(239, 68, 68)',
                'rgb(168, 85, 247)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportReport() {
    const period = new URLSearchParams(window.location.search).get('period') || 'month';
    const dateFrom = new URLSearchParams(window.location.search).get('date_from') || '';
    const dateTo = new URLSearchParams(window.location.search).get('date_to') || '';
    
    let url = `/admin/reports/export?period=${period}`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;
    
    window.open(url, '_blank');
}
</script>
@endpush
@endsection
