<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin System',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
            'theme_preference' => 'light',
        ]);

        // Create regular user for testing
        User::create([
            'name' => '<PERSON>',
            'username' => 'johndoe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'is_active' => true,
            'email_verified_at' => now(),
            'theme_preference' => 'light',
        ]);

        echo "Admin and test users created successfully!\n";
        echo "Admin login: <EMAIL> / admin123\n";
        echo "User login: <EMAIL> / password\n";
    }
}
