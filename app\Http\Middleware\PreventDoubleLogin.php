<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SecurityLog;
use Illuminate\Support\Facades\Auth;

class PreventDoubleLogin
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip all checks for admin users - admin dapat login multiple session
        if (Auth::check() && Auth::user()->role === 'admin') {
            return $next($request);
        }

        if (Auth::check()) {
            $user = Auth::user();
            $currentSessionId = session()->getId();

            // Check if user is forced to logout
            if ($user->force_logout) {
                SecurityLog::logAction(
                    'forced_logout_triggered',
                    $user->id,
                    'medium',
                    ['reason' => 'Account locked or force logout enabled'],
                    $currentSessionId
                );

                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                return redirect()->route('login')->with('error', 'Akun Anda telah dikunci atau dipaksa keluar dari sistem.');
            }

            // Enhanced double login prevention for non-admin users
            if ($user->session_id && $user->session_id !== $currentSessionId) {
                // Check if the stored session is still active
                $storedSessionActive = $this->isSessionActive($user->session_id);
                
                if ($storedSessionActive) {
                    SecurityLog::logAction(
                        'double_login_prevented',
                        $user->id,
                        'high',
                        [
                            'old_session' => $user->session_id,
                            'new_session' => $currentSessionId,
                            'action' => 'forcing_logout_old_session',
                            'ip_address' => $request->ip(),
                            'user_agent' => $request->userAgent()
                        ],
                        $currentSessionId
                    );

                    // Invalidate old session
                    $this->invalidateSession($user->session_id);
                    
                    // Force logout old session
                    $user->update([
                        'session_id' => $currentSessionId,
                        'force_logout' => false
                    ]);
                } else {
                    // Old session is not active, update to current session
                    $user->update(['session_id' => $currentSessionId]);
                }
            } else {
                // No session ID stored, set current session
                $user->update(['session_id' => $currentSessionId]);
            }

            // Check if account is locked
            if ($user->isLocked()) {
                SecurityLog::logAction(
                    'access_attempt_locked_account',
                    $user->id,
                    'high',
                    ['locked_until' => $user->locked_until->toISOString()],
                    $currentSessionId
                );

                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                return redirect()->route('login')->with('error', 'Akun Anda terkunci sampai ' . $user->locked_until->format('d/m/Y H:i'));
            }

            // Check for suspicious activity patterns
            $this->checkSuspiciousActivity($request, $user);
        }

        return $next($request);
    }

    /**
     * Check if a session is still active
     */
    protected function isSessionActive($sessionId)
    {
        // Check if session file exists and is valid
        $sessionPath = storage_path('framework/sessions/' . $sessionId);
        
        if (file_exists($sessionPath)) {
            $sessionData = file_get_contents($sessionPath);
            if ($sessionData && strlen($sessionData) > 0) {
                return true;
            }
        }

        // For database sessions, check sessions table
        try {
            $session = \DB::table('sessions')->where('id', $sessionId)->first();
            return $session !== null;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Invalidate a specific session
     */
    protected function invalidateSession($sessionId)
    {
        try {
            // Remove session file
            $sessionPath = storage_path('framework/sessions/' . $sessionId);
            if (file_exists($sessionPath)) {
                unlink($sessionPath);
            }

            // Remove from database if using database sessions
            \DB::table('sessions')->where('id', $sessionId)->delete();
        } catch (\Exception $e) {
            \Log::warning('Failed to invalidate session: ' . $e->getMessage());
        }
    }

    /**
     * Check for suspicious login activity
     */
    protected function checkSuspiciousActivity(Request $request, $user)
    {
        $currentIP = $request->ip();
        $lastIP = $user->last_login_ip;
        $lastLoginAt = $user->last_login_at;

        // Check for login from different country/region
        if ($lastIP && $lastIP !== $currentIP) {
            // Simple geo-location check (you can enhance this with proper geo-IP service)
            $ipInfo = $this->getBasicIPInfo($currentIP);
            $lastIPInfo = $this->getBasicIPInfo($lastIP);

            if ($ipInfo['country'] !== $lastIPInfo['country']) {
                SecurityLog::logAction(
                    'login_from_different_country',
                    $user->id,
                    'medium',
                    [
                        'current_ip' => $currentIP,
                        'current_country' => $ipInfo['country'],
                        'last_ip' => $lastIP,
                        'last_country' => $lastIPInfo['country']
                    ],
                    session()->getId()
                );
            }
        }

        // Check for rapid login attempts
        if ($lastLoginAt && $lastLoginAt->diffInMinutes(now()) < 1) {
            SecurityLog::logAction(
                'rapid_login_detected',
                $user->id,
                'medium',
                [
                    'time_diff_seconds' => $lastLoginAt->diffInSeconds(now()),
                    'ip' => $currentIP
                ],
                session()->getId()
            );
        }

        // Update login tracking
        $user->update([
            'last_login_ip' => $currentIP,
            'last_login_at' => now()
        ]);
    }

    /**
     * Get basic IP information
     */
    protected function getBasicIPInfo($ip)
    {
        // Basic IP range detection (enhance with proper service)
        $info = ['country' => 'Unknown', 'region' => 'Unknown'];

        // Indonesian IP ranges (simplified)
        $indonesianRanges = [
            '103.', '118.', '125.', '180.', '202.', '203.'
        ];

        foreach ($indonesianRanges as $range) {
            if (str_starts_with($ip, $range)) {
                $info['country'] = 'Indonesia';
                break;
            }
        }

        return $info;
    }
}
