# 📅 Sistem Pemesanan Ruang Rapat

Sistem pemesanan ruang rapat modern dengan keamanan tinggi, real-time updates menggunakan Pusher, dan interface yang responsif.

## 🚀 Fitur Utama

### 🔐 Keamanan Tinggi
- **Autentikasi Multi-Layer**: Login dengan username/password + verifikasi email
- **Anti-Brute Force**: Akun terkunci otomatis setelah 3x percobaan gagal
- **Pencegahan Double Login**: Satu user hanya bisa login di satu sesi
- **Security Logging**: Semua aktivitas keamanan dilog secara detail
- **Proteksi Berlapis**: 
  - SQL Injection (SQLi)
  - Cross-Site Scripting (XSS) 
  - Cross-Site Request Forgery (CSRF)
  - Command Injection
  - Path Traversal
  - Session Hijacking
  - Dan banyak lagi...

### 🎨 User Interface
- **Login Page Animasi**: 3 form dengan smooth slide transitions (Login, Register, Reset Password)
- **Theme Switcher**: 3 tema (Light, Solarized Dark, Synth Wave)
- **Dashboard Real-time**: Update data secara real-time menggunakan Pusher
- **Responsive Design**: Optimal di desktop dan mobile
- **Smooth Transitions**: Semua navigasi tanpa refresh halaman

### 📱 Notifikasi Real-time
- **Pusher Integration**: Notifikasi real-time untuk semua user
- **Email Notifications**: Verifikasi email dan reset password
- **SweetAlert**: Notifikasi yang elegan dan user-friendly

### 🏢 Manajemen Ruang Rapat
- **Kalender Interaktif**: Lihat jadwal booking dalam format kalender
- **Booking System**: Sistem pemesanan dengan approval workflow
- **Conflict Detection**: Deteksi otomatis bentrok jadwal
- **Room Management**: Kelola ruang rapat dengan detail fasilitas

## 🛠️ Tech Stack

- **Backend**: Laravel 11 (PHP 8.1+)
- **Database**: MySQL/PostgreSQL
- **Real-time**: Pusher
- **Frontend**: Blade Templates + CSS/JavaScript
- **Email**: SMTP (Gmail)
- **Security**: Multi-layer protection

## 📋 Persyaratan Sistem

- PHP >= 8.1
- Composer
- MySQL/PostgreSQL
- Node.js & NPM (untuk asset compilation)
- Web Server (Apache/Nginx)

## ⚡ Quick Start

### 1. Clone & Install
```bash
git clone [repository-url]
cd sistem-rapat
composer install
```

### 2. Environment Setup
```bash
cp .env.example .env
php artisan key:generate
```

### 3. Database Setup
```bash
# Edit .env file untuk konfigurasi database
php artisan migrate
php artisan db:seed
```

### 4. Run Application
```bash
php artisan serve
```

Aplikasi akan berjalan di `http://localhost:8000`

## 👥 Default Users

### Administrator
- **Username**: `admin`
- **Password**: `Admin123!`
- **Email**: `<EMAIL>`

### Regular Users
- **Username**: `budi.santoso` | **Password**: `User123!`
- **Username**: `siti.nurhaliza` | **Password**: `User123!`
- **Username**: `andi.wijaya` | **Password**: `User123!`
- **Username**: `maya.sari` | **Password**: `User123!`
- **Username**: `doni.hermawan` | **Password**: `User123!`

## 🔧 Konfigurasi

### Email Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

### Pusher Configuration
```env
BROADCAST_CONNECTION=pusher
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_APP_CLUSTER=ap1
```

## 🎯 Fitur yang Akan Datang

- [ ] Mobile App (React Native)
- [ ] Integration dengan Google Calendar
- [ ] QR Code Check-in/out
- [ ] Reporting & Analytics
- [ ] Multi-tenant Support
- [ ] API Documentation (Swagger)

## 🔒 Security Features Detail

### Input Validation
- Semua input divalidasi dan disanitasi
- Protection terhadap malicious payloads
- Rate limiting untuk mencegah abuse

### Session Security
- Secure session handling
- Session fixation protection
- Automatic session timeout

### Database Security
- Prepared statements (protection against SQL injection)
- Database connection encryption
- Audit logging

### API Security
- CSRF protection
- Rate limiting
- Input validation
- Proper error handling

## 📖 Dokumentasi API

Coming soon...

## 🤝 Contributing

1. Fork the project
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

Untuk support dan pertanyaan, silakan buka [issue](link-to-issues) di repository ini.

---

<p align="center">
Dibuat dengan ❤️ menggunakan Laravel
</p>

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Redberry](https://redberry.international/laravel-development)**
- **[Active Logic](https://activelogic.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
