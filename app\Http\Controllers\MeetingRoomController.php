<?php

namespace App\Http\Controllers;

use App\Models\MeetingRoom;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MeetingRoomController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('security');
    }

    public function index()
    {
        $rooms = MeetingRoom::where('is_active', true)->with('roomFacilities')->get();
        
        // Get current bookings for each room
        foreach ($rooms as $room) {
            $room->current_booking = $room->bookings()
                ->where('status', 'approved')
                ->where('start_time', '<=', now())
                ->where('end_time', '>=', now())
                ->with('user')
                ->first();
                
            $room->next_booking = $room->bookings()
                ->where('status', 'approved')
                ->where('start_time', '>', now())
                ->orderBy('start_time')
                ->with('user')
                ->first();
                
            // Get today's schedule
            $room->today_schedule = $room->bookings()
                ->where('status', 'approved')
                ->whereDate('start_time', today())
                ->orderBy('start_time')
                ->with('user')
                ->get();
        }

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_meeting_rooms',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return view('rooms.index', compact('rooms'));
    }

    public function show(MeetingRoom $room)
    {
        if (!$room->is_active) {
            abort(404);
        }

        // Load facilities relationship
        $room->load('roomFacilities');

        // Get today's bookings for this room
        $todayBookings = $room->bookings()
                                   ->whereDate('start_time', today())
                                   ->whereIn('status', ['pending', 'approved'])
                                   ->with('user')
                                   ->orderBy('start_time')
                                   ->get();

        // Get upcoming bookings (next 7 days)
        $upcomingBookings = $room->bookings()
                                      ->whereBetween('start_time', [now(), now()->addDays(7)])
                                      ->whereIn('status', ['pending', 'approved'])
                                      ->with('user')
                                      ->orderBy('start_time')
                                      ->get();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_meeting_room_details',
            'details' => "Viewed room: {$room->name}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return view('meeting-rooms.show', compact('room', 'todayBookings', 'upcomingBookings'));
    }

    public function checkAvailability(Request $request, MeetingRoom $meetingRoom)
    {
        $request->validate([
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
        ]);

        $conflicts = $meetingRoom->bookings()
                                ->where(function($query) use ($request) {
                                    $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                                          ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                                          ->orWhere(function($q) use ($request) {
                                              $q->where('start_time', '<=', $request->start_time)
                                                ->where('end_time', '>=', $request->end_time);
                                          });
                                })
                                ->whereIn('status', ['pending', 'approved'])
                                ->exists();

        return response()->json([
            'available' => !$conflicts,
            'message' => $conflicts ? 'Ruang tidak tersedia pada waktu tersebut' : 'Ruang tersedia'
        ]);
    }
}
