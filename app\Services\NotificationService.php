<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Events\NotificationCreated;

class NotificationService
{
    /**
     * Create notification for a specific user
     */
    public static function createForUser(
        int $userId,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): Notification {
        $notification = Notification::createForUser($userId, $type, $title, $message, $data);
        
        // Broadcast the notification
        broadcast(new NotificationCreated($notification));
        
        return $notification;
    }

    /**
     * Create notification for all admins
     */
    public static function createForAdmins(
        string $type,
        string $title,
        string $message,
        array $data = []
    ): void {
        $adminUsers = User::where('role', 'admin')->get();
        
        foreach ($adminUsers as $admin) {
            self::createForUser($admin->id, $type, $title, $message, $data);
        }
    }

    /**
     * Notification for new booking created
     */
    public static function notifyNewBooking($booking): void
    {
        // Notify all admins
        self::createForAdmins(
            'booking_created',
            '<PERSON><PERSON><PERSON><PERSON> Baru',
            "<PERSON><PERSON><PERSON><PERSON> ruang {$booking->meetingRoom->name} oleh {$booking->user->name} perlu disetujui",
            [
                'booking_id' => $booking->id,
                'room_name' => $booking->meetingRoom->name,
                'user_name' => $booking->user->name,
                'start_time' => $booking->start_time->format('d/m/Y H:i'),
                'end_time' => $booking->end_time->format('d/m/Y H:i'),
            ]
        );

        // Notify the user who created the booking
        self::createForUser(
            $booking->user_id,
            'booking_submitted',
            'Pemesanan Berhasil Dibuat',
            "Pemesanan ruang {$booking->meetingRoom->name} telah dibuat dan menunggu persetujuan admin",
            [
                'booking_id' => $booking->id,
                'room_name' => $booking->meetingRoom->name,
                'start_time' => $booking->start_time->format('d/m/Y H:i'),
                'end_time' => $booking->end_time->format('d/m/Y H:i'),
            ]
        );
    }

    /**
     * Notification for booking status update
     */
    public static function notifyBookingStatusUpdate($booking, $oldStatus): void
    {
        $statusText = [
            'approved' => 'disetujui',
            'rejected' => 'ditolak',
            'cancelled' => 'dibatalkan'
        ];

        $status = $statusText[$booking->status] ?? $booking->status;

        // Notify the booking owner
        self::createForUser(
            $booking->user_id,
            'booking_status_updated',
            'Status Pemesanan Diperbarui',
            "Pemesanan ruang {$booking->meetingRoom->name} telah {$status}",
            [
                'booking_id' => $booking->id,
                'room_name' => $booking->meetingRoom->name,
                'old_status' => $oldStatus,
                'new_status' => $booking->status,
                'start_time' => $booking->start_time->format('d/m/Y H:i'),
                'end_time' => $booking->end_time->format('d/m/Y H:i'),
            ]
        );

        // Notify admins about the status change
        if ($booking->status === 'cancelled') {
            self::createForAdmins(
                'booking_cancelled',
                'Pemesanan Dibatalkan',
                "Pemesanan ruang {$booking->meetingRoom->name} oleh {$booking->user->name} telah dibatalkan",
                [
                    'booking_id' => $booking->id,
                    'room_name' => $booking->meetingRoom->name,
                    'user_name' => $booking->user->name,
                    'start_time' => $booking->start_time->format('d/m/Y H:i'),
                    'end_time' => $booking->end_time->format('d/m/Y H:i'),
                ]
            );
        }
    }

    /**
     * Notification for user account locked
     */
    public static function notifyUserLocked($user, $reason = 'Gagal login berulang kali'): void
    {
        // Notify all admins
        self::createForAdmins(
            'user_locked',
            'Akun User Terkunci',
            "Akun {$user->name} ({$user->email}) telah terkunci. Alasan: {$reason}",
            [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'reason' => $reason,
                'locked_until' => $user->locked_until?->format('d/m/Y H:i'),
            ]
        );

        // Notify the user
        self::createForUser(
            $user->id,
            'account_locked',
            'Akun Anda Terkunci',
            "Akun Anda telah terkunci karena {$reason}. Silakan hubungi administrator",
            [
                'reason' => $reason,
                'locked_until' => $user->locked_until?->format('d/m/Y H:i'),
            ]
        );
    }

    /**
     * Notification for security alert
     */
    public static function notifySecurityAlert($securityLog): void
    {
        if ($securityLog->severity === 'warning' || $securityLog->severity === 'error') {
            self::createForAdmins(
                'security_alert',
                'Peringatan Keamanan',
                "Alert keamanan: {$securityLog->action} dari IP {$securityLog->ip_address}",
                [
                    'security_log_id' => $securityLog->id,
                    'action' => $securityLog->action,
                    'ip_address' => $securityLog->ip_address,
                    'severity' => $securityLog->severity,
                    'user_name' => $securityLog->user?->name ?? 'Guest',
                ]
            );
        }
    }

    /**
     * Notification for user unlocked
     */
    public static function notifyUserUnlocked($user): void
    {
        // Notify the user
        self::createForUser(
            $user->id,
            'account_unlocked',
            'Akun Anda Telah Dibuka',
            "Akun Anda telah dibuka kembali oleh administrator. Anda dapat login kembali sekarang",
            [
                'user_id' => $user->id,
            ]
        );

        // Notify admins
        self::createForAdmins(
            'user_unlocked',
            'Akun User Dibuka',
            "Akun {$user->name} ({$user->email}) telah dibuka kembali",
            [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
            ]
        );
    }
}
