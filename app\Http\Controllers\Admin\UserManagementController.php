<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SecurityLog;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->middleware('security');
    }

    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where(function($q) {
                    $q->whereNull('locked_until')
                      ->orWhere('locked_until', '<=', now());
                });
            } elseif ($request->status === 'locked') {
                $query->where('locked_until', '>', now());
            }
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        $users = $query->withCount(['bookings'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(15);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_user_management',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'description' => 'Admin mengakses halaman kelola pengguna',
            'level' => 'info'
        ]);

        return view('admin.users.index', compact('users'));
    }

    public function show(User $user)
    {
        $user->load(['bookings.meetingRoom']);
        
        // Recent bookings
        $recentBookings = $user->bookings()
                             ->with('meetingRoom')
                             ->orderBy('created_at', 'desc')
                             ->limit(5)
                             ->get();

        // Booking statistics
        $totalBookings = $user->bookings()->count();
        $approvedBookings = $user->bookings()->where('status', 'approved')->count();
        $pendingBookings = $user->bookings()->where('status', 'pending')->count();
        $rejectedBookings = $user->bookings()->where('status', 'rejected')->count();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_user_detail',
            'description' => "Melihat detail pengguna: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'level' => 'info'
        ]);

        return view('admin.users.show', compact(
            'user', 
            'recentBookings', 
            'totalBookings',
            'approvedBookings',
            'pendingBookings',
            'rejectedBookings'
        ));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'department' => 'nullable|string|max:100',
            'position' => 'nullable|string|max:100',
            'role' => 'required|in:user,admin',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'security_notes' => 'nullable|string|max:1000',
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'department' => $request->department,
            'position' => $request->position,
            'role' => $request->role,
            'security_notes' => $request->security_notes,
            'email_verified_at' => $request->boolean('email_verified') ? now() : null,
        ];

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $path = $request->file('avatar')->store('avatars', 'public');
            $userData['avatar'] = $path;
        }

        $user = User::create($userData);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'create_user',
            'description' => "Membuat pengguna baru: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'level' => 'info'
        ]);

        return redirect()->route('admin.users.index')->with('success', 'Pengguna berhasil dibuat.');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'department' => 'nullable|string|max:100',
            'position' => 'nullable|string|max:100',
            'role' => 'required|in:user,admin',
            'password' => 'nullable|min:8|confirmed',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'security_notes' => 'nullable|string|max:1000',
            'account_locked' => 'nullable|in:0,1',
            'email_verified' => 'nullable|in:0,1',
            'login_attempts' => 'nullable|integer|min:0',
        ]);

        // Handle avatar upload
        $avatarPath = null;
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && \Storage::exists($user->avatar)) {
                \Storage::delete($user->avatar);
            }
            
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
        }

        // Update basic user information
        $updateData = [
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'phone' => $request->phone,
            'department' => $request->department,
            'position' => $request->position,
            'role' => $request->role,
            'security_notes' => $request->security_notes,
        ];

        // Add avatar path if uploaded
        if ($avatarPath) {
            $updateData['avatar'] = $avatarPath;
        }

        // Handle avatar removal
        if ($request->has('remove_avatar') && $user->avatar) {
            if (\Storage::exists($user->avatar)) {
                \Storage::delete($user->avatar);
            }
            $updateData['avatar'] = null;
        }

        $user->update($updateData);

        // Handle password update
        if ($request->filled('password')) {
            $user->update([
                'password' => \Hash::make($request->password)
            ]);
        }

        // Handle email verification status
        if ($request->has('email_verified')) {
            $emailVerified = $request->input('email_verified') == '1';
            if ($emailVerified) {
                $user->email_verified_at = now();
            } else {
                $user->email_verified_at = null;
            }
            $user->save();
        }

        // Handle account lock status
        if ($request->has('account_locked')) {
            $accountLocked = $request->input('account_locked') == '1';
            if ($accountLocked) {
                $user->lockAccount();
            } else {
                $user->unlockAccount();
            }
        }

        // Handle login attempts reset
        if ($request->has('login_attempts')) {
            $user->failed_login_attempts = $request->integer('login_attempts');
            $user->save();
        }

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'update_user',
            'details' => "Updated user: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'level' => 'info'
        ]);

        return redirect()->route('admin.users.index')->with('success', 'User berhasil diperbarui.');
    }

    public function destroy(User $user)
    {
        if ($user->id === Auth::id()) {
            return back()->withErrors(['error' => 'Tidak dapat menghapus akun sendiri.']);
        }

        $userName = $user->name;
        $user->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'delete_user',
            'details' => "Deleted user: {$userName}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return redirect()->route('admin.users.index')->with('success', 'User berhasil dihapus.');
    }

    public function toggleStatus(User $user)
    {
        if ($user->id === Auth::id()) {
            return back()->withErrors(['error' => 'Tidak dapat mengubah status akun sendiri.']);
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        
        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => "user_{$status}",
            'details' => "User {$status}: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        $message = $user->is_active ? 'User berhasil diaktifkan.' : 'User berhasil dinonaktifkan.';
        return back()->with('success', $message);
    }

    public function unlockAccount(User $user)
    {
        $user->unlockAccount();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'unlock_user_account',
            'details' => "Unlocked account: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return back()->with('success', 'Akun berhasil dibuka kembali.');
    }

    public function resetPassword(User $user)
    {
        $newPassword = 'Password123!'; // Default password
        
        $user->update([
            'password' => Hash::make($newPassword),
            'password_changed_at' => now()
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'reset_user_password',
            'details' => "Reset password for user: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return back()->with('success', "Password berhasil direset. Password baru: {$newPassword}");
    }

    public function lock(User $user)
    {
        \Log::info('Lock user called', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'current_locked_until' => $user->locked_until,
            'is_locked_before' => $user->isLocked()
        ]);

        if ($user->id === Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Tidak dapat mengunci akun sendiri.']);
        }

        $user->lockAccount();

        // Reload user to get fresh data
        $user->refresh();

        \Log::info('User locked', [
            'user_id' => $user->id,
            'locked_until_after' => $user->locked_until,
            'is_locked_after' => $user->isLocked()
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'lock_user_account',
            'details' => "Mengunci akun pengguna: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'severity' => 'medium'
        ]);

        return response()->json(['success' => true, 'message' => 'Akun berhasil dikunci.']);
    }

    public function unlock(User $user)
    {
        \Log::info('Unlock user called', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'current_locked_until' => $user->locked_until,
            'is_locked_before' => $user->isLocked()
        ]);

        $user->unlockAccount();

        // Reload user to get fresh data
        $user->refresh();

        \Log::info('User unlocked', [
            'user_id' => $user->id,
            'locked_until_after' => $user->locked_until,
            'is_locked_after' => $user->isLocked()
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'unlock_user_account',
            'details' => "Membuka kunci akun pengguna: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'severity' => 'info'
        ]);

        return response()->json(['success' => true, 'message' => 'Akun berhasil dibuka.']);
    }

    public function resendVerification(User $user)
    {
        if ($user->email_verified_at) {
            return response()->json(['success' => false, 'message' => 'Email sudah terverifikasi.']);
        }

        // In real application, you would send verification email here
        // For now, we'll just mark as verified
        $user->update(['email_verified_at' => now()]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'resend_verification',
            'details' => "Mengirim ulang verifikasi email untuk: {$user->name} (ID: {$user->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'severity' => 'info'
        ]);

        return response()->json(['success' => true, 'message' => 'Email verifikasi telah dikirim.']);
    }
}
