<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Services\NotificationService;

class SecurityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'ip_address',
        'user_agent',
        'severity',
        'details',
        'session_id',
    ];

    protected function casts(): array
    {
        return [
            'details' => 'array',
        ];
    }

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a security log entry
     */
    public static function logAction(
        string $action,
        ?int $userId = null,
        string $severity = 'medium',
        array $details = [],
        ?string $sessionId = null
    ): self {
        $log = self::create([
            'user_id' => $userId,
            'action' => $action,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'severity' => $severity,
            'details' => $details,
            'session_id' => $sessionId,
        ]);

        // Create notification for security alerts (avoid circular dependency)
        try {
            if (in_array($severity, ['warning', 'error']) && class_exists('\App\Services\NotificationService')) {
                \App\Services\NotificationService::notifySecurityAlert($log);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to create security alert notification', ['error' => $e->getMessage()]);
        }

        return $log;
    }

    /**
     * Get logs by severity
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Get logs by action
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Get recent logs
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }
}
