# Copilot Instructions - Sistem Pemesanan Ruang Rapat

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
Sistem Pemesanan Ruang Rapat menggunakan Laravel dengan keamanan tinggi, real-time updates menggunakan Pusher, dan database PostgreSQL.

## Tech Stack
- **Backend**: Laravel 11 (PHP 8.1+)
- **Database**: PostgreSQL
- **Real-time**: Pusher
- **Frontend**: Blade Templates dengan animasi CSS/JavaScript
- **Email**: SMTP Gmail

## Security Features
- Implementasi proteksi berlapis terhadap SQL Injection, XSS, CSRF
- Rate limiting untuk mencegah brute force attacks
- Session security dan anti-hijacking
- Input validation dan sanitization
- File upload security

## Key Features
1. **Login System**: Animasi slide transitions dengan 3 form (login, register, reset password)
2. **Dashboard**: Real-time updates dengan theme switcher (Solarized Dark, Synth Wave, Light)
3. **Room Booking**: Sistem pemesanan ruang rapat dengan kalender interaktif
4. **Notifications**: Real-time menggunakan Pusher dan email notifications

## Development Guidelines
- Semua teks dan interface menggunakan bahasa Indonesia
- Implementasi smooth transitions tanpa refresh halaman
- Gunakan SweetAlert untuk notifikasi
- Responsive design untuk mobile dan desktop
- Code harus mengikuti Laravel best practices

## Email Configuration
- SMTP: Gmail (smtp.gmail.com:587)
- Encryption: TLS

## Pusher Configuration
- Cluster: ap1
- Real-time untuk notifikasi dan updates

## Database Schema
- Users dengan enhanced security fields
- Meeting rooms dengan fasilitas
- Bookings dengan status tracking
- Security logs untuk audit trail
