<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\SecurityLog;

class RateLimitingMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Skip rate limiting for admin users
        if (Auth::check() && Auth::user()->role === 'admin') {
            return $next($request);
        }

        $this->applyBruteForceProtection($request);
        $this->applyCredentialStuffingProtection($request);
        $this->applyGeneralRateLimiting($request);

        return $next($request);
    }

    protected function applyBruteForceProtection(Request $request)
    {
        if ($request->is('login') && $request->isMethod('POST')) {
            $key = 'login_attempts:' . $request->ip();
            $maxAttempts = 5;
            $decayMinutes = 15;

            if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
                $this->logSecurityEvent('brute_force_detected', $request, 'critical');
                abort(429, 'Terlalu banyak percobaan login. Coba lagi dalam ' . $decayMinutes . ' menit.');
            }

            RateLimiter::hit($key, $decayMinutes * 60);
        }
    }

    protected function applyCredentialStuffingProtection(Request $request)
    {
        if ($request->is('login') && $request->isMethod('POST')) {
            $username = $request->input('username');
            if ($username) {
                $key = 'credential_stuffing:' . hash('sha256', $username);
                $maxAttempts = 3;
                $decayMinutes = 30;

                if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
                    $this->logSecurityEvent('credential_stuffing_detected', $request, 'critical', [
                        'username' => $username
                    ]);
                    abort(429, 'Akun ini sementara dikunci karena aktivitas mencurigakan.');
                }

                RateLimiter::hit($key, $decayMinutes * 60);
            }
        }
    }

    protected function applyGeneralRateLimiting(Request $request)
    {
        $key = 'general_rate_limit:' . $request->ip();
        $maxAttempts = Auth::check() ? 1000 : 200; // Higher limit for authenticated users
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $this->logSecurityEvent('rate_limit_exceeded', $request, 'high');
            abort(429, 'Terlalu banyak permintaan. Silakan tunggu sebentar.');
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    protected function logSecurityEvent($event, Request $request, $severity = 'medium', $additional_data = [])
    {
        $data = array_merge([
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => Auth::id()
        ], $additional_data);

        SecurityLog::logAction(
            $event,
            Auth::id(),
            $severity,
            $data,
            session()->getId()
        );
    }
}
