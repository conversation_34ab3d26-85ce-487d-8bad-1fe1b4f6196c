<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('security_logs', function (Blueprint $table) {
            $table->dropColumn('severity');
        });
        
        Schema::table('security_logs', function (Blueprint $table) {
            $table->enum('severity', ['info', 'low', 'medium', 'high', 'critical'])->default('medium')->after('user_agent');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('security_logs', function (Blueprint $table) {
            $table->dropColumn('severity');
        });
        
        Schema::table('security_logs', function (Blueprint $table) {
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium')->after('user_agent');
        });
    }
};
