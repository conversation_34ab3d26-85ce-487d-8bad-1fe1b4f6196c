@extends('layouts.dashboard')
@section('title', 'Tambah Fasilitas')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Tambah Fasilitas</h1>
        <a href="{{ route('admin.facilities.index') }}" class="btn btn-secondary"><i class="bi bi-arrow-left"></i> Kembali</a>
    </div>
    <div class="card shadow">
        <div class="card-body">
            <form action="{{ route('admin.facilities.store') }}" method="POST">
                @csrf
                <div class="mb-3">
                    <label for="name" class="form-label"><PERSON>a Fasilitas <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
                <div class="mb-3">
                    <label for="icon" class="form-label">Icon (opsional, Bootstrap Icon class)</label>
                    <input type="text" class="form-control @error('icon') is-invalid @enderror" id="icon" name="icon" value="{{ old('icon') }}" placeholder="cth: bi-projector">
                    @error('icon')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
                <button type="submit" class="btn btn-primary"><i class="bi bi-check-lg"></i> Simpan</button>
            </form>
        </div>
    </div>
</div>
@endsection
