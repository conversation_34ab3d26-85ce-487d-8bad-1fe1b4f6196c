<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

echo "=== DEBUG VERIFIKASI EMAIL ===\n";

// Cek user yang belum diverifikasi
$unverifiedUsers = User::whereNull('email_verified_at')->get();
echo "Jumlah user belum diverifikasi: " . $unverifiedUsers->count() . "\n";

foreach ($unverifiedUsers as $user) {
    echo "\n--- User: {$user->email} ---\n";
    echo "Token: " . ($user->email_verification_token ?? 'NULL') . "\n";
    echo "Token expires: " . ($user->email_verification_expires_at ? $user->email_verification_expires_at->toDateTimeString() : 'NULL') . "\n";
    echo "Waktu sekarang: " . Carbon::now()->toDateTimeString() . "\n";
    
    if ($user->email_verification_expires_at) {
        echo "Token expired: " . ($user->email_verification_expires_at->isPast() ? 'YA' : 'TIDAK') . "\n";
        echo "Selisih waktu: " . $user->email_verification_expires_at->diffForHumans() . "\n";
    }
}

// Test generate token baru
echo "\n=== TEST GENERATE TOKEN ===\n";
$testToken = Str::random(64);
$testExpiry = Carbon::now()->addHours(24);
echo "Test token: $testToken\n";
echo "Test expiry: " . $testExpiry->toDateTimeString() . "\n";
echo "Test token length: " . strlen($testToken) . "\n";

// Simulasi pencarian token
echo "\n=== TEST PENCARIAN TOKEN ===\n";
if ($unverifiedUsers->count() > 0) {
    $user = $unverifiedUsers->first();
    if ($user->email_verification_token) {
        $foundUser = User::where('email_verification_token', $user->email_verification_token)
                         ->where('email_verification_expires_at', '>', Carbon::now())
                         ->first();
        echo "User ditemukan dengan token: " . ($foundUser ? 'YA' : 'TIDAK') . "\n";
        
        // Test tanpa filter expire
        $foundUserNoExpire = User::where('email_verification_token', $user->email_verification_token)->first();
        echo "User ditemukan tanpa filter expire: " . ($foundUserNoExpire ? 'YA' : 'TIDAK') . "\n";
    }
}

echo "\n=== SELESAI ===\n";
