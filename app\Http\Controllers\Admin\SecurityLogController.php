<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SecurityLogController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->middleware('security');
    }

    public function index(Request $request)
    {
        $query = SecurityLog::with('user');

        // Date range filter
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Action filter
        if ($request->filled('action')) {
            $query->where('action', 'like', '%' . $request->action . '%');
        }

        // User filter
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // IP address filter
        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        // Severity filter
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        // Risk level filter
        if ($request->filled('risk_level')) {
            $riskActions = $this->getRiskActions($request->risk_level);
            $query->whereIn('action', $riskActions);
        }

        $securityLogs = $query->orderBy('created_at', 'desc')->paginate(50);

        // Statistics
        $today = Carbon::today();
        $stats = [
            'total_logs_today' => SecurityLog::whereDate('created_at', $today)->count(),
            'failed_logins_today' => SecurityLog::whereDate('created_at', $today)
                                              ->where('action', 'like', '%failed_login%')
                                              ->count(),
            'locked_accounts' => \App\Models\User::whereNotNull('locked_until')
                                                ->where('locked_until', '>', Carbon::now())
                                                ->count(),
            'unique_ips_today' => SecurityLog::whereDate('created_at', $today)
                                            ->distinct('ip_address')
                                            ->count('ip_address'),
        ];

        // Recent high-risk activities
        $highRiskLogs = SecurityLog::whereIn('action', $this->getRiskActions('high'))
                                  ->with('user')
                                  ->orderBy('created_at', 'desc')
                                  ->limit(10)
                                  ->get();

        // Activity by action
        $actionStats = SecurityLog::selectRaw('action, COUNT(*) as count')
                                 ->groupBy('action')
                                 ->orderByDesc('count')
                                 ->limit(10)
                                 ->get();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_security_logs',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return view('admin.security.logs', compact('securityLogs', 'stats', 'highRiskLogs', 'actionStats'));
    }

    public function show(SecurityLog $log, Request $request)
    {
        $log->load('user');

        // Get related logs (same user, similar time)
        $relatedLogs = SecurityLog::where('user_id', $log->user_id)
                                 ->where('id', '!=', $log->id)
                                 ->whereBetween('created_at', [
                                     $log->created_at->subMinutes(30),
                                     $log->created_at->addMinutes(30)
                                 ])
                                 ->orderBy('created_at')
                                 ->get();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_security_log_detail',
            'details' => "Viewed log ID: {$log->id}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        // Return JSON response for AJAX requests
        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json([
                'success' => true,
                'log' => $log,
                'related_logs' => $relatedLogs
            ]);
        }

        // Return view for direct access
        return view('admin.security.show', compact('log', 'relatedLogs'));
    }

    public function destroy(SecurityLog $log)
    {
        $log->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'delete_security_log',
            'details' => "Deleted log ID: {$log->id}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return back()->with('success', 'Log keamanan berhasil dihapus.');
    }

    public function cleanup(Request $request)
    {
        // Ambil filter dari request
        $query = SecurityLog::query();

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('ip_address', 'like', '%' . $request->search . '%')
                  ->orWhere('user_agent', 'like', '%' . $request->search . '%')
                  ->orWhere('details', 'like', '%' . $request->search . '%');
            });
        }
        if ($request->filled('action')) {
            $query->where('action', 'like', '%' . $request->action . '%');
        }
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Hapus log sesuai filter
        $deletedCount = $query->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'cleanup_security_logs',
            'details' => "Cleaned up {$deletedCount} logs by filter",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Berhasil menghapus {$deletedCount} log sesuai filter.",
                'deleted_count' => $deletedCount
            ]);
        }
        return back()->with('success', "Berhasil menghapus {$deletedCount} log sesuai filter.");
    }

    /**
     * Get real-time statistics data for security logs
     */
    public function getStatsData(Request $request)
    {
        $today = Carbon::today();
        
        // Calculate statistics
        $stats = [
            'total_logs_today' => SecurityLog::whereDate('created_at', $today)->count(),
            'failed_logins_today' => SecurityLog::whereDate('created_at', $today)
                                              ->where('action', 'like', '%failed_login%')
                                              ->count(),
            'locked_accounts' => \App\Models\User::whereNotNull('locked_until')
                                                ->where('locked_until', '>', Carbon::now())
                                                ->count(),
            'unique_ips_today' => SecurityLog::whereDate('created_at', $today)
                                            ->distinct('ip_address')
                                            ->count('ip_address'),
        ];

        // Get recent high-risk activities for chart
        $highRiskLogs = SecurityLog::whereIn('action', $this->getRiskActions('high'))
                                  ->whereDate('created_at', '>=', $today->subDays(7))
                                  ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                  ->groupBy('date')
                                  ->orderBy('date')
                                  ->get();

        // Get activity breakdown
        $actionStats = SecurityLog::whereDate('created_at', $today)
                                 ->selectRaw('action, COUNT(*) as count')
                                 ->groupBy('action')
                                 ->orderByDesc('count')
                                 ->limit(5)
                                 ->get();

        return response()->json([
            'stats' => $stats,
            'high_risk_chart' => $highRiskLogs,
            'action_breakdown' => $actionStats,
            'last_updated' => Carbon::now()->format('H:i:s')
        ]);
    }

    private function getRiskActions($level)
    {
        $actions = [
            'high' => [
                'failed_login_attempt',
                'account_locked',
                'suspicious_activity',
                'unauthorized_access_attempt',
                'admin_access',
                'delete_user',
                'delete_booking',
                'reset_user_password',
                'unlock_user_account'
            ],
            'medium' => [
                'login',
                'logout',
                'change_password',
                'update_profile',
                'create_user',
                'update_user',
                'approve_booking',
                'reject_booking'
            ],
            'low' => [
                'view_dashboard',
                'view_bookings',
                'view_meeting_rooms',
                'view_profile',
                'create_booking',
                'update_booking'
            ]
        ];

        return $actions[$level] ?? [];
    }
}
