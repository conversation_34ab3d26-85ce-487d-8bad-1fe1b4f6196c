<!DOCTYPE html>
<html lang="id" data-theme="{{ auth()->check() ? auth()->user()->theme_preference : 'light' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Sistem Pemesanan Ruang Rapat')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            /* Light Theme */
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --info-color: #0891b2;
            
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow: rgba(0, 0, 0, 0.1);
            
            --sidebar-width: 280px;
            --header-height: 70px;
        }

        [data-theme="solarized-dark"] {
            --primary-color: #268bd2;
            --primary-hover: #2aa198;
            --secondary-color: #93a1a1;
            --success-color: #859900;
            --warning-color: #b58900;
            --error-color: #dc322f;
            --info-color: #2aa198;
            
            --bg-primary: #002b36;
            --bg-secondary: #073642;
            --bg-tertiary: #586e75;
            --text-primary: #839496;
            --text-secondary: #93a1a1;
            --text-muted: #586e75;
            --border-color: #073642;
            --shadow: rgba(0, 0, 0, 0.5);
        }

        [data-theme="synth-wave"] {
            --primary-color: #ff006e;
            --primary-hover: #8338ec;
            --secondary-color: #fb5607;
            --success-color: #3a86ff;
            --warning-color: #ffbe0b;
            --error-color: #ff006e;
            --info-color: #8338ec;
            
            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --text-muted: #a0a0a0;
            --border-color: #3a3a5c;
            --shadow: rgba(255, 0, 110, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Smooth transitions */
        *, *::before, *::after {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* Button styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover:before {
            left: 100%;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--error-color);
            color: white;
        }

        .btn-info {
            background-color: var(--info-color);
            color: white;
        }

        /* Form styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Card styles */
        .card {
            background-color: var(--bg-primary);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px var(--shadow);
            border: 1px solid var(--border-color);
        }

        /* Animation classes */
        .slide-in-right {
            animation: slideInRight 0.5s ease-out;
        }

        .slide-out-left {
            animation: slideOutLeft 0.5s ease-out;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        .fade-out {
            animation: fadeOut 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutLeft {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* Theme switcher */
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background-color: var(--bg-primary);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px var(--shadow);
            border: 1px solid var(--border-color);
        }

        .theme-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            transition: all 0.2s;
        }

        .theme-btn:hover {
            transform: scale(1.1);
        }

        .theme-btn.active {
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .theme-light { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .theme-solarized { background: linear-gradient(135deg, #268bd2, #2aa198); }
        .theme-synth { background: linear-gradient(135deg, #ff006e, #8338ec); }

        /* Responsive */
        @media (max-width: 768px) {
            .theme-switcher {
                top: 10px;
                right: 10px;
                padding: 0.25rem;
            }
            
            .theme-btn {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switcher">
        <button class="theme-btn theme-light active" onclick="setTheme('light')" title="Light Theme">
            <i class="bi bi-sun"></i>
        </button>
        <button class="theme-btn theme-solarized" onclick="setTheme('solarized-dark')" title="Solarized Dark">
            <i class="bi bi-moon"></i>
        </button>
        <button class="theme-btn theme-synth" onclick="setTheme('synth-wave')" title="Synth Wave">
            <i class="bi bi-lightning"></i>
        </button>
    </div>

    <!-- Main Content -->
    <main id="main-content">
        @yield('content')
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.min.js"></script>
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    
    <script>
        // CSRF Token setup for AJAX
        window.Laravel = {
            csrfToken: '{{ csrf_token() }}'
        };

        // Theme management
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update active theme button
            document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`.theme-${theme === 'solarized-dark' ? 'solarized' : theme === 'synth-wave' ? 'synth' : 'light'}`).classList.add('active');
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // SweetAlert configuration
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        // Show flash messages
        @if(session('success'))
            Toast.fire({
                icon: 'success',
                title: '{{ session('success') }}'
            });
        @endif

        @if(session('error'))
            Toast.fire({
                icon: 'error',
                title: '{{ session('error') }}'
            });
        @endif

        @if(session('warning'))
            Toast.fire({
                icon: 'warning',
                title: '{{ session('warning') }}'
            });
        @endif

        @if(session('info'))
            Toast.fire({
                icon: 'info',
                title: '{{ session('info') }}'
            });
        @endif

        // Validation errors
        @if($errors->any())
            let errorMessages = @json($errors->all());
            let errorText = errorMessages.join('<br>');
            Swal.fire({
                icon: 'error',
                title: 'Kesalahan Validasi',
                html: errorText,
                confirmButtonText: 'OK'
            });
        @endif

        // Pusher configuration
        @auth
        const pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
            cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
            forceTLS: true
        });

        // Subscribe to user-specific channel for real-time notifications
        const channel = pusher.subscribe('user.{{ auth()->id() }}');
        
        channel.bind('notification', function(data) {
            Toast.fire({
                icon: data.type || 'info',
                title: data.message
            });
        });
        @endauth

        // Page transition animation
        function navigateTo(url) {
            const mainContent = document.getElementById('main-content');
            mainContent.classList.add('slide-out-left');
            
            setTimeout(() => {
                window.location.href = url;
            }, 300);
        }

        // Smooth scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            });
        }, 5000);
    </script>
    
    @stack('scripts')
</body>
</html>
