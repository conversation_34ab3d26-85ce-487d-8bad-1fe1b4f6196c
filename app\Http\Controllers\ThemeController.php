<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ThemeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function updateTheme(Request $request)
    {
        \Log::info('Theme update request received', [
            'user_id' => Auth::id(),
            'theme' => $request->theme,
            'all_data' => $request->all()
        ]);

        $request->validate([
            'theme' => 'required|in:light,solarized-dark,synth-wave'
        ]);

        $user = Auth::user();
        $oldTheme = $user->theme_preference;
        $user->theme_preference = $request->theme;
        $saved = $user->save();

        \Log::info('Theme update result', [
            'user_id' => Auth::id(),
            'old_theme' => $oldTheme,
            'new_theme' => $request->theme,
            'saved' => $saved
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Theme berhasil diubah',
            'theme' => $request->theme
        ]);
    }

    public function debugTheme()
    {
        $user = Auth::user();
        return response()->json([
            'user_id' => $user->id,
            'current_theme' => $user->theme_preference,
            'fillable' => $user->getFillable(),
            'attributes' => $user->getAttributes()
        ]);
    }
}
