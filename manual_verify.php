<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Carbon\Carbon;

echo "=== MANUAL VERIFIKASI USER ===\n";

// Cari user yang token sudah NULL tapi email_verified_at masih NULL
$user = User::whereNull('email_verified_at')
           ->whereNull('email_verification_token')
           ->where('email', '<EMAIL>')
           ->first();

if (!$user) {
    echo "User tidak ditemukan atau sudah diverifikasi.\n";
    exit;
}

echo "User ditemukan: {$user->email}\n";
echo "Status sebelum: " . ($user->email_verified_at ? 'TERVERIFIKASI' : 'BELUM TERVERIFIKASI') . "\n";

// Update manual
$user->email_verified_at = Carbon::now();
$result = $user->save();

echo "Save result: " . ($result ? 'BERHASIL' : 'GAGAL') . "\n";

// Refresh dari database
$user->refresh();
echo "Status sesudah: " . ($user->email_verified_at ? 'TERVERIFIKASI' : 'BELUM TERVERIFIKASI') . "\n";
echo "Email verified at: " . ($user->email_verified_at ? $user->email_verified_at->toDateTimeString() : 'NULL') . "\n";

echo "\n=== SELESAI ===\n";
