<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class MeetingRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'capacity',
        'location',
        'facilities',
        'image',
        'is_active',
        'hourly_rate',
    ];

    protected function casts(): array
    {
        return [
            'facilities' => 'array',
            'is_active' => 'boolean',
            'hourly_rate' => 'decimal:2',
        ];
    }

    /**
     * Bookings relationship
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Check if room is available for given time period
     */
    public function isAvailable(Carbon $startTime, Carbon $endTime, ?int $excludeBookingId = null): bool
    {
        $query = $this->bookings()
            ->where(function ($q) use ($startTime, $endTime) {
                $q->where(function ($subQ) use ($startTime, $endTime) {
                    // Booking starts before our end time and ends after our start time
                    $subQ->where('start_time', '<', $endTime)
                         ->where('end_time', '>', $startTime);
                });
            })
            ->whereIn('status', ['approved', 'pending']);

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->count() === 0;
    }

    /**
     * Get bookings for a specific date
     */
    public function getBookingsForDate(Carbon $date)
    {
        return $this->bookings()
            ->whereDate('start_time', $date)
            ->whereIn('status', ['approved', 'pending'])
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Scope for active rooms
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for rooms with minimum capacity
     */
    public function scopeWithCapacity($query, int $minCapacity)
    {
        return $query->where('capacity', '>=', $minCapacity);
    }

    /**
     * Get formatted facilities list
     */
    public function getFacilitiesListAttribute(): string
    {
        if (!$this->facilities) {
            return 'Tidak ada fasilitas tersedia';
        }

        return implode(', ', $this->facilities);
    }

    /**
     * Get room status
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Tidak Aktif';
        }

        $now = Carbon::now();
        $currentBooking = $this->bookings()
            ->where('start_time', '<=', $now)
            ->where('end_time', '>', $now)
            ->where('status', 'approved')
            ->first();

        return $currentBooking ? 'Sedang Digunakan' : 'Tersedia';
    }

    /**
     * Facilities relationship
     */
    public function facilities()
    {
        return $this->belongsToMany(Facility::class, 'facility_meeting_room');
    }
}
