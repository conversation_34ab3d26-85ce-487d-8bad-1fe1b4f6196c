@extends('layouts.dashboard')

@section('title', 'Manajemen Pemesanan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Manajemen Pemesanan</h1>
            <p class="text-muted">Kelola semua pemesanan ruang rapat</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="showFilterModal()">
                <i class="bi bi-funnel"></i> Filter
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Menunggu Persetujuan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Disetujui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['approved'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Ditolak
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['rejected'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pemesanan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Pemesanan</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="bookingsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Pemohon</th>
                            <th>Ruang</th>
                            <th>Judul</th>
                            <th>Waktu</th>
                            <th>Status</th>
                            <th>Dibuat</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($bookings as $booking)
                        <tr>
                            <td>{{ $booking->id }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title rounded-circle bg-primary text-white">
                                            {{ substr($booking->user->name, 0, 1) }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ $booking->user->name }}</div>
                                        <small class="text-muted">{{ $booking->user->email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="font-weight-bold">{{ $booking->meetingRoom->name }}</div>
                                <small class="text-muted">Kapasitas: {{ $booking->meetingRoom->capacity }} orang</small>
                            </td>
                            <td>{{ $booking->title }}</td>
                            <td>
                                <div>{{ $booking->start_time->format('d M Y') }}</div>
                                <small class="text-muted">
                                    {{ $booking->start_time->format('H:i') }} - {{ $booking->end_time->format('H:i') }}
                                </small>
                            </td>
                            <td>
                                @if($booking->status == 'pending')
                                    <span class="badge bg-warning">Menunggu</span>
                                @elseif($booking->status == 'approved')
                                    <span class="badge bg-success">Disetujui</span>
                                @elseif($booking->status == 'rejected')
                                    <span class="badge bg-danger">Ditolak</span>
                                @elseif($booking->status == 'cancelled')
                                    <span class="badge bg-secondary">Dibatalkan</span>
                                @elseif($booking->status == 'completed')
                                    <span class="badge bg-info">Selesai</span>
                                @endif
                            </td>
                            <td>{{ $booking->created_at->format('d M Y H:i') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.bookings.show', $booking) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Lihat Detail">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    @if($booking->status == 'pending')
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="approveBooking({{ $booking->id }})" title="Setujui">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="rejectBooking({{ $booking->id }})" title="Tolak">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    @endif
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="changeBookingStatus({{ $booking->id }}, '{{ $booking->status }}')" 
                                            title="Ubah Status">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </button>
                                    <a href="{{ route('admin.bookings.edit', $booking) }}" 
                                       class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="py-4">
                                    <i class="bi bi-calendar-x fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Tidak ada pemesanan yang ditemukan</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($bookings->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $bookings->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Filter Modal dengan SweetAlert2
function showFilterModal() {
    const currentParams = new URLSearchParams(window.location.search);
    
    Swal.fire({
        title: 'Filter Pemesanan',
        html: `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="swal-status" class="form-label text-start d-block">Status</label>
                    <select id="swal-status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="pending" ${currentParams.get('status') === 'pending' ? 'selected' : ''}>Menunggu</option>
                        <option value="approved" ${currentParams.get('status') === 'approved' ? 'selected' : ''}>Disetujui</option>
                        <option value="rejected" ${currentParams.get('status') === 'rejected' ? 'selected' : ''}>Ditolak</option>
                        <option value="cancelled" ${currentParams.get('status') === 'cancelled' ? 'selected' : ''}>Dibatalkan</option>
                        <option value="completed" ${currentParams.get('status') === 'completed' ? 'selected' : ''}>Selesai</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="swal-room" class="form-label text-start d-block">Ruang</label>
                    <select id="swal-room" class="form-select">
                        <option value="">Semua Ruang</option>
                        @foreach($rooms ?? [] as $room)
                        <option value="{{ $room->id }}" ${currentParams.get('room_id') === '{{ $room->id }}' ? 'selected' : ''}>{{ $room->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="swal-start-date" class="form-label text-start d-block">Tanggal Mulai</label>
                    <input type="date" id="swal-start-date" class="form-control" value="${currentParams.get('start_date') || ''}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="swal-end-date" class="form-label text-start d-block">Tanggal Akhir</label>
                    <input type="date" id="swal-end-date" class="form-control" value="${currentParams.get('end_date') || ''}">
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Terapkan Filter',
        cancelButtonText: 'Batal',
        width: '600px',
        preConfirm: () => {
            const status = document.getElementById('swal-status').value;
            const roomId = document.getElementById('swal-room').value;
            const startDate = document.getElementById('swal-start-date').value;
            const endDate = document.getElementById('swal-end-date').value;
            
            // Build URL with filters
            const url = new URL(window.location.href);
            url.search = ''; // Clear existing params
            
            if (status) url.searchParams.append('status', status);
            if (roomId) url.searchParams.append('room_id', roomId);
            if (startDate) url.searchParams.append('start_date', startDate);
            if (endDate) url.searchParams.append('end_date', endDate);
            
            window.location.href = url.toString();
        }
    });
}

// Approve booking dengan SweetAlert2
function approveBooking(bookingId) {
    Swal.fire({
        title: 'Setujui Pemesanan',
        text: 'Apakah Anda yakin ingin menyetujui pemesanan ini?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Setujui',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            performBookingAction(bookingId, 'approve');
        }
    });
}

// Reject booking dengan SweetAlert2
function rejectBooking(bookingId) {
    Swal.fire({
        title: 'Tolak Pemesanan',
        html: `
            <p>Apakah Anda yakin ingin menolak pemesanan ini?</p>
            <div class="mt-3">
                <label for="rejection-reason" class="form-label text-start d-block">Alasan Penolakan</label>
                <textarea id="rejection-reason" class="form-control" rows="3" placeholder="Masukkan alasan penolakan..."></textarea>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Tolak',
        cancelButtonText: 'Batal',
        preConfirm: () => {
            const rejectionReason = document.getElementById('rejection-reason').value.trim();
            if (!rejectionReason) {
                Swal.showValidationMessage('Alasan penolakan harus diisi');
                return false;
            }
            return rejectionReason;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performBookingAction(bookingId, 'reject', result.value);
        }
    });
}

// Perform booking action
function performBookingAction(bookingId, action, rejectionReason = null) {
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('_method', 'PATCH');
    
    if (rejectionReason) {
        formData.append('rejection_reason', rejectionReason);
    }

    // Show loading
    Swal.fire({
        title: 'Memproses...',
        text: 'Mohon tunggu sebentar',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(`/admin/bookings/${bookingId}/${action}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                // Update status badge di tabel tanpa reload
                const rows = document.querySelectorAll('#bookingsTable tbody tr');
                let foundRow = null;
                rows.forEach(row => {
                    const idCell = row.querySelector('td:first-child');
                    if (idCell && idCell.textContent.trim() == bookingId) {
                        foundRow = row;
                    }
                });
                if (foundRow && data.booking) {
                    const statusCell = foundRow.querySelector('td:nth-child(6)');
                    if (statusCell) {
                        let badgeClass = 'bg-secondary', badgeText = 'Dibatalkan';
                        if (data.booking.status === 'pending') { badgeClass = 'bg-warning'; badgeText = 'Menunggu'; }
                        if (data.booking.status === 'approved') { badgeClass = 'bg-success'; badgeText = 'Disetujui'; }
                        if (data.booking.status === 'rejected') { badgeClass = 'bg-danger'; badgeText = 'Ditolak'; }
                        if (data.booking.status === 'completed') { badgeClass = 'bg-info'; badgeText = 'Selesai'; }
                        statusCell.innerHTML = `<span class=\"badge ${badgeClass}\">${badgeText}</span>`;
                    }
                }
                // Update stats card jika data.stats tersedia
                if (data.stats) {
                    document.querySelectorAll('.card .text-warning + .h5, .card .text-warning ~ .h5').forEach(el => el.textContent = data.stats.pending);
                    document.querySelectorAll('.card .text-success + .h5, .card .text-success ~ .h5').forEach(el => el.textContent = data.stats.approved);
                    document.querySelectorAll('.card .text-info + .h5, .card .text-info ~ .h5').forEach(el => el.textContent = data.stats.rejected);
                    document.querySelectorAll('.card .text-primary + .h5, .card .text-primary ~ .h5').forEach(el => el.textContent = data.stats.total);
                }
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Terjadi kesalahan sistem. Silakan coba lagi.'
        });
    });
}

// Function to change booking status
function changeBookingStatus(bookingId, currentStatus) {
    // Define status options
    const statusOptions = {
        'pending': 'Menunggu Persetujuan',
        'approved': 'Disetujui',
        'rejected': 'Ditolak',
        'cancelled': 'Dibatalkan',
        'completed': 'Selesai'
    };

    // Create select options excluding current status
    let selectOptions = '';
    for (const [value, label] of Object.entries(statusOptions)) {
        if (value !== currentStatus) {
            selectOptions += `<option value="${value}">${label}</option>`;
        }
    }

    Swal.fire({
        title: 'Ubah Status Pemesanan',
        html: `
            <div class="mb-3">
                <label class="form-label">Status Saat Ini: <strong>${statusOptions[currentStatus]}</strong></label>
            </div>
            <div class="mb-3">
                <label for="newStatus" class="form-label">Pilih Status Baru:</label>
                <select id="newStatus" class="form-select">
                    <option value="">-- Pilih Status --</option>
                    ${selectOptions}
                </select>
            </div>
            <div class="mb-3" id="rejectionReasonDiv" style="display: none;">
                <label for="rejectionReason" class="form-label">Alasan Penolakan:</label>
                <textarea id="rejectionReason" class="form-control" rows="3" placeholder="Masukkan alasan penolakan..."></textarea>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Ubah Status',
        cancelButtonText: 'Batal',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        focusConfirm: false,
        preConfirm: () => {
            const newStatus = document.getElementById('newStatus').value;
            const rejectionReason = document.getElementById('rejectionReason').value;

            if (!newStatus) {
                Swal.showValidationMessage('Silakan pilih status baru');
                return false;
            }

            if (newStatus === 'rejected' && !rejectionReason.trim()) {
                Swal.showValidationMessage('Alasan penolakan harus diisi');
                return false;
            }

            return { status: newStatus, rejection_reason: rejectionReason };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Show rejection reason field when rejected is selected
            document.getElementById('newStatus').addEventListener('change', function() {
                const rejectionDiv = document.getElementById('rejectionReasonDiv');
                if (this.value === 'rejected') {
                    rejectionDiv.style.display = 'block';
                } else {
                    rejectionDiv.style.display = 'none';
                }
            });

            // Submit the status change
            submitStatusChange(bookingId, result.value);
        }
    });

    // Add event listener after Swal is shown
    setTimeout(() => {
        const newStatusSelect = document.getElementById('newStatus');
        if (newStatusSelect) {
            newStatusSelect.addEventListener('change', function() {
                const rejectionDiv = document.getElementById('rejectionReasonDiv');
                if (this.value === 'rejected') {
                    rejectionDiv.style.display = 'block';
                } else {
                    rejectionDiv.style.display = 'none';
                }
            });
        }
    }, 100);
}

// Function to submit status change
function submitStatusChange(bookingId, data) {
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('_method', 'PATCH');
    formData.append('status', data.status);
    if (data.rejection_reason) {
        formData.append('rejection_reason', data.rejection_reason);
    }

    // Show loading
    Swal.fire({
        title: 'Mengubah Status...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(`/admin/bookings/${bookingId}/change-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: formData
    })
    .then(async response => {
        let data;
        try {
            data = await response.json();
        } catch (e) {
            throw new Error('Response bukan JSON atau kosong.');
        }
        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }
        return data;
    })
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                // Update status badge di tabel tanpa reload
                const rows = document.querySelectorAll('#bookingsTable tbody tr');
                let foundRow = null;
                rows.forEach(row => {
                    const idCell = row.querySelector('td:first-child');
                    if (idCell && idCell.textContent.trim() == bookingId) {
                        foundRow = row;
                    }
                });
                if (foundRow && data.booking) {
                    const statusCell = foundRow.querySelector('td:nth-child(6)');
                    if (statusCell) {
                        let badgeClass = 'bg-secondary', badgeText = 'Dibatalkan';
                        if (data.booking.status === 'pending') { badgeClass = 'bg-warning'; badgeText = 'Menunggu'; }
                        if (data.booking.status === 'approved') { badgeClass = 'bg-success'; badgeText = 'Disetujui'; }
                        if (data.booking.status === 'rejected') { badgeClass = 'bg-danger'; badgeText = 'Ditolak'; }
                        if (data.booking.status === 'completed') { badgeClass = 'bg-info'; badgeText = 'Selesai'; }
                        statusCell.innerHTML = `<span class=\"badge ${badgeClass}\">${badgeText}</span>`;
                    }
                }
                // Update stats card jika data.stats tersedia
                if (data.stats) {
                    document.querySelectorAll('.card .text-warning + .h5, .card .text-warning ~ .h5').forEach(el => el.textContent = data.stats.pending);
                    document.querySelectorAll('.card .text-success + .h5, .card .text-success ~ .h5').forEach(el => el.textContent = data.stats.approved);
                    document.querySelectorAll('.card .text-info + .h5, .card .text-info ~ .h5').forEach(el => el.textContent = data.stats.rejected);
                    document.querySelectorAll('.card .text-primary + .h5, .card .text-primary ~ .h5').forEach(el => el.textContent = data.stats.total);
                }
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: error.message || 'Terjadi kesalahan sistem. Silakan coba lagi.'
        });
    });
}
</script>
@endpush
