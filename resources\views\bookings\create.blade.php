@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>emesanan - Sistem Pemesanan Ruang Rapat')
@section('page-title', '<PERSON><PERSON><PERSON>em<PERSON>')

@push('styles')
<style>
    .booking-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .booking-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .booking-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }

    .booking-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
    }

    .booking-form-container {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 3rem;
        align-items: start;
    }

    .booking-form {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px var(--shadow);
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        background: var(--bg-secondary);
        color: var(--text-primary);
        font-size: 1rem;
        transition: all 0.2s;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .room-selection {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .room-card {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.2s;
        background: var(--bg-secondary);
    }

    .room-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px var(--shadow);
    }

    .room-card.selected {
        border-color: var(--primary-color);
        background: rgba(37, 99, 235, 0.1);
    }

    .room-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .room-capacity {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .room-facilities {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        margin-top: 0.5rem;
    }

    .facility-tag {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
    }

    .booking-summary {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px var(--shadow);
        position: sticky;
        top: 2rem;
    }

    .summary-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }

    .summary-label {
        color: var(--text-secondary);
    }

    .summary-value {
        color: var(--text-primary);
        font-weight: 500;
    }

    .submit-btn {
        width: 100%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 0.75rem;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        margin-top: 1rem;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    }

    .submit-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    @media (max-width: 768px) {
        .booking-form-container {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .booking-summary {
            position: static;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .room-selection {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="booking-container">
    <div class="booking-header">
        <h1 class="booking-title">Buat Pemesanan Ruang Rapat</h1>
        <p class="booking-subtitle">Pilih ruang rapat dan tentukan jadwal yang sesuai dengan kebutuhan Anda</p>
    </div>

    <div class="booking-form-container">
        <div class="booking-form">
            <form action="{{ route('bookings.store') }}" method="POST" id="booking-form">
                @csrf
                
                <!-- Room Selection -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-door-open"></i>
                        Pilih Ruang Rapat
                    </h3>
                    
                    <input type="hidden" name="meeting_room_id" id="selected-room-id" required>
                    
                    <div class="room-selection">
                        @foreach($meetingRooms as $room)
                        <div class="room-card" onclick="selectRoom({{ $room->id }}, '{{ $room->name }}', {{ $room->capacity }})">
                            <div class="room-name">{{ $room->name }}</div>
                            <div class="room-capacity">
                                <i class="bi bi-people"></i> Kapasitas: {{ $room->capacity }} orang
                            </div>
                            @if($room->roomFacilities && $room->roomFacilities->count() > 0)
                            <div class="room-facilities">
                                @foreach($room->roomFacilities as $facility)
                                <span class="facility-tag">{{ $facility->name }}</span>
                                @endforeach
                            </div>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Meeting Details -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-clipboard-data"></i>
                        Detail Rapat
                    </h3>
                    
                    <div class="form-group">
                        <label for="title" class="form-label">Judul Rapat *</label>
                        <input type="text" id="title" name="title" class="form-control" 
                               placeholder="Masukkan judul rapat" required>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Deskripsi</label>
                        <textarea id="description" name="description" class="form-control" rows="3"
                                  placeholder="Deskripsi singkat tentang rapat (opsional)"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="participants" class="form-label">Jumlah Peserta</label>
                        <input type="number" id="participants" name="participants" class="form-control" 
                               placeholder="Masukkan jumlah peserta" min="1">
                    </div>
                </div>

                <!-- Schedule -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-calendar-event"></i>
                        Jadwal Rapat
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start_date" class="form-label">Tanggal Mulai *</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" 
                                   min="{{ date('Y-m-d') }}" required>
                        </div>
                        <div class="form-group">
                            <label for="start_time" class="form-label">Waktu Mulai *</label>
                            <input type="time" id="start_time" name="start_time" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="end_date" class="form-label">Tanggal Selesai *</label>
                            <input type="date" id="end_date" name="end_date" class="form-control" 
                                   min="{{ date('Y-m-d') }}" required>
                        </div>
                        <div class="form-group">
                            <label for="end_time" class="form-label">Waktu Selesai *</label>
                            <input type="time" id="end_time" name="end_time" class="form-control" required>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Booking Summary -->
        <div class="booking-summary">
            <h3 class="summary-title">Ringkasan Pemesanan</h3>
            
            <div class="summary-item">
                <span class="summary-label">Ruang Rapat:</span>
                <span class="summary-value" id="summary-room">Belum dipilih</span>
            </div>
            
            <div class="summary-item">
                <span class="summary-label">Kapasitas:</span>
                <span class="summary-value" id="summary-capacity">-</span>
            </div>
            
            <div class="summary-item">
                <span class="summary-label">Tanggal:</span>
                <span class="summary-value" id="summary-date">Belum dipilih</span>
            </div>
            
            <div class="summary-item">
                <span class="summary-label">Waktu:</span>
                <span class="summary-value" id="summary-time">Belum dipilih</span>
            </div>
            
            <div class="summary-item">
                <span class="summary-label">Durasi:</span>
                <span class="summary-value" id="summary-duration">-</span>
            </div>
            
            <button type="submit" form="booking-form" class="submit-btn" id="submit-btn" disabled>
                <i class="bi bi-check-circle"></i>
                Buat Pemesanan
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let selectedRoomId = null;

function selectRoom(roomId, roomName, capacity) {
    // Remove previous selection
    document.querySelectorAll('.room-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Select current room
    event.target.closest('.room-card').classList.add('selected');
    selectedRoomId = roomId;
    document.getElementById('selected-room-id').value = roomId;
    
    // Update summary
    document.getElementById('summary-room').textContent = roomName;
    document.getElementById('summary-capacity').textContent = capacity + ' orang';
    
    checkFormValidity();
    checkConflicts(); // Check for conflicts when room is selected
}

function updateSummary() {
    const startDate = document.getElementById('start_date').value;
    const startTime = document.getElementById('start_time').value;
    const endDate = document.getElementById('end_date').value;
    const endTime = document.getElementById('end_time').value;
    
    if (startDate && endDate) {
        if (startDate === endDate) {
            document.getElementById('summary-date').textContent = new Date(startDate).toLocaleDateString('id-ID');
        } else {
            document.getElementById('summary-date').textContent = 
                new Date(startDate).toLocaleDateString('id-ID') + ' - ' + 
                new Date(endDate).toLocaleDateString('id-ID');
        }
    }
    
    if (startTime && endTime) {
        document.getElementById('summary-time').textContent = startTime + ' - ' + endTime;
        
        // Calculate duration
        if (startDate && endDate) {
            const start = new Date(startDate + 'T' + startTime);
            const end = new Date(endDate + 'T' + endTime);
            const diffMs = end - start;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
            
            if (diffHours > 0 || diffMinutes > 0) {
                document.getElementById('summary-duration').textContent = 
                    (diffHours > 0 ? diffHours + ' jam ' : '') + 
                    (diffMinutes > 0 ? diffMinutes + ' menit' : '');
            }
        }
    }
    
    checkFormValidity();
}

function checkFormValidity() {
    const roomSelected = selectedRoomId !== null;
    const title = document.getElementById('title').value.trim();
    const startDate = document.getElementById('start_date').value;
    const startTime = document.getElementById('start_time').value;
    const endDate = document.getElementById('end_date').value;
    const endTime = document.getElementById('end_time').value;
    
    const isValid = roomSelected && title && startDate && startTime && endDate && endTime;
    
    document.getElementById('submit-btn').disabled = !isValid;
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Auto-set end date when start date changes
    document.getElementById('start_date').addEventListener('change', function() {
        const endDateField = document.getElementById('end_date');
        if (!endDateField.value) {
            endDateField.value = this.value;
        }
        updateSummary();
        checkConflicts(); // Check for conflicts when date changes
    });
    
    // Update summary when any field changes
    ['start_date', 'start_time', 'end_date', 'end_time', 'title'].forEach(fieldId => {
        document.getElementById(fieldId).addEventListener('change', function() {
            updateSummary();
            if (fieldId !== 'title') {
                checkConflicts(); // Check conflicts when time/date changes
            }
        });
        document.getElementById(fieldId).addEventListener('input', checkFormValidity);
    });
    
    // Form validation and submission
    document.getElementById('booking-form').addEventListener('submit', function(e) {
        e.preventDefault(); // Always prevent default to use AJAX
        
        if (!selectedRoomId) {
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan',
                text: 'Silakan pilih ruang rapat terlebih dahulu!'
            });
            return;
        }
        
        submitBooking();
    });
});

// Function to check conflicts in real-time
function checkConflicts() {
    const roomId = selectedRoomId;
    const startDate = document.getElementById('start_date').value;
    const startTime = document.getElementById('start_time').value;
    const endDate = document.getElementById('end_date').value;
    const endTime = document.getElementById('end_time').value;
    
    // Only check if all required fields are filled
    if (!roomId || !startDate || !startTime || !endDate || !endTime) {
        return;
    }
    
    const startDateTime = startDate + ' ' + startTime;
    const endDateTime = endDate + ' ' + endTime;
    
    // Clear previous conflict warning
    clearConflictWarning();
    
    fetch('{{ route("bookings.check-conflict") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            meeting_room_id: roomId,
            start_time: startDateTime,
            end_time: endDateTime
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.has_conflict) {
            showConflictWarning(data.conflict_details);
        } else {
            showAvailabilityConfirmation();
        }
    })
    .catch(error => {
        console.error('Error checking conflicts:', error);
    });
}

// Function to show conflict warning
function showConflictWarning(conflictDetails) {
    const warningHtml = `
        <div id="conflict-warning" class="alert alert-danger mt-3">
            <h6><i class="bi bi-exclamation-triangle"></i> Konflik Pemesanan Terdeteksi!</h6>
            <p><strong>Ruang ${conflictDetails.room}</strong> sudah dipesan oleh:</p>
            <ul class="mb-2">
                <li><strong>Pemesanan:</strong> ${conflictDetails.title}</li>
                <li><strong>Oleh:</strong> ${conflictDetails.user}</li>
                <li><strong>Waktu:</strong> ${conflictDetails.start_time} - ${conflictDetails.end_time}</li>
                <li><strong>Status:</strong> ${conflictDetails.status}</li>
            </ul>
            <small class="text-muted">Silakan pilih waktu yang berbeda untuk pemesanan Anda.</small>
        </div>
    `;
    
    document.getElementById('booking-form').insertAdjacentHTML('beforeend', warningHtml);
    document.getElementById('submit-btn').disabled = true;
}

// Function to show availability confirmation
function showAvailabilityConfirmation() {
    const confirmationHtml = `
        <div id="availability-confirmation" class="alert alert-success mt-3">
            <h6><i class="bi bi-check-circle"></i> Waktu Tersedia!</h6>
            <p class="mb-0">Ruang rapat tersedia pada waktu yang dipilih.</p>
        </div>
    `;
    
    document.getElementById('booking-form').insertAdjacentHTML('beforeend', confirmationHtml);
    
    // Re-enable submit button if form is valid
    setTimeout(() => {
        document.getElementById('availability-confirmation')?.remove();
    }, 3000);
}

// Function to clear conflict warnings
function clearConflictWarning() {
    document.getElementById('conflict-warning')?.remove();
    document.getElementById('availability-confirmation')?.remove();
}

// Function to submit booking via AJAX
function submitBooking() {
    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Memproses...';
    submitBtn.disabled = true;
    
    // Prepare form data
    const formData = new FormData();
    formData.append('meeting_room_id', selectedRoomId);
    formData.append('title', document.getElementById('title').value);
    formData.append('description', document.getElementById('description').value);
    formData.append('participants', document.getElementById('participants').value);
    
    const startDate = document.getElementById('start_date').value;
    const startTime = document.getElementById('start_time').value;
    const endDate = document.getElementById('end_date').value;
    const endTime = document.getElementById('end_time').value;
    
    formData.append('start_time', startDate + ' ' + startTime);
    formData.append('end_time', endDate + ' ' + endTime);
    
    fetch('{{ route("bookings.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message with booking details
            Swal.fire({
                icon: 'success',
                title: 'Pemesanan Berhasil!',
                html: `
                    <div class="text-left">
                        <p><strong>Detail Pemesanan:</strong></p>
                        <ul style="text-align: left; display: inline-block;">
                            <li><strong>Ruang:</strong> ${data.booking.room}</li>
                            <li><strong>Judul:</strong> ${data.booking.title}</li>
                            <li><strong>Waktu:</strong> ${data.booking.start_time} - ${data.booking.end_time}</li>
                        </ul>
                        <p class="mt-3 text-info">
                            <i class="bi bi-info-circle"></i> 
                            Pemesanan Anda menunggu persetujuan dari admin.
                        </p>
                    </div>
                `,
                confirmButtonText: 'Lihat Daftar Pemesanan'
            }).then(() => {
                window.location.href = data.redirect;
            });
        } else if (data.type === 'conflict') {
            // Show conflict error with details
            Swal.fire({
                icon: 'error',
                title: 'Konflik Pemesanan!',
                html: `
                    <div class="text-left">
                        <p><strong>Ruang ${data.conflict_details.room}</strong> sudah dipesan:</p>
                        <ul style="text-align: left; display: inline-block;">
                            <li><strong>Pemesanan:</strong> ${data.conflict_details.title}</li>
                            <li><strong>Oleh:</strong> ${data.conflict_details.user}</li>
                            <li><strong>Waktu:</strong> ${data.conflict_details.start_time} - ${data.conflict_details.end_time}</li>
                            <li><strong>Status:</strong> ${data.conflict_details.status}</li>
                        </ul>
                        <p class="mt-3 text-warning">
                            <i class="bi bi-exclamation-triangle"></i> 
                            Silakan pilih waktu yang berbeda untuk pemesanan Anda.
                        </p>
                    </div>
                `,
                confirmButtonText: 'Ubah Waktu'
            });
        } else {
            // Show generic error
            Swal.fire({
                icon: 'error',
                title: 'Terjadi Kesalahan',
                text: data.message || 'Tidak dapat memproses pemesanan. Silakan coba lagi.',
                confirmButtonText: 'OK'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Kesalahan Sistem',
            text: 'Terjadi kesalahan pada sistem. Silakan coba lagi.',
            confirmButtonText: 'OK'
        });
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        checkFormValidity();
    });
}


</script>
@endpush
