<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\MeetingRoom;
use App\Models\SecurityLog;
use App\Events\BookingCreated;
use App\Events\DashboardUpdated;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class BookingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('security');
    }

    public function index()
    {
        $bookings = Auth::user()->bookings()->with('meetingRoom')->orderBy('start_time', 'desc')->paginate(10);
        
        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_bookings',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return view('bookings.index', compact('bookings'));
    }

    public function create(Request $request)
    {
        $meetingRooms = MeetingRoom::where('is_active', true)->get();
        $selectedRoomId = $request->get('room');
        
        return view('bookings.create', compact('meetingRooms', 'selectedRoomId'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'participants' => 'nullable|integer|min:1'
        ]);

        // Check for conflicts dengan validasi yang lebih komprehensif
        $conflictingBooking = Booking::where('meeting_room_id', $request->meeting_room_id)
            ->where(function($query) use ($request) {
                $query->where(function($q) use ($request) {
                    // Booking yang dimulai dalam rentang waktu yang diminta
                    $q->whereBetween('start_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($q) use ($request) {
                    // Booking yang berakhir dalam rentang waktu yang diminta
                    $q->whereBetween('end_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($q) use ($request) {
                    // Booking yang mencakup seluruh rentang waktu yang diminta
                    $q->where('start_time', '<=', $request->start_time)
                      ->where('end_time', '>=', $request->end_time);
                })->orWhere(function($q) use ($request) {
                    // Booking yang dimulai sebelum dan berakhir setelah waktu yang diminta
                    $q->where('start_time', '<', $request->end_time)
                      ->where('end_time', '>', $request->start_time);
                });
            })
            ->whereIn('status', ['pending', 'approved'])
            ->with(['user', 'meetingRoom'])
            ->first();

        if ($conflictingBooking) {
            $conflictDetails = [
                'room' => $conflictingBooking->meetingRoom->name,
                'title' => $conflictingBooking->title,
                'user' => $conflictingBooking->user->name,
                'start_time' => $conflictingBooking->start_time->format('d/m/Y H:i'),
                'end_time' => $conflictingBooking->end_time->format('d/m/Y H:i'),
                'status' => $conflictingBooking->status
            ];

            return response()->json([
                'success' => false,
                'type' => 'conflict',
                'message' => 'Ruang rapat sudah dipesan pada waktu tersebut!',
                'conflict_details' => $conflictDetails
            ], 409);
        }

        $booking = Booking::create([
            'user_id' => Auth::id(),
            'meeting_room_id' => $request->meeting_room_id,
            'title' => $request->title,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'participants' => $request->participants ?? 1,
            'status' => 'pending'
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'create_booking',
            'details' => "Booking created: {$booking->title}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        // Create notifications
        NotificationService::notifyNewBooking($booking);

        // Broadcast real-time update
        event(new BookingCreated($booking));
        
        // Broadcast dashboard update
        event(new DashboardUpdated('booking_created', [
            'booking_id' => $booking->id,
            'user_id' => $booking->user_id
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Pemesanan berhasil dibuat dan menunggu persetujuan!',
            'booking' => [
                'id' => $booking->id,
                'title' => $booking->title,
                'room' => $booking->meetingRoom->name,
                'start_time' => $booking->start_time->format('d/m/Y H:i'),
                'end_time' => $booking->end_time->format('d/m/Y H:i')
            ],
            'redirect' => route('bookings.index')
        ]);
    }

    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);
        
        return view('bookings.show', compact('booking'));
    }

    public function edit(Booking $booking)
    {
        $this->authorize('update', $booking);
        
        if ($booking->status !== 'pending') {
            return back()->withErrors(['error' => 'Hanya pemesanan dengan status pending yang dapat diedit.']);
        }

        $meetingRooms = MeetingRoom::where('is_active', true)->get();
        
        return view('bookings.edit', compact('booking', 'meetingRooms'));
    }

    public function update(Request $request, Booking $booking)
    {
        $this->authorize('update', $booking);
        
        if ($booking->status !== 'pending') {
            return back()->withErrors(['error' => 'Hanya pemesanan dengan status pending yang dapat diedit.']);
        }

        $request->validate([
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'participants' => 'nullable|integer|min:1'
        ]);

        // Check for conflicts (excluding current booking)
        $conflictingBooking = Booking::where('meeting_room_id', $request->meeting_room_id)
            ->where('id', '!=', $booking->id)
            ->where(function($query) use ($request) {
                $query->where(function($q) use ($request) {
                    $q->whereBetween('start_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($q) use ($request) {
                    $q->whereBetween('end_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($q) use ($request) {
                    $q->where('start_time', '<=', $request->start_time)
                      ->where('end_time', '>=', $request->end_time);
                })->orWhere(function($q) use ($request) {
                    $q->where('start_time', '<', $request->end_time)
                      ->where('end_time', '>', $request->start_time);
                });
            })
            ->whereIn('status', ['pending', 'approved'])
            ->with(['user', 'meetingRoom'])
            ->first();

        if ($conflictingBooking) {
            return response()->json([
                'success' => false,
                'type' => 'conflict',
                'message' => 'Ruang rapat sudah dipesan pada waktu tersebut!',
                'conflict_details' => [
                    'room' => $conflictingBooking->meetingRoom->name,
                    'title' => $conflictingBooking->title,
                    'user' => $conflictingBooking->user->name,
                    'start_time' => $conflictingBooking->start_time->format('d/m/Y H:i'),
                    'end_time' => $conflictingBooking->end_time->format('d/m/Y H:i'),
                    'status' => $conflictingBooking->status
                ]
            ], 409);
        }

        $booking->update([
            'meeting_room_id' => $request->meeting_room_id,
            'title' => $request->title,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'participants' => $request->participants ?? 1
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'update_booking',
            'details' => "Booking updated: {$booking->title}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pemesanan berhasil diperbarui!',
            'booking' => [
                'id' => $booking->id,
                'title' => $booking->title,
                'room' => $booking->meetingRoom->name,
                'start_time' => $booking->start_time->format('d/m/Y H:i'),
                'end_time' => $booking->end_time->format('d/m/Y H:i')
            ],
            'redirect' => route('bookings.index')
        ]);
    }

    public function destroy(Booking $booking)
    {
        $this->authorize('delete', $booking);
        
        if ($booking->status === 'approved' && $booking->start_time <= now()) {
            return back()->withErrors(['error' => 'Tidak dapat menghapus pemesanan yang sudah berlangsung.']);
        }

        $title = $booking->title;
        $booking->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'delete_booking',
            'details' => "Booking deleted: {$title}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return redirect()->route('bookings.index')->with('success', 'Pemesanan berhasil dihapus.');
    }

    public function calendar()
    {
        $bookings = Booking::with(['meetingRoom', 'user'])
                          ->whereIn('status', ['pending', 'approved'])
                          ->get()
                          ->map(function($booking) {
                              return [
                                  'id' => $booking->id,
                                  'title' => $booking->title,
                                  'start' => $booking->start_time->toISOString(),
                                  'end' => $booking->end_time->toISOString(),
                                  'backgroundColor' => $booking->status === 'approved' ? '#28a745' : '#ffc107',
                                  'borderColor' => $booking->status === 'approved' ? '#28a745' : '#ffc107',
                                  'extendedProps' => [
                                      'room' => $booking->meetingRoom->name,
                                      'user' => $booking->user->name,
                                      'status' => $booking->status,
                                      'participants' => $booking->participants
                                  ]
                              ];
                          });

        return view('bookings.calendar', compact('bookings'));
    }

    public function checkConflict(Request $request)
    {
        $request->validate([
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'exclude_booking_id' => 'nullable|exists:bookings,id'
        ]);

        $query = Booking::where('meeting_room_id', $request->meeting_room_id)
            ->where(function($q) use ($request) {
                $q->where(function($query) use ($request) {
                    // Booking yang dimulai dalam rentang waktu yang diminta
                    $query->whereBetween('start_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($query) use ($request) {
                    // Booking yang berakhir dalam rentang waktu yang diminta
                    $query->whereBetween('end_time', [$request->start_time, $request->end_time]);
                })->orWhere(function($query) use ($request) {
                    // Booking yang mencakup seluruh rentang waktu yang diminta
                    $query->where('start_time', '<=', $request->start_time)
                          ->where('end_time', '>=', $request->end_time);
                })->orWhere(function($query) use ($request) {
                    // Booking yang dimulai sebelum dan berakhir setelah waktu yang diminta
                    $query->where('start_time', '<', $request->end_time)
                          ->where('end_time', '>', $request->start_time);
                });
            })
            ->whereIn('status', ['pending', 'approved']);

        // Exclude current booking if editing
        if ($request->exclude_booking_id) {
            $query->where('id', '!=', $request->exclude_booking_id);
        }

        $conflictingBooking = $query->with(['user', 'meetingRoom'])->first();

        if ($conflictingBooking) {
            return response()->json([
                'has_conflict' => true,
                'conflict_details' => [
                    'id' => $conflictingBooking->id,
                    'title' => $conflictingBooking->title,
                    'user' => $conflictingBooking->user->name,
                    'room' => $conflictingBooking->meetingRoom->name,
                    'start_time' => $conflictingBooking->start_time->format('d/m/Y H:i'),
                    'end_time' => $conflictingBooking->end_time->format('d/m/Y H:i'),
                    'status' => $conflictingBooking->status === 'approved' ? 'Disetujui' : 'Menunggu Persetujuan'
                ]
            ]);
        }

        return response()->json([
            'has_conflict' => false,
            'message' => 'Waktu tersedia untuk pemesanan'
        ]);
    }
}
