<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real-time Updates</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; border-radius: 5px; }
        .button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Real-time Updates - Sistem Pemesan<PERSON>at</h1>
        
        <div>
            <button class="button" onclick="testConnection()">Test Pusher Connection</button>
            <button class="button" onclick="testBookingCreated()">Test Booking Created Event</button>
            <button class="button" onclick="testStatusUpdate()">Test Status Update Event</button>
            <button class="button" onclick="clearLog()">Clear Log</button>
        </div>

        <div id="log" class="log">Test log will appear here...\n</div>
    </div>

    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script>
        const logElement = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            logElement.innerHTML = 'Test log cleared...\n';
        }

        // Initialize Pusher
        log('Initializing Pusher connection...');
        const pusher = new Pusher('d71a5703505dd468c2fa', {
            cluster: 'ap1',
            encrypted: true
        });

        pusher.connection.bind('connected', function() {
            log('✅ Pusher connected successfully!', 'success');
        });

        pusher.connection.bind('failed', function() {
            log('❌ Pusher connection failed!', 'error');
        });

        pusher.connection.bind('disconnected', function() {
            log('⚠️  Pusher disconnected', 'error');
        });

        // Subscribe to bookings channel
        log('Subscribing to bookings channel...');
        const bookingChannel = pusher.subscribe('bookings');
        
        bookingChannel.bind('pusher:subscription_succeeded', function() {
            log('✅ Successfully subscribed to bookings channel!', 'success');
        });

        bookingChannel.bind('pusher:subscription_error', function(status) {
            log('❌ Subscription error: ' + JSON.stringify(status), 'error');
        });

        // Listen for events
        bookingChannel.bind('booking.created', function(data) {
            log('📅 NEW BOOKING CREATED EVENT RECEIVED:', 'success');
            log('Data: ' + JSON.stringify(data, null, 2), 'info');
        });

        bookingChannel.bind('booking.status.updated', function(data) {
            log('🔄 BOOKING STATUS UPDATED EVENT RECEIVED:', 'success');
            log('Data: ' + JSON.stringify(data, null, 2), 'info');
        });

        function testConnection() {
            log('Testing Pusher connection state...');
            log('Connection state: ' + pusher.connection.state);
            log('Socket ID: ' + pusher.connection.socket_id);
        }

        async function testBookingCreated() {
            log('Triggering booking created event...');
            try {
                const response = await fetch('http://localhost:8000/test-pusher');
                const result = await response.json();
                log('✅ Test trigger response: ' + JSON.stringify(result), 'success');
            } catch (error) {
                log('❌ Test trigger failed: ' + error.message, 'error');
            }
        }

        async function testStatusUpdate() {
            log('Triggering booking status update event...');
            try {
                const response = await fetch('http://localhost:8000/test-status-update');
                const result = await response.json();
                log('✅ Test trigger response: ' + JSON.stringify(result), 'success');
            } catch (error) {
                log('❌ Test trigger failed: ' + error.message, 'error');
            }
        }

        // Initial connection test
        setTimeout(() => {
            testConnection();
        }, 2000);
    </script>
</body>
</html>
