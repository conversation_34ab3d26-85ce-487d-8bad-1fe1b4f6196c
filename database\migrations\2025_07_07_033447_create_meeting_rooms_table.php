<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_rooms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('capacity');
            $table->string('location');
            $table->json('facilities')->nullable(); // Array of facilities like projector, AC, etc.
            $table->string('image')->nullable();
            $table->boolean('is_active')->default(true);
            $table->decimal('hourly_rate', 10, 2)->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'capacity']);
            $table->index('location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_rooms');
    }
};
