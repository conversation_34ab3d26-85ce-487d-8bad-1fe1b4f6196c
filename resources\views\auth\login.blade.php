@extends('layouts.app')

@section('title', 'Masuk - Sistem Pemesanan Ruang Rapat')

@push('styles')
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="300" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    .login-card {
        display: grid;
        grid-template-columns: 1fr 1fr;
        max-width: 1000px;
        width: 100%;
        border-radius: 2rem;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        z-index: 1;
        position: relative;
    }

    .login-animation {
        background: var(--bg-primary);
        padding: 3rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .login-animation::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, var(--primary-color), var(--info-color), var(--success-color), var(--warning-color), var(--primary-color));
        animation: rotate 10s linear infinite;
        opacity: 0.1;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .animation-content {
        text-align: center;
        z-index: 2;
        position: relative;
    }

    .logo {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 3rem;
        color: white;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .welcome-text {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .form-section {
        background: var(--bg-primary);
        padding: 3rem;
        position: relative;
        overflow: hidden;
        min-height: 600px; /* Memastikan tinggi minimum yang cukup */
    }

    .form-container {
        position: relative;
        width: 100%;
        min-height: 500px;
        display: flex;
        flex-direction: column;
    }

    .form-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100% - 80px); /* Memberikan ruang untuk navigation di bawah */
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding-bottom: 2rem; /* Ruang tambahan di bawah untuk scroll */
        /* Custom scrollbar untuk tampilan yang bersih */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    .form-slide::-webkit-scrollbar {
        width: 0px;
        background: transparent; /* Chrome/Safari/Webkit */
    }

    /* Khusus untuk form register yang lebih panjang */
    #form-register {
        padding-bottom: 3rem;
    }

    .form-slide.active {
        opacity: 1;
        transform: translateX(0);
        z-index: 2;
    }

    .form-slide.exit {
        opacity: 0;
        transform: translateX(-100%);
        z-index: 1;
    }

    .form-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .form-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .form-subtitle {
        color: var(--text-secondary);
        font-size: 0.95rem;
    }

    .form-navigation {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: auto;
        padding-top: 1rem;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        z-index: 10;
        border-top: 1px solid var(--border-color);
        padding: 1rem;
    }

    .nav-btn {
        padding: 0.4rem 1rem;
        background: transparent;
        border: 2px solid var(--border-color);
        border-radius: 2rem;
        font-size: 0.8rem;
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.3s;
        white-space: nowrap;
        flex: 1;
        max-width: 120px;
    }

    .nav-btn.active {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
    }

    .input-group {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 1.1rem;
        z-index: 2;
    }

    .form-control {
        padding-left: 3rem;
        height: 3.5rem;
        font-size: 1rem;
        border-radius: 1rem;
        border: 2px solid var(--border-color);
        background: var(--bg-secondary);
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        background: var(--bg-primary);
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    }

    .form-control:focus + .input-icon {
        color: var(--primary-color);
    }

    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-muted);
        cursor: pointer;
        font-size: 1.1rem;
        z-index: 2;
    }

    .password-toggle:hover {
        color: var(--primary-color);
    }

    .form-actions {
        margin-top: 2rem;
    }

    .btn-login {
        width: 100%;
        height: 3.5rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 1rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        border: none;
        color: white;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px rgba(37, 99, 235, 0.4);
    }

    .btn-login::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .btn-login:hover::before {
        left: 100%;
    }

    .form-links {
        text-align: center;
        margin-top: 1.5rem;
    }

    .form-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
    }

    .form-link:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }

    .form-divider {
        color: var(--text-secondary);
        margin: 0 0.5rem;
        font-weight: 300;
    }

    .loading {
        display: none;
        position: relative;
    }

    .loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        margin: auto;
        border: 2px solid white;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .login-card {
            grid-template-columns: 1fr;
            margin: 1rem;
        }

        .login-animation {
            padding: 2rem;
        }

        .form-section {
            padding: 2rem;
            min-height: 500px;
        }

        .form-container {
            min-height: 400px;
        }

        .welcome-text {
            font-size: 1.5rem;
        }

        .form-title {
            font-size: 1.5rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }

        .form-navigation {
            flex-wrap: wrap;
            gap: 0.5rem;
            padding: 0.8rem;
        }

        .nav-btn {
            padding: 0.4rem 1rem;
            font-size: 0.8rem;
        }

        .form-slide {
            height: calc(100% - 70px); /* Sesuaikan untuk mobile */
        }
    }
</style>
@endpush

@section('content')
<div class="login-container">
    <div class="login-card">
        <!-- Left side - Animation -->
        <div class="login-animation">
            <div class="animation-content">
                <div class="logo">
                    <i class="bi bi-calendar-check"></i>
                </div>
                <h1 class="welcome-text">Selamat Datang</h1>
                <p class="welcome-subtitle">
                    Sistem Pemesanan Ruang Rapat yang aman dan modern.<br>
                    Kelola jadwal rapat Anda dengan mudah dan efisien.
                </p>
            </div>
        </div>

        <!-- Right side - Forms -->
        <div class="form-section">
            <div class="form-container">
                <!-- Login Form -->
                <div class="form-slide active" id="form-login">
                    <div class="form-header">
                        <h2 class="form-title">Masuk ke Akun</h2>
                        <p class="form-subtitle">Masukkan username dan password Anda</p>
                    </div>

                    <form action="{{ route('login') }}" method="POST" id="login-form">
                        @csrf
                        <div class="input-group">
                            <i class="bi bi-person input-icon"></i>
                            <input type="text" class="form-control" name="username" 
                                   placeholder="Username" required>
                        </div>

                        <div class="input-group">
                            <i class="bi bi-lock input-icon"></i>
                            <input type="password" class="form-control" name="password" 
                                   placeholder="Password" required id="login-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('login-password', this)">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-login">
                                <span class="btn-text">Masuk</span>
                                <div class="loading"></div>
                            </button>
                        </div>
                    </form>

                    <div class="form-links">
                        <a href="#" class="form-link" onclick="showForm('reset')">Lupa password?</a>
                        <span class="form-divider">•</span>
                        <a href="#" class="form-link" onclick="showForm('verify')">Verifikasi Email</a>
                    </div>
                </div>

                <!-- Register Form -->
                <div class="form-slide" id="form-register">
                    <div class="form-header">
                        <h2 class="form-title">Buat Akun Baru</h2>
                        <p class="form-subtitle">Daftarkan diri Anda untuk menggunakan sistem</p>
                    </div>

                    <form action="{{ route('register') }}" method="POST" id="register-form">
                        @csrf
                        <div class="input-group">
                            <i class="bi bi-person input-icon"></i>
                            <input type="text" class="form-control" name="name" 
                                   placeholder="Nama Lengkap" required>
                        </div>

                        <div class="input-group">
                            <i class="bi bi-person-badge input-icon"></i>
                            <input type="text" class="form-control" name="username" 
                                   placeholder="Username" required>
                        </div>

                        <div class="input-group">
                            <i class="bi bi-envelope input-icon"></i>
                            <input type="email" class="form-control" name="email" 
                                   placeholder="Email" required>
                        </div>

                        <div class="input-group">
                            <i class="bi bi-phone input-icon"></i>
                            <input type="text" class="form-control" name="phone" 
                                   placeholder="No. Telepon">
                        </div>

                        <div class="input-group">
                            <i class="bi bi-building input-icon"></i>
                            <input type="text" class="form-control" name="department" 
                                   placeholder="Departemen">
                        </div>

                        <div class="input-group">
                            <i class="bi bi-lock input-icon"></i>
                            <input type="password" class="form-control" name="password" 
                                   placeholder="Password" required id="register-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('register-password', this)">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>

                        <div class="input-group">
                            <i class="bi bi-lock input-icon"></i>
                            <input type="password" class="form-control" name="password_confirmation" 
                                   placeholder="Konfirmasi Password" required id="register-password-confirm">
                            <button type="button" class="password-toggle" onclick="togglePassword('register-password-confirm', this)">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-login">
                                <span class="btn-text">Daftar</span>
                                <div class="loading"></div>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Reset Password Form -->
                <div class="form-slide" id="form-reset">
                    <div class="form-header">
                        <h2 class="form-title">Reset Password</h2>
                        <p class="form-subtitle">Masukkan email untuk reset password</p>
                    </div>

                    <form action="{{ route('password.email') }}" method="POST" id="reset-form">
                        @csrf
                        <div class="input-group">
                            <i class="bi bi-envelope input-icon"></i>
                            <input type="email" class="form-control" name="email" 
                                   placeholder="Email" required>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-login">
                                <span class="btn-text">Kirim Link Reset</span>
                                <div class="loading"></div>
                            </button>
                        </div>
                    </form>

                    <div class="form-links">
                        <a href="#" class="form-link" onclick="showForm('login')">Kembali ke login</a>
                    </div>
                </div>

                <!-- Email Verification Form -->
                <div class="form-slide" id="form-verify">
                    <div class="form-header">
                        <h2 class="form-title">Verifikasi Email</h2>
                        <p class="form-subtitle">Masukkan email untuk mendapatkan link verifikasi baru</p>
                    </div>

                    <form action="{{ route('verification.send') }}" method="POST" id="verify-form">
                        @csrf
                        <div class="input-group">
                            <i class="bi bi-envelope-check input-icon"></i>
                            <input type="email" class="form-control" name="email" 
                                   placeholder="Email yang perlu diverifikasi" required>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-login">
                                <span class="btn-text">Kirim Email Verifikasi</span>
                                <div class="loading"></div>
                            </button>
                        </div>
                    </form>

                    <div class="form-links">
                        <a href="#" class="form-link" onclick="showForm('login')">Kembali ke login</a>
                        <span class="form-divider">•</span>
                        <a href="#" class="form-link" onclick="showForm('register')">Belum punya akun?</a>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="form-navigation">
                    <button class="nav-btn active" onclick="showForm('login')" id="btn-login">
                        <i class="bi bi-box-arrow-in-right me-1"></i> Masuk
                    </button>
                    <button class="nav-btn" onclick="showForm('register')" id="btn-register">
                        <i class="bi bi-person-plus me-1"></i> Daftar
                    </button>
                    <button class="nav-btn" onclick="showForm('reset')" id="btn-reset">
                        <i class="bi bi-key me-1"></i> Reset
                    </button>
                    <button class="nav-btn" onclick="showForm('verify')" id="btn-verify">
                        <i class="bi bi-envelope-check me-1"></i> Verifikasi
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let currentForm = 'login';

    function showForm(formType) {
        if (currentForm === formType) return;

        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`btn-${formType}`).classList.add('active');

        // Animate form transition
        const currentSlide = document.getElementById(`form-${currentForm}`);
        const newSlide = document.getElementById(`form-${formType}`);

        // Exit current form
        currentSlide.classList.add('exit');
        currentSlide.classList.remove('active');

        // Enter new form after a delay
        setTimeout(() => {
            currentSlide.classList.remove('exit');
            newSlide.classList.add('active');
            currentForm = formType;
        }, 250);
    }

    function togglePassword(inputId, button) {
        const input = document.getElementById(inputId);
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }
    }

    // Form submission with loading state
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('.btn-text');
            const loading = submitBtn.querySelector('.loading');

            // Special handling for verification form
            if (this.id === 'verify-form') {
                const email = this.querySelector('input[name="email"]').value;
                if (!email) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'Email Diperlukan',
                        text: 'Silakan masukkan email yang perlu diverifikasi.',
                        icon: 'warning',
                        confirmButtonColor: '#ffc107'
                    });
                    return;
                }
                
                // Show confirmation before sending
                e.preventDefault();
                Swal.fire({
                    title: 'Kirim Email Verifikasi?',
                    text: `Email verifikasi akan dikirim ke ${email}`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#667eea',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Kirim',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        btnText.style.display = 'none';
                        loading.style.display = 'block';
                        submitBtn.disabled = true;
                        
                        // Send AJAX request instead of form submission
                        const formData = new FormData(this);
                        
                        fetch(this.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.text().then(text => {
                                    try {
                                        return JSON.parse(text);
                                    } catch (e) {
                                        throw new Error(`HTTP ${response.status}: ${text.substring(0, 100)}...`);
                                    }
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Hide loading state
                            btnText.style.display = 'block';
                            loading.style.display = 'none';
                            submitBtn.disabled = false;
                            
                            if (data.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: data.message,
                                    icon: 'success',
                                    confirmButtonColor: '#667eea',
                                    confirmButtonText: 'OK',
                                    timer: 5000,
                                    timerProgressBar: true
                                });
                                
                                // Clear form
                                this.reset();
                            } else {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: data.message || 'Terjadi kesalahan saat mengirim email verifikasi.',
                                    icon: 'error',
                                    confirmButtonColor: '#667eea',
                                    confirmButtonText: 'OK'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            
                            // Hide loading state
                            btnText.style.display = 'block';
                            loading.style.display = 'none';
                            submitBtn.disabled = false;
                            
                            let errorMessage = 'Terjadi kesalahan saat mengirim email verifikasi.';
                            
                            if (error.message) {
                                if (error.message.includes('HTTP 422')) {
                                    errorMessage = 'Data yang Anda masukkan tidak valid. Periksa email Anda.';
                                } else if (error.message.includes('HTTP 404')) {
                                    errorMessage = 'Email tidak ditemukan dalam sistem.';
                                } else if (error.message.includes('HTTP 429')) {
                                    errorMessage = 'Terlalu banyak permintaan. Silakan coba lagi nanti.';
                                }
                            }
                            
                            Swal.fire({
                                title: 'Error!',
                                text: errorMessage,
                                icon: 'error',
                                confirmButtonColor: '#667eea',
                                confirmButtonText: 'OK'
                            });
                        });
                    }
                });
                return;
            }

            btnText.style.display = 'none';
            loading.style.display = 'block';
            submitBtn.disabled = true;

            // Re-enable button after 5 seconds (in case of errors)
            setTimeout(() => {
                btnText.style.display = 'block';
                loading.style.display = 'none';
                submitBtn.disabled = false;
            }, 5000);
        });
    });

    // Auto-focus first input when form changes
    function focusFirstInput() {
        const activeForm = document.querySelector('.form-slide.active');
        const firstInput = activeForm.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 300);
        }
    }

    // Initialize
    focusFirstInput();

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') return; // Allow normal tab navigation
        
        if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
            const activeForm = document.querySelector('.form-slide.active form');
            if (activeForm) {
                activeForm.requestSubmit();
            }
        }
    });

    // Password strength indicator (for register form)
    document.getElementById('register-password')?.addEventListener('input', function() {
        const password = this.value;
        const requirements = [
            { test: password.length >= 8, text: 'Minimal 8 karakter' },
            { test: /[A-Z]/.test(password), text: 'Huruf besar' },
            { test: /[a-z]/.test(password), text: 'Huruf kecil' },
            { test: /\d/.test(password), text: 'Angka' },
            { test: /[!@#$%^&*(),.?":{}|<>]/.test(password), text: 'Simbol' }
        ];

        // You can add password strength indicator UI here
    });

    // Check for verification success parameter
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('verified') === '1') {
            Swal.fire({
                title: 'Email Berhasil Diverifikasi!',
                text: 'Akun Anda telah aktif. Silakan login dengan username dan password Anda.',
                icon: 'success',
                confirmButtonText: 'Login Sekarang',
                confirmButtonColor: '#667eea',
                timer: 5000,
                timerProgressBar: true,
                showClass: {
                    popup: 'animate__animated animate__fadeInDown'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                }
            }).then(() => {
                // Remove the parameter from URL
                window.history.replaceState({}, document.title, window.location.pathname);
                // Focus on username input
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            });
        }

        // Show logout success message
        @if(session('logout_success'))
            Swal.fire({
                title: 'Logout Berhasil!',
                text: '{{ session('logout_success') }}',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#667eea',
                timer: 4000,
                timerProgressBar: true
            });
        @endif

        // Show success message from verification email resend
        @if(session('success'))
            showForm('verify'); // Show verification form
            Swal.fire({
                title: 'Email Terkirim!',
                text: '{{ session('success') }}',
                icon: 'success',
                confirmButtonText: 'Mengerti',
                confirmButtonColor: '#667eea',
                timer: 5000,
                timerProgressBar: true
            });
        @endif

        // Show error message from verification email resend
        @if($errors->has('email') && request()->route()->getName() === 'verification.send')
            showForm('verify'); // Show verification form
            Swal.fire({
                title: 'Gagal Mengirim Email',
                text: '{{ $errors->first('email') }}',
                icon: 'error',
                confirmButtonText: 'Coba Lagi',
                confirmButtonColor: '#dc3545'
            });
        @endif
    });

    @if(session('sweetalert'))
    <script>
        Swal.fire({
            icon: '{{ session('sweetalert.icon') }}',
            title: '{{ session('sweetalert.title') }}',
            text: '{{ session('sweetalert.text') }}',
            confirmButtonText: 'OK'
        });
    </script>
    @endif
</script>
@endpush
