<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Security tracking fields
            $table->timestamp('last_password_change')->nullable();
            $table->json('login_history')->nullable(); // Store last 10 logins
            $table->json('failed_login_history')->nullable(); // Store failed attempts
            $table->string('account_status')->default('active'); // active, suspended, locked, banned
            $table->timestamp('account_locked_at')->nullable();
            $table->string('account_locked_reason')->nullable();
            $table->integer('security_score')->default(100); // 0-100, decreases with suspicious activity
            $table->json('security_flags')->nullable(); // Array of security concerns
            $table->timestamp('terms_accepted_at')->nullable();
            $table->string('terms_version')->nullable();
            $table->boolean('two_factor_enabled')->default(false);
            $table->string('two_factor_secret')->nullable();
            $table->json('trusted_devices')->nullable(); // Device fingerprints
            $table->integer('concurrent_sessions_limit')->default(1); // Admin can have unlimited
            $table->timestamp('password_expires_at')->nullable();
            $table->boolean('force_password_change')->default(false);
            $table->json('security_questions')->nullable(); // Backup security
            $table->timestamp('last_security_check')->nullable();
            $table->string('risk_level')->default('low'); // low, medium, high, critical
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'last_password_change',
                'login_history',
                'failed_login_history',
                'account_status',
                'account_locked_at',
                'account_locked_reason',
                'security_score',
                'security_flags',
                'terms_accepted_at',
                'terms_version',
                'two_factor_enabled',
                'two_factor_secret',
                'trusted_devices',
                'concurrent_sessions_limit',
                'password_expires_at',
                'force_password_change',
                'security_questions',
                'last_security_check',
                'risk_level'
            ]);
        });
    }
};
