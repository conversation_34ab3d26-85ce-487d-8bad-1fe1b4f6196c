@extends('layouts.dashboard')

@section('title', 'Dashboard Admin')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Dashboard Admin</h1>
            <p class="text-muted">Kelola sistem pemesanan ruang rapat</p>
        </div>
        <div class="text-end">
            <small class="text-muted">{{ now()->format('d M Y, H:i') }}</small>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pengguna
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalUsers ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Ruang Rapat
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalRooms ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pemesanan Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $todayBookings ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-event fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Menunggu Persetujuan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $pendingBookings ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart and Activity -->
    <div class="row">
        <!-- Booking Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Statistik Pemesanan Bulanan</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="bookingChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Aktivitas Terbaru</h6>
                </div>
                <div class="card-body">
                    @if(isset($recentActivities) && count($recentActivities) > 0)
                        @foreach($recentActivities as $activity)
                        <div class="mb-3 border-bottom pb-2">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    @if($activity->action === 'login')
                                        <i class="bi bi-box-arrow-in-right text-success"></i>
                                    @elseif($activity->action === 'booking')
                                        <i class="bi bi-calendar-plus text-info"></i>
                                    @else
                                        <i class="bi bi-activity text-warning"></i>
                                    @endif
                                </div>
                                <div class="ms-3">
                                    <div class="small text-gray-800">{{ $activity->description }}</div>
                                    <div class="small text-muted">{{ $activity->created_at->diffForHumans() }}</div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted">Belum ada aktivitas terbaru</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aksi Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6 mb-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-primary btn-block">
                                <i class="bi bi-people me-1"></i> Kelola Pengguna
                            </a>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <a href="{{ route('admin.bookings.index') }}" class="btn btn-success btn-block">
                                <i class="bi bi-calendar-check me-1"></i> Kelola Pemesanan
                            </a>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <a href="{{ route('admin.security.logs') }}" class="btn btn-warning btn-block">
                                <i class="bi bi-shield-check me-1"></i> Log Keamanan
                            </a>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <a href="{{ route('admin.reports') }}" class="btn btn-info btn-block">
                                <i class="bi bi-graph-up me-1"></i> Laporan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Sistem</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Database</span>
                            <span class="badge bg-success">Online</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Real-time (Pusher)</span>
                            <span class="badge bg-success">Aktif</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Email Service</span>
                            <span class="badge bg-success">Berjalan</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Storage</span>
                            <span class="badge bg-warning">75% Terpakai</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
try {
    // Chart.js untuk statistik pemesanan
    const ctx = document.getElementById('bookingChart').getContext('2d');
    const chartData = {!! json_encode($monthlyBookings ?? []) !!};
    const chartLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const bookingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartLabels,
            datasets: [{
                label: 'Pemesanan',
                data: Array.isArray(chartData) ? chartData : [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
} catch (e) {
    console.error('Chart.js error:', e);
}
</script>
@endpush
@endsection
