<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SecurityLog;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        if (!$user->isAdmin()) {
            SecurityLog::logAction(
                'unauthorized_admin_access_attempt',
                $user->id,
                'high',
                [
                    'attempted_url' => $request->fullUrl(),
                    'user_role' => $user->role
                ],
                session()->getId()
            );

            abort(403, 'Aks<PERSON> ditolak. Anda tidak memiliki hak administrator.');
        }

        return $next($request);
    }
}
