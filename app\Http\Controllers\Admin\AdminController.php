<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Booking;
use App\Models\MeetingRoom;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->middleware('security');
    }

    public function dashboard()
    {
        // Statistik dasar
        $totalUsers = User::count();
        $totalRooms = MeetingRoom::count();
        $todayBookings = Booking::whereDate('start_time', today())->count();
        $pendingBookings = Booking::where('status', 'pending')->count();
        
        // Data untuk chart (12 bulan terakhir)
        $monthlyBookings = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyBookings[] = Booking::whereYear('created_at', $date->year)
                                      ->whereMonth('created_at', $date->month)
                                      ->count();
        }
        
        // Aktivitas terbaru (10 terakhir)
        $recentActivities = SecurityLog::with('user')
                                     ->orderBy('created_at', 'desc')
                                     ->limit(10)
                                     ->get();

        $stats = [
            'total_users' => $totalUsers,
            'active_users' => User::where(function($query) {
                $query->whereNull('locked_until')
                      ->orWhere('locked_until', '<=', now());
            })->count(),
            'total_bookings' => Booking::count(),
            'pending_bookings' => $pendingBookings,
            'approved_bookings' => Booking::where('status', 'approved')->count(),
            'total_rooms' => $totalRooms,
            'active_rooms' => MeetingRoom::where('is_active', true)->count(),
        ];

        // Recent activities
        $recentBookings = Booking::with(['user', 'meetingRoom'])
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        $recentUsers = User::orderBy('created_at', 'desc')
                          ->limit(10)
                          ->get();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_admin_dashboard',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => 'Admin mengakses dashboard',
            'severity' => 'info'
        ]);

        return view('admin.dashboard', compact(
            'stats', 
            'recentBookings', 
            'recentUsers',
            'totalUsers',
            'totalRooms', 
            'todayBookings', 
            'pendingBookings',
            'monthlyBookings',
            'recentActivities'
        ));
    }

    /**
     * Get admin dashboard data for AJAX updates
     */
    public function reports(Request $request)
    {
        $period = $request->get('period', 'month');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        
        // Tentukan rentang tanggal berdasarkan periode
        switch ($period) {
            case 'today':
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'quarter':
                $startDate = now()->startOfQuarter();
                $endDate = now()->endOfQuarter();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                $endDate = now()->endOfYear();
                break;
            case 'custom':
                $startDate = $dateFrom ? \Carbon\Carbon::parse($dateFrom)->startOfDay() : now()->startOfMonth();
                $endDate = $dateTo ? \Carbon\Carbon::parse($dateTo)->endOfDay() : now()->endOfMonth();
                break;
            default:
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
        }
        
        // Statistik dasar
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $approvedBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->where('status', 'approved')->count();
        $activeUsers = User::whereHas('bookings', function($q) use ($startDate, $endDate) {
            $q->whereBetween('created_at', [$startDate, $endDate]);
        })->count();
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();

        // Periode sebelumnya untuk growth
        $prevStart = $startDate->copy()->subDays($endDate->diffInDays($startDate) + 1);
        $prevEnd = $startDate->copy()->subDay();
        $prevTotalBookings = Booking::whereBetween('created_at', [$prevStart, $prevEnd])->count();
        $bookingGrowth = $prevTotalBookings > 0 ? round((($totalBookings - $prevTotalBookings) / $prevTotalBookings) * 100) : ($totalBookings > 0 ? 100 : 0);

        $approvalRate = $totalBookings > 0 ? round(($approvedBookings / $totalBookings) * 100) : 0;

        // Utilisasi: hitung total slot waktu yang tersedia dan terpakai (misal: 1 slot = 1 jam per ruang)
        $totalRooms = MeetingRoom::count();
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $slotPerDay = 8; // misal 8 jam kerja per hari
        $totalSlots = $totalRooms * $totalDays * $slotPerDay;
        $utilizationRate = $totalSlots > 0 ? round(($approvedBookings / $totalSlots) * 100) : 0;

        $stats = [
            'total_bookings' => $totalBookings,
            'approved_bookings' => $approvedBookings,
            'active_users' => $activeUsers,
            'new_users' => $newUsers,
            'booking_growth' => $bookingGrowth,
            'approval_rate' => $approvalRate,
            'utilization_rate' => $utilizationRate
        ];
        
        // Ruang terpopuler
        $popularRooms = MeetingRoom::withCount(['bookings' => function($q) use ($startDate, $endDate) {
            $q->whereBetween('created_at', [$startDate, $endDate]);
        }])->orderByDesc('bookings_count')->limit(5)->get();
        
        // Pengguna teraktif
        $activeUsers = User::withCount(['bookings' => function($q) use ($startDate, $endDate) {
            $q->whereBetween('created_at', [$startDate, $endDate]);
        }])->orderByDesc('bookings_count')->limit(5)->get();
        
        // Data trend untuk chart
        $trendLabels = [];
        $trendData = [];
        $approvedTrendData = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $trendLabels[] = $date->format('M Y');
            $trendData[] = Booking::whereYear('created_at', $date->year)
                                 ->whereMonth('created_at', $date->month)
                                 ->count();
            $approvedTrendData[] = Booking::whereYear('created_at', $date->year)
                                         ->whereMonth('created_at', $date->month)
                                         ->where('status', 'approved')
                                         ->count();
        }
        
        // Data utilisasi ruang untuk pie chart
        $roomNames = $popularRooms->pluck('name')->toArray();
        $roomUtilizationData = $popularRooms->pluck('bookings_count')->toArray();
        
        // Laporan harian detail
        $dailyReports = Booking::whereBetween('created_at', [$startDate, $endDate])
                              ->selectRaw('DATE(created_at) as date, COUNT(*) as total_bookings')
                              ->selectRaw('SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_bookings')
                              ->selectRaw('SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_bookings')
                              ->groupBy('date')
                              ->orderBy('date', 'desc')
                              ->get()
                              ->map(function($item) {
                                  $item->utilization_rate = $item->total_bookings > 0 
                                      ? round(($item->approved_bookings / $item->total_bookings) * 100) 
                                      : 0;
                                  return $item;
                              });
        
        return view('admin.reports', compact(
            'stats',
            'popularRooms',
            'activeUsers',
            'trendLabels',
            'trendData',
            'approvedTrendData',
            'roomNames',
            'roomUtilizationData',
            'dailyReports'
        ));
    }

    public function getBookingStats(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $bookings = Booking::whereBetween('created_at', [$startDate, $endDate])
                          ->selectRaw('DATE(created_at) as date, status, COUNT(*) as count')
                          ->groupBy('date', 'status')
                          ->orderBy('date')
                          ->get();

        $roomUsage = Booking::with('meetingRoom')
                           ->whereBetween('start_time', [$startDate, $endDate])
                           ->where('status', 'approved')
                           ->selectRaw('meeting_room_id, COUNT(*) as usage_count')
                           ->groupBy('meeting_room_id')
                           ->orderByDesc('usage_count')
                           ->get();

        return response()->json([
            'monthly_bookings' => $monthlyBookings,
            'room_usage' => $roomUsage
        ]);
    }

    /**
     * Get admin dashboard data for real-time updates
     */
    public function getDashboardData()
    {
        $data = [
            'totalUsers' => User::count(),
            'activeUsers' => User::where(function($query) {
                $query->whereNull('locked_until')
                      ->orWhere('locked_until', '<=', now());
            })->count(),
            'totalRooms' => MeetingRoom::count(),
            'activeRooms' => MeetingRoom::active()->count(),
            'totalBookings' => Booking::count(),
            'pendingApprovals' => Booking::where('status', 'pending')->count(),
            'todayBookings' => Booking::whereDate('start_time', today())->count(),
            'securityAlerts' => SecurityLog::where(function($query) {
                $query->where('severity', 'warning')
                      ->orWhere('severity', 'error');
            })->where('created_at', '>=', now()->subDays(7))->count(),
            'recentBookings' => Booking::with(['user', 'meetingRoom'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(),
        ];

        return response()->json($data);
    }
}
