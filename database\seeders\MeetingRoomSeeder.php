<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MeetingRoom;

class MeetingRoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $meetingRooms = [
            [
                'name' => 'Ruang Meeting Executive',
                'description' => '<PERSON><PERSON> meeting mewah untuk rapat eksekutif dengan fasilitas lengkap dan pemandangan kota.',
                'capacity' => 12,
                'location' => 'Lantai 15, Gedung Utama',
                'facilities' => [
                    'Proyektor 4K',
                    'Smart TV 75 inch',
                    'AC Central',
                    'Whiteboard Digital',
                    'Audio System',
                    'WiFi Premium',
                    'Coffee Machine',
                    'Video Conference',
                    'Kursi Executive',
                    'Meja Kayu Jati'
                ],
                'hourly_rate' => 150000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Brainstorming',
                'description' => 'Ruang kreatif dengan desain modern untuk sesi brainstorming dan diskusi tim.',
                'capacity' => 8,
                'location' => 'Lantai 10, Sayap Barat',
                'facilities' => [
                    'Whiteboard Besar',
                    'Sticky Notes',
                    'Marker Warna-warni',
                    'Flipchart',
                    'Proyektor Portable',
                    'WiFi',
                    'AC',
                    'Kursi Fleksibel',
                    'Meja Bundar'
                ],
                'hourly_rate' => 75000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Training',
                'description' => 'Ruang pelatihan dengan kapasitas besar dilengkapi fasilitas presentasi modern.',
                'capacity' => 25,
                'location' => 'Lantai 5, Gedung Training',
                'facilities' => [
                    'Proyektor HD',
                    'Screen Besar',
                    'Sound System',
                    'Microphone',
                    'AC',
                    'WiFi',
                    'Meja Training',
                    'Kursi Ergonomis',
                    'Dispenser Air'
                ],
                'hourly_rate' => 100000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Meeting Kecil A',
                'description' => 'Ruang meeting intimate untuk diskusi tim kecil dengan suasana nyaman.',
                'capacity' => 4,
                'location' => 'Lantai 8, Koridor A',
                'facilities' => [
                    'Smart TV 42 inch',
                    'AC',
                    'WiFi',
                    'Whiteboard',
                    'Kursi Empuk',
                    'Meja Oval'
                ],
                'hourly_rate' => 50000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Meeting Kecil B',
                'description' => 'Ruang meeting kecil dengan fasilitas dasar untuk rapat singkat.',
                'capacity' => 4,
                'location' => 'Lantai 8, Koridor B',
                'facilities' => [
                    'TV LED 32 inch',
                    'AC',
                    'WiFi',
                    'Whiteboard',
                    'Kursi Standard',
                    'Meja Persegi'
                ],
                'hourly_rate' => 50000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Presentasi',
                'description' => 'Ruang khusus presentasi dengan teknologi canggih dan tata cahaya profesional.',
                'capacity' => 15,
                'location' => 'Lantai 12, Gedung Utama',
                'facilities' => [
                    'Proyektor 4K Laser',
                    'Screen Motorized',
                    'Lighting Control',
                    'Audio Premium',
                    'Video Conference 4K',
                    'AC Premium',
                    'WiFi Fiber',
                    'Kursi Theater',
                    'Podium Digital'
                ],
                'hourly_rate' => 125000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Board Meeting',
                'description' => 'Ruang rapat dewan dengan kemewahan tinggi dan privasi maksimal.',
                'capacity' => 20,
                'location' => 'Lantai 20, Penthouse',
                'facilities' => [
                    'Meja Board Mahogany',
                    'Kursi Kulit Executive',
                    'Smart TV 85 inch',
                    'Audio Surround',
                    'AC VIP',
                    'WiFi Premium',
                    'Mini Bar',
                    'Toilet Private',
                    'Balkon View',
                    'Security Access'
                ],
                'hourly_rate' => 250000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Workshop',
                'description' => 'Ruang workshop dengan setup fleksibel untuk berbagai kegiatan pelatihan.',
                'capacity' => 30,
                'location' => 'Lantai 3, Gedung Workshop',
                'facilities' => [
                    'Meja Portable',
                    'Kursi Stackable',
                    'Proyektor Multiple',
                    'Sound System',
                    'AC Central',
                    'WiFi',
                    'Flipchart Multiple',
                    'Dispenser Air',
                    'Pantry Kecil'
                ],
                'hourly_rate' => 90000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Teleconference',
                'description' => 'Ruang khusus teleconference dengan teknologi video conference terdepan.',
                'capacity' => 6,
                'location' => 'Lantai 11, IT Center',
                'facilities' => [
                    'Video Conference Pro',
                    'Multiple Cameras',
                    'Audio Echo Cancellation',
                    'Smart TV Dual',
                    'AC Silent',
                    'WiFi Dedicated',
                    'Lighting Professional',
                    'Background Green Screen'
                ],
                'hourly_rate' => 100000,
                'is_active' => true,
            ],
            [
                'name' => 'Ruang Meeting Outdoor',
                'description' => 'Ruang meeting semi-outdoor dengan pemandangan taman untuk suasana fresh.',
                'capacity' => 10,
                'location' => 'Lantai 6, Rooftop Garden',
                'facilities' => [
                    'Meja Tahan Cuaca',
                    'Kursi Outdoor',
                    'Payung Besar',
                    'WiFi Outdoor',
                    'Sound Portable',
                    'Flipchart Portable',
                    'Garden View',
                    'Fresh Air'
                ],
                'hourly_rate' => 80000,
                'is_active' => true,
            ],
        ];

        foreach ($meetingRooms as $roomData) {
            MeetingRoom::create($roomData);
        }

        $this->command->info('Created ' . count($meetingRooms) . ' meeting rooms');
    }
}
