<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Global middleware
        $middleware->web(append: [
            \App\Http\Middleware\SecurityMiddleware::class,
            \App\Http\Middleware\AntiDDoSMiddleware::class,
        ]);

        // Route middleware aliases
        $middleware->alias([
            'security' => \App\Http\Middleware\SecurityMiddleware::class,
            'prevent.double.login' => \App\Http\Middleware\PreventDoubleLogin::class,
            'rate.limit' => \App\Http\Middleware\RateLimitingMiddleware::class,
            'anti.ddos' => \App\Http\Middleware\AntiDDoSMiddleware::class,
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
        ]);

        // Throttle middleware for API routes
        $middleware->api(prepend: [
            'throttle:api',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
