<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\MeetingRoom;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MeetingRoomTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_handles_facilities_as_json_string()
    {
        $room = new MeetingRoom();
        $room->name = 'Test Room';
        $room->capacity = 10;
        $room->location = 'Test Location';
        $room->facilities = '["projector", "wifi", "whiteboard"]'; // JSON string
        $room->is_active = true;
        $room->save();

        // Test that facilities_list accessor works with JSON string
        $facilitiesList = $room->facilities_list;
        $this->assertEquals('projector, wifi, whiteboard', $facilitiesList);
    }

    /** @test */
    public function it_handles_facilities_as_array()
    {
        $room = new MeetingRoom();
        $room->name = 'Test Room';
        $room->capacity = 10;
        $room->location = 'Test Location';
        $room->facilities = ['projector', 'wifi', 'whiteboard']; // Array
        $room->is_active = true;
        $room->save();

        // Test that facilities_list accessor works with array
        $facilitiesList = $room->facilities_list;
        $this->assertEquals('projector, wifi, whiteboard', $facilitiesList);
    }

    /** @test */
    public function it_handles_null_facilities()
    {
        $room = new MeetingRoom();
        $room->name = 'Test Room';
        $room->capacity = 10;
        $room->location = 'Test Location';
        $room->facilities = null;
        $room->is_active = true;
        $room->save();

        // Test that facilities_list accessor works with null
        $facilitiesList = $room->facilities_list;
        $this->assertEquals('Tidak ada fasilitas tersedia', $facilitiesList);
    }

    /** @test */
    public function it_handles_empty_facilities()
    {
        $room = new MeetingRoom();
        $room->name = 'Test Room';
        $room->capacity = 10;
        $room->location = 'Test Location';
        $room->facilities = [];
        $room->is_active = true;
        $room->save();

        // Test that facilities_list accessor works with empty array
        $facilitiesList = $room->facilities_list;
        $this->assertEquals('Tidak ada fasilitas tersedia', $facilitiesList);
    }
}
