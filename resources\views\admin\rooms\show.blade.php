@extends('layouts.dashboard')

@section('title', 'Detail Ruang Rapat - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.rooms.index') }}">Manaj<PERSON>en Ruang</a></li>
                    <li class="breadcrumb-item active">{{ $room->name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Detail Ruang Rapat</h1>
        </div>
        <div>
            <a href="{{ route('admin.rooms.edit', $room->id) }}" class="btn btn-warning me-2">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <a href="{{ route('admin.rooms.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Room Information -->
        <div class="col-xl-8 col-lg-7">
            <!-- Basic Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Ruang</h6>
                    <div class="dropdown no-arrow">
                        <span class="badge {{ $room->is_active ? 'bg-success' : 'bg-danger' }}">
                            {{ $room->is_active ? 'Aktif' : 'Non-aktif' }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Room Image -->
                        @if($room->image)
                        <div class="col-md-4 mb-3">
                            <img src="{{ asset('storage/' . $room->image) }}" 
                                 alt="{{ $room->name }}" 
                                 class="img-fluid rounded shadow-sm">
                        </div>
                        @endif
                        
                        <div class="col-md-{{ $room->image ? '8' : '12' }}">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%" class="fw-bold">Nama Ruang:</td>
                                    <td>{{ $room->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Lokasi:</td>
                                    <td>{{ $room->location }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Kapasitas:</td>
                                    <td>{{ $room->capacity }} orang</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Tarif per Jam:</td>
                                    <td>
                                        @if($room->hourly_rate > 0)
                                            Rp {{ number_format($room->hourly_rate, 0, ',', '.') }}
                                        @else
                                            Gratis
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge {{ $room->is_active ? 'bg-success' : 'bg-danger' }}">
                                            {{ $room->is_active ? 'Aktif' : 'Non-aktif' }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($room->description)
                    <div class="mt-3">
                        <h6 class="fw-bold">Deskripsi:</h6>
                        <p class="text-muted">{{ $room->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Facilities Card -->
            @if($room->roomFacilities && $room->roomFacilities->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Fasilitas</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($room->roomFacilities as $facility)
                        <div class="col-md-4 col-sm-6 mb-2">
                            <span class="badge bg-info me-1">
                                @if($facility->icon)
                                    <i class="bi {{ $facility->icon }}"></i>
                                @else
                                    <i class="bi bi-check-circle"></i>
                                @endif
                                {{ $facility->name }}
                            </span>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Bookings Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">10 Pemesanan Terakhir</h6>
                </div>
                <div class="card-body">
                    @if($room->bookings && $room->bookings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Waktu</th>
                                    <th>Acara</th>
                                    <th>Pemesan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($room->bookings as $booking)
                                <tr>
                                    <td>{{ $booking->start_time->format('d/m/Y') }}</td>
                                    <td>{{ $booking->start_time->format('H:i') }} - {{ $booking->end_time->format('H:i') }}</td>
                                    <td>
                                        <strong>{{ $booking->title }}</strong>
                                        @if($booking->description)
                                        <br><small class="text-muted">{{ Str::limit($booking->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $booking->user->name }}</td>
                                    <td>
                                        @switch($booking->status)
                                            @case('approved')
                                                <span class="badge bg-success">Disetujui</span>
                                                @break
                                            @case('pending')
                                                <span class="badge bg-warning">Menunggu</span>
                                                @break
                                            @case('rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $booking->status }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-3">
                        <i class="bi bi-calendar-x text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">Belum ada pemesanan untuk ruang ini</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Statistics Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Statistics Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistik Pemesanan</h6>
                </div>
                <div class="card-body">
                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pemesanan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_bookings'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['this_month'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-month text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Disetujui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['approved'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    <div class="row no-gutters align-items-center mb-3">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Menunggu Persetujuan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>

                    @if($stats['revenue'] > 0)
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Pendapatan Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['revenue'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-dollar text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aksi Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.rooms.edit', $room->id) }}" class="btn btn-warning btn-sm">
                            <i class="bi bi-pencil"></i> Edit Ruang
                        </a>
                        
                        @if($room->is_active)
                        <form action="{{ route('admin.rooms.toggle-status', $room->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100" 
                                    onclick="return confirm('Yakin ingin menonaktifkan ruang ini?')">
                                <i class="bi bi-x-circle"></i> Nonaktifkan
                            </button>
                        </form>
                        @else
                        <form action="{{ route('admin.rooms.toggle-status', $room->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                <i class="bi bi-check-circle"></i> Aktifkan
                            </button>
                        </form>
                        @endif

                        <a href="{{ route('admin.bookings.index', ['room_id' => $room->id]) }}" class="btn btn-info btn-sm">
                            <i class="bi bi-list-ul"></i> Lihat Semua Pemesanan
                        </a>
                        
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteRoom({{ $room->id }})">
                            <i class="bi bi-trash"></i> Hapus Ruang
                        </button>
                    </div>
                </div>
            </div>

            <!-- Room Info Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Sistem</h6>
                </div>
                <div class="card-body">
                    <div class="small text-muted">
                        <div class="mb-1">
                            <strong>Dibuat:</strong> {{ $room->created_at->format('d/m/Y H:i') }}
                        </div>
                        <div>
                            <strong>Terakhir diperbarui:</strong> {{ $room->updated_at->format('d/m/Y H:i') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<script>
function deleteRoom(roomId) {
    Swal.fire({
        title: 'Hapus Ruang Rapat?',
        text: 'Tindakan ini tidak dapat dibatalkan. Semua data pemesanan terkait juga akan terhapus.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/rooms/${roomId}`;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';
            
            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            
            form.submit();
        }
    });
}
</script>
@endsection
