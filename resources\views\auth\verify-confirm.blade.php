<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Konfirmasi Verifikasi Email - Sistem Pemesanan Ruang Rapat</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .verification-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .verification-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .email-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        h1 {
            color: #333;
            margin-bottom: 15px;
            font-size: 28px;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.5;
        }

        .user-info {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .user-email {
            font-weight: 600;
            color: #333;
            word-break: break-all;
        }

        .verify-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
        }

        .verify-btn:active {
            transform: translateY(0);
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .security-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #856404;
        }

        @media (max-width: 480px) {
            .verification-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .verify-btn {
                padding: 12px 30px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="email-icon">
            📧
        </div>
        
        <h1>Konfirmasi Verifikasi Email</h1>
        <p class="subtitle">
            Anda akan memverifikasi alamat email berikut untuk mengaktifkan akun Anda
        </p>
        
        <div class="user-info">
            <div class="user-email">{{ $user->email }}</div>
        </div>
        
        <p style="margin-bottom: 30px; color: #666;">
            Setelah verifikasi berhasil, Anda dapat login ke sistem dan mulai melakukan pemesanan ruang rapat.
        </p>
        
        <button id="confirmVerifyBtn" class="verify-btn">
            ✓ Ya, Verifikasi Email Saya
        </button>
        
        <a href="{{ route('login') }}" class="back-btn">
            ← Kembali ke Login
        </a>
        
        <div class="loading" id="loadingDiv">
            <div class="spinner"></div>
            <p style="margin-top: 10px; color: #666;">Memproses verifikasi...</p>
        </div>
        
        <div class="security-info">
            <strong>Keamanan:</strong> Link verifikasi ini hanya dapat digunakan sekali dan akan expired dalam waktu tertentu.
        </div>
    </div>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const confirmBtn = document.getElementById('confirmVerifyBtn');
            const loadingDiv = document.getElementById('loadingDiv');
            const token = '{{ $token }}';
            
            confirmBtn.addEventListener('click', function() {
                // Show confirmation dialog first
                Swal.fire({
                    title: 'Konfirmasi Verifikasi',
                    text: 'Apakah Anda yakin ingin memverifikasi email ini?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#667eea',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Verifikasi!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        processVerification();
                    }
                });
            });
            
            function processVerification() {
                // Show loading
                confirmBtn.style.display = 'none';
                loadingDiv.style.display = 'block';
                
                // Send verification request
                fetch(`/email/verify/${token}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    loadingDiv.style.display = 'none';
                    
                    if (data.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonColor: '#667eea',
                            confirmButtonText: 'Lanjut ke Login'
                        }).then(() => {
                            window.location.href = '{{ route("login") }}';
                        });
                    } else {
                        Swal.fire({
                            title: 'Gagal!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonColor: '#667eea',
                            confirmButtonText: 'OK'
                        });
                        
                        // Show button again if not expired
                        if (!data.expired && !data.already_verified) {
                            confirmBtn.style.display = 'inline-block';
                        } else {
                            // Redirect to login for expired or already verified
                            setTimeout(() => {
                                window.location.href = '{{ route("login") }}';
                            }, 3000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    loadingDiv.style.display = 'none';
                    confirmBtn.style.display = 'inline-block';
                    
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat memproses verifikasi. Silakan coba lagi.',
                        icon: 'error',
                        confirmButtonColor: '#667eea',
                        confirmButtonText: 'OK'
                    });
                });
            }
            
            // Auto-focus on confirm button
            setTimeout(() => {
                confirmBtn.focus();
            }, 500);
        });
    </script>
</body>
</html>
