<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('facilities', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('icon')->nullable(); // opsional, untuk icon fasilitas
            $table->timestamps();
        });

        Schema::create('facility_meeting_room', function (Blueprint $table) {
            $table->id();
            $table->foreignId('facility_id')->constrained()->onDelete('cascade');
            $table->foreignId('meeting_room_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            $table->unique(['facility_id', 'meeting_room_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('facility_meeting_room');
        Schema::dropIfExists('facilities');
    }
};
