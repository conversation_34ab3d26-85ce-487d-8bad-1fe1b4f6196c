<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action'); // login, logout, failed_login, register, password_reset, etc.
            $table->string('ip_address');
            $table->text('user_agent');
            $table->enum('severity', ['info', 'low', 'medium', 'high', 'critical'])->default('medium');
            $table->json('details')->nullable(); // Additional details as JSON
            $table->string('session_id')->nullable();
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['action', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('severity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_logs');
    }
};
