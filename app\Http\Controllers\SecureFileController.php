<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use App\Models\SecurityLog;

class SecureFileController extends Controller
{
    public function show(Request $request, string $encryptedPath)
    {
        try {
            // Decrypt the file path
            $path = decrypt($encryptedPath);
            
            // Security checks
            $this->validateFileAccess($request, $path);
            
            // Check if file exists
            if (!Storage::disk('public')->exists($path)) {
                abort(404, 'File tidak ditemukan.');
            }

            // Get file info
            $fullPath = storage_path("app/public/{$path}");
            $mimeType = mime_content_type($fullPath);
            $filename = basename($path);

            // Log file access
            SecurityLog::logAction(
                'secure_file_accessed',
                Auth::id(),
                'low',
                [
                    'file_path' => $path,
                    'file_name' => $filename,
                    'mime_type' => $mimeType,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]
            );

            // Return file response
            return response()->file($fullPath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $filename . '"',
                'X-Content-Type-Options' => 'nosniff',
                'X-Frame-Options' => 'DENY'
            ]);

        } catch (\Exception $e) {
            // Log suspicious access attempt
            SecurityLog::logAction(
                'suspicious_file_access',
                Auth::id(),
                'high',
                [
                    'encrypted_path' => $encryptedPath,
                    'error' => $e->getMessage(),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]
            );

            abort(403, 'Akses file tidak diperbolehkan.');
        }
    }

    protected function validateFileAccess(Request $request, string $path): void
    {
        // Check authentication
        if (!Auth::check()) {
            throw new \Exception('User not authenticated');
        }

        // Validate path (prevent path traversal)
        if (str_contains($path, '..') || str_contains($path, '\\')) {
            throw new \Exception('Invalid file path');
        }

        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'txt', 'csv', 'xls', 'xlsx'];
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            throw new \Exception('File type not allowed');
        }

        // Rate limiting for file access
        $key = 'file_access:' . Auth::id() . ':' . $request->ip();
        if (cache()->has($key) && cache()->get($key) > 50) { // Max 50 files per minute
            throw new \Exception('Rate limit exceeded');
        }
        
        cache()->put($key, cache()->get($key, 0) + 1, 60);
    }

    public function download(Request $request, string $encryptedPath)
    {
        try {
            $path = decrypt($encryptedPath);
            $this->validateFileAccess($request, $path);
            
            if (!Storage::disk('public')->exists($path)) {
                abort(404, 'File tidak ditemukan.');
            }

            $fullPath = storage_path("app/public/{$path}");
            $filename = basename($path);

            // Log file download
            SecurityLog::logAction(
                'secure_file_downloaded',
                Auth::id(),
                'low',
                [
                    'file_path' => $path,
                    'file_name' => $filename,
                    'ip' => $request->ip()
                ]
            );

            return response()->download($fullPath, $filename);

        } catch (\Exception $e) {
            SecurityLog::logAction(
                'suspicious_file_download',
                Auth::id(),
                'high',
                [
                    'encrypted_path' => $encryptedPath,
                    'error' => $e->getMessage(),
                    'ip' => $request->ip()
                ]
            );

            abort(403, 'Download file tidak diperbolehkan.');
        }
    }
}
