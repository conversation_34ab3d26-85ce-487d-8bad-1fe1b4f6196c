<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\SecurityLog;
use App\Events\BookingStatusUpdated;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookingManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->middleware('security');
    }

    public function index(Request $request)
    {
        $query = Booking::with(['user', 'meetingRoom']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('start_time', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('end_time', '<=', $request->end_date);
        }

        // Search by title or user name
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get statistics
        $stats = [
            'total' => Booking::count(),
            'pending' => Booking::where('status', 'pending')->count(),
            'approved' => Booking::where('status', 'approved')->count(),
            'rejected' => Booking::where('status', 'rejected')->count(),
        ];

        // Get rooms for filter
        $rooms = \App\Models\MeetingRoom::all();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'view_booking_management',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return view('admin.bookings.index', compact('bookings', 'stats', 'rooms'));
    }

    public function show(Booking $booking)
    {
        $booking->load(['user', 'meetingRoom', 'approvedBy']);
        return view('admin.bookings.show', compact('booking'));
    }

    public function edit(Booking $booking)
    {
        $rooms = \App\Models\MeetingRoom::all();
        return view('admin.bookings.edit', compact('booking', 'rooms'));
    }

    public function update(Request $request, Booking $booking)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'meeting_room_id' => 'required|exists:meeting_rooms,id',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'attendees_count' => 'required|integer|min:1',
            'status' => 'required|in:pending,approved,rejected,cancelled,completed',
            'required_facilities' => 'nullable|array',
            'rejection_reason' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        // Check room availability if changing room or time
        if ($booking->meeting_room_id != $request->meeting_room_id || 
            $booking->start_time->format('Y-m-d H:i') != date('Y-m-d H:i', strtotime($request->start_time)) ||
            $booking->end_time->format('Y-m-d H:i') != date('Y-m-d H:i', strtotime($request->end_time))) {
            
            $conflictBooking = Booking::where('meeting_room_id', $request->meeting_room_id)
                ->where('id', '!=', $booking->id)
                ->where('status', '!=', 'rejected')
                ->where('status', '!=', 'cancelled')
                ->where(function($query) use ($request) {
                    $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                          ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                          ->orWhere(function($q) use ($request) {
                              $q->where('start_time', '<=', $request->start_time)
                                ->where('end_time', '>=', $request->end_time);
                          });
                })
                ->first();

            if ($conflictBooking) {
                return back()->withErrors(['error' => 'Ruang sudah dipesan pada waktu tersebut.']);
            }
        }

        $oldStatus = $booking->status;
        
        $booking->update([
            'title' => $request->title,
            'description' => $request->description,
            'meeting_room_id' => $request->meeting_room_id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'attendees_count' => $request->attendees_count,
            'status' => $request->status,
            'required_facilities' => $request->required_facilities ? json_encode($request->required_facilities) : null,
            'rejection_reason' => $request->status == 'rejected' ? $request->rejection_reason : null,
            'notes' => $request->notes,
            'approved_at' => $request->status == 'approved' && $oldStatus != 'approved' ? now() : $booking->approved_at,
            'approved_by' => $request->status == 'approved' && $oldStatus != 'approved' ? Auth::id() : $booking->approved_by,
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'update_booking',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => json_encode([
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $request->status,
                'updated_fields' => array_keys($request->only([
                    'title', 'description', 'meeting_room_id', 'start_time', 
                    'end_time', 'attendees_count', 'status', 'notes'
                ]))
            ])
        ]);

        return redirect()->route('admin.bookings.show', $booking)
                        ->with('success', 'Pemesanan berhasil diperbarui.');
    }

    public function approve(Booking $booking)
    {
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Hanya pemesanan dengan status pending yang dapat disetujui.'
            ], 400);
        }

        $oldStatus = $booking->status;
        
        $booking->update([
            'status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now()
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'approve_booking',
            'details' => "Approved booking: {$booking->title} (ID: {$booking->id})",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        // Create notification for booking status update
        NotificationService::notifyBookingStatusUpdate($booking, $oldStatus);

        // Broadcast real-time update
        event(new BookingStatusUpdated($booking, $oldStatus));

        return response()->json([
            'success' => true,
            'message' => 'Pemesanan berhasil disetujui.'
        ]);
    }

    public function reject(Request $request, Booking $booking)
    {
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Hanya pemesanan dengan status pending yang dapat ditolak.'
            ], 400);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        $oldStatus = $booking->status;

        $booking->update([
            'status' => 'rejected',
            'rejected_by' => Auth::id(),
            'rejected_at' => now(),
            'rejection_reason' => $request->rejection_reason
        ]);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'reject_booking',
            'details' => "Rejected booking: {$booking->title} (ID: {$booking->id}). Reason: {$request->rejection_reason}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        // Create notification for booking status update
        NotificationService::notifyBookingStatusUpdate($booking, $oldStatus);

        // Broadcast real-time update
        event(new BookingStatusUpdated($booking, $oldStatus));

        return response()->json([
            'success' => true,
            'message' => 'Pemesanan berhasil ditolak.'
        ]);
    }

    public function destroy(Booking $booking)
    {
        $title = $booking->title;
        $booking->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_delete_booking',
            'details' => "Admin deleted booking: {$title}",
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return redirect()->route('admin.bookings.index')->with('success', 'Pemesanan berhasil dihapus.');
    }

    /**
     * Change booking status
     */
    public function changeStatus(Request $request, Booking $booking)
    {
        // Debug logging
        \Log::info('changeStatus called', [
            'booking_id' => $booking->id,
            'request_data' => $request->all()
        ]);

        try {
            $request->validate([
                'status' => 'required|in:pending,approved,rejected,cancelled,completed',
                'rejection_reason' => 'required_if:status,rejected|nullable|string|max:500'
            ]);

            $oldStatus = $booking->status;
            $newStatus = $request->status;

            // Update booking status
            $updateData = ['status' => $newStatus];

            if ($newStatus === 'approved') {
                $updateData['approved_at'] = now();
                $updateData['approved_by'] = Auth::id();
                $updateData['rejection_reason'] = null;
            } elseif ($newStatus === 'rejected') {
                $updateData['approved_at'] = null;
                $updateData['approved_by'] = null;
                $updateData['rejection_reason'] = $request->rejection_reason;
            } elseif ($newStatus === 'pending') {
                $updateData['approved_at'] = null;
                $updateData['approved_by'] = null;
                $updateData['rejection_reason'] = null;
            }

            $booking->update($updateData);

            // Reload booking with relationships
            $booking->load(['user', 'meetingRoom', 'approvedBy']);

            // Log security activity
            try {
                SecurityLog::create([
                    'user_id' => Auth::id(),
                    'action' => 'admin_change_booking_status',
                    'details' => "Admin changed booking status from {$oldStatus} to {$newStatus}. Booking: {$booking->title}",
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent()
                ]);
            } catch (\Exception $e) {
                \Log::warning('Failed to create security log', ['error' => $e->getMessage()]);
            }

            // Create notification for booking status update
            try {
                NotificationService::notifyBookingStatusUpdate($booking, $oldStatus);
            } catch (\Exception $e) {
                \Log::warning('Failed to create notification', ['error' => $e->getMessage()]);
            }

            // Dispatch event for real-time updates
            try {
                event(new BookingStatusUpdated($booking, $oldStatus));
            } catch (\Exception $e) {
                \Log::warning('Failed to dispatch BookingStatusUpdated event', ['error' => $e->getMessage()]);
            }

            \Log::info('changeStatus successful', [
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            return response()->json([
                'success' => true,
                'message' => "Status pemesanan berhasil diubah menjadi " . $this->getStatusLabel($newStatus),
                'booking' => $booking->toArray(),
                // Tambahkan data stats terbaru
                'stats' => [
                    'pending' => \App\Models\Booking::where('status', 'pending')->count(),
                    'approved' => \App\Models\Booking::where('status', 'approved')->count(),
                    'rejected' => \App\Models\Booking::where('status', 'rejected')->count(),
                    'total' => \App\Models\Booking::count(),
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in changeStatus', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get status label in Indonesian
     */
    private function getStatusLabel($status)
    {
        $labels = [
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            'cancelled' => 'Dibatalkan',
            'completed' => 'Selesai'
        ];

        return $labels[$status] ?? $status;
    }
}
