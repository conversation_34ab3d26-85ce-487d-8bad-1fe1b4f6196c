@extends('layouts.dashboard')

@section('title', 'Detail Pemesanan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.index') }}"><PERSON><PERSON><PERSON><PERSON></a></li>
                    <li class="breadcrumb-item active">Detail Pemesanan</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Detail Pemesanan</h1>
        </div>
        <div>
            @if($booking->status === 'pending')
                <a href="{{ route('bookings.edit', $booking->id) }}" class="btn btn-warning">
                    <i class="bi bi-pencil me-1"></i> Edit
                </a>
            @endif
            <a href="{{ route('bookings.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Booking Info -->
        <div class="col-xl-8 col-lg-7">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Pemesanan</h6>
                    <span class="badge fs-6
                        @if($booking->status === 'approved') bg-success
                        @elseif($booking->status === 'pending') bg-warning
                        @elseif($booking->status === 'rejected') bg-danger
                        @elseif($booking->status === 'cancelled') bg-secondary
                        @else bg-info
                        @endif">
                        {{ ucfirst($booking->status === 'approved' ? 'Disetujui' : 
                           ($booking->status === 'pending' ? 'Menunggu Persetujuan' : 
                           ($booking->status === 'rejected' ? 'Ditolak' : 
                           ($booking->status === 'cancelled' ? 'Dibatalkan' : $booking->status)))) }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Judul Acara</label>
                                <p class="form-control-static h5">{{ $booking->title }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Kode Booking</label>
                                <p class="form-control-static font-monospace">{{ $booking->booking_code ?? $booking->id }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tanggal</label>
                                <p class="form-control-static">{{ \Carbon\Carbon::parse($booking->date)->format('l, d F Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Waktu</label>
                                <p class="form-control-static">{{ $booking->start_time }} - {{ $booking->end_time }} WIB</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Jumlah Peserta</label>
                                <p class="form-control-static">{{ $booking->attendees }} orang</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tanggal Pemesanan</label>
                                <p class="form-control-static">{{ $booking->created_at->format('d M Y, H:i') }}</p>
                            </div>
                        </div>
                        @if($booking->description)
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Deskripsi Acara</label>
                                <div class="alert alert-light">{{ $booking->description }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Room Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Ruang Rapat</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            @if($booking->meetingRoom->image)
                                <img src="{{ Storage::url($booking->meetingRoom->image) }}" 
                                     alt="{{ $booking->meetingRoom->name }}" 
                                     class="img-fluid rounded mb-3">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                                     style="height: 200px;">
                                    <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-8">
                            <h5>{{ $booking->meetingRoom->name }}</h5>
                            <p class="text-muted">{{ $booking->meetingRoom->description }}</p>
                            
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Kapasitas:</strong> {{ $booking->meetingRoom->capacity }} orang
                                </div>
                                <div class="col-sm-6">
                                    <strong>Lokasi:</strong> {{ $booking->meetingRoom->location }}
                                </div>
                            </div>
                            
                            @if($booking->meetingRoom->facilities)
                                <div class="mb-3">
                                    <strong>Fasilitas:</strong>
                                    <div class="mt-2">
                                        @php
                                            $facilities = is_string($booking->meetingRoom->facilities) 
                                                ? json_decode($booking->meetingRoom->facilities, true) 
                                                : $booking->meetingRoom->facilities;
                                        @endphp
                                        @if(is_array($facilities))
                                            @foreach($facilities as $facility)
                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $facility }}</span>
                                            @endforeach
                                        @else
                                            <span class="badge bg-light text-dark">{{ $booking->meetingRoom->facilities }}</span>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status History -->
            @if($booking->status_history || $booking->admin_notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Riwayat Status</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <!-- Created -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Pemesanan Dibuat</h6>
                                <p class="text-muted mb-0">{{ $booking->created_at->format('d M Y, H:i') }}</p>
                            </div>
                        </div>
                        
                        @if($booking->status === 'approved')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>Pemesanan Disetujui</h6>
                                <p class="text-muted mb-0">{{ $booking->updated_at->format('d M Y, H:i') }}</p>
                                @if($booking->approved_by)
                                    <small class="text-muted">Oleh: {{ $booking->approvedBy->name ?? 'Admin' }}</small>
                                @endif
                            </div>
                        </div>
                        @elseif($booking->status === 'rejected')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6>Pemesanan Ditolak</h6>
                                <p class="text-muted mb-0">{{ $booking->updated_at->format('d M Y, H:i') }}</p>
                                @if($booking->rejected_by)
                                    <small class="text-muted">Oleh: {{ $booking->rejectedBy->name ?? 'Admin' }}</small>
                                @endif
                            </div>
                        </div>
                        @elseif($booking->status === 'cancelled')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6>Pemesanan Dibatalkan</h6>
                                <p class="text-muted mb-0">{{ $booking->updated_at->format('d M Y, H:i') }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    @if($booking->admin_notes)
                        <div class="mt-3">
                            <h6>Catatan Admin:</h6>
                            <div class="alert alert-info">{{ $booking->admin_notes }}</div>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Actions -->
        <div class="col-xl-4 col-lg-5">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Aksi</h6>
                </div>
                <div class="card-body">
                    @if($booking->status === 'pending')
                        <div class="d-grid gap-2">
                            <a href="{{ route('bookings.edit', $booking->id) }}" class="btn btn-warning">
                                <i class="bi bi-pencil me-1"></i> Edit Pemesanan
                            </a>
                            <button class="btn btn-danger" onclick="cancelBooking()">
                                <i class="bi bi-x-circle me-1"></i> Batalkan Pemesanan
                            </button>
                        </div>
                    @elseif($booking->status === 'approved')
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="downloadConfirmation()">
                                <i class="bi bi-download me-1"></i> Download Konfirmasi
                            </button>
                            @if(\Carbon\Carbon::parse($booking->date)->isFuture())
                                <button class="btn btn-outline-warning" onclick="requestChange()">
                                    <i class="bi bi-arrow-repeat me-1"></i> Minta Perubahan
                                </button>
                            @endif
                        </div>
                    @endif
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="shareBooking()">
                            <i class="bi bi-share me-1"></i> Bagikan
                        </button>
                        <button class="btn btn-outline-info" onclick="printBooking()">
                            <i class="bi bi-printer me-1"></i> Cetak
                        </button>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Kontak</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Pemesan</label>
                        <div class="d-flex align-items-center">
                            @if($booking->user->avatar)
                                <img src="{{ Storage::url($booking->user->avatar) }}" alt="Avatar" 
                                     class="rounded-circle me-2" width="40" height="40">
                            @else
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 40px; height: 40px; color: white;">
                                    {{ strtoupper(substr($booking->user->name, 0, 1)) }}
                                </div>
                            @endif
                            <div>
                                <div class="fw-bold">{{ $booking->user->name }}</div>
                                <small class="text-muted">{{ $booking->user->email }}</small>
                            </div>
                        </div>
                    </div>
                    
                    @if($booking->user->phone)
                        <div class="mb-3">
                            <label class="form-label text-muted">Telepon</label>
                            <p class="form-control-static">{{ $booking->user->phone }}</p>
                        </div>
                    @endif
                    
                    @if($booking->user->department)
                        <div class="mb-3">
                            <label class="form-label text-muted">Departemen</label>
                            <p class="form-control-static">{{ $booking->user->department }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Room Schedule -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Jadwal Hari Ini</h6>
                </div>
                <div class="card-body">
                    @if(isset($todaySchedule) && count($todaySchedule) > 0)
                        @foreach($todaySchedule as $schedule)
                            <div class="mb-2 p-2 rounded {{ $schedule->id === $booking->id ? 'bg-primary text-white' : 'bg-light' }}">
                                <div class="small fw-bold">{{ $schedule->start_time }} - {{ $schedule->end_time }}</div>
                                <div class="small">{{ Str::limit($schedule->title, 30) }}</div>
                                @if($schedule->id !== $booking->id)
                                    <div class="small text-muted">{{ $schedule->user->name }}</div>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted small">Tidak ada jadwal lain hari ini</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: 10px;
    top: 30px;
    bottom: -20px;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item:last-child:before {
    display: none;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    margin-left: 10px;
}
</style>
@endpush

@push('scripts')
<script>
function cancelBooking() {
    Swal.fire({
        title: 'Batalkan Pemesanan?',
        text: 'Pemesanan yang dibatalkan tidak dapat dikembalikan.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Batalkan!',
        cancelButtonText: 'Kembali'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/bookings/{{ $booking->id }}/cancel`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Pemesanan telah dibatalkan.', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function downloadConfirmation() {
    window.open(`/bookings/{{ $booking->id }}/confirmation`, '_blank');
}

function requestChange() {
    Swal.fire({
        title: 'Minta Perubahan',
        input: 'textarea',
        inputLabel: 'Alasan perubahan',
        inputPlaceholder: 'Jelaskan perubahan apa yang Anda inginkan...',
        showCancelButton: true,
        confirmButtonText: 'Kirim Permintaan',
        cancelButtonText: 'Batal',
        inputValidator: (value) => {
            if (!value) {
                return 'Alasan perubahan harus diisi';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/bookings/{{ $booking->id }}/request-change`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    reason: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('Berhasil!', 'Permintaan perubahan telah dikirim ke admin.', 'success');
                } else {
                    Swal.fire('Error!', data.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(error => {
                Swal.fire('Error!', 'Terjadi kesalahan sistem.', 'error');
            });
        }
    });
}

function shareBooking() {
    const url = window.location.href;
    const text = `Pemesanan Ruang Rapat: {{ $booking->title }} - {{ $booking->meetingRoom->name }}`;
    
    if (navigator.share) {
        navigator.share({
            title: text,
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            Swal.fire('Berhasil!', 'Link pemesanan telah disalin ke clipboard.', 'success');
        });
    }
}

function printBooking() {
    window.print();
}
</script>
@endpush
@endsection
