<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Facility;

class FacilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $facilities = [
            ['name' => 'Proyektor', 'icon' => 'bi-camera-video'],
            ['name' => 'Papan Tulis', 'icon' => 'bi-easel'],
            ['name' => 'WiFi', 'icon' => 'bi-wifi'],
            ['name' => 'AC', 'icon' => 'bi-thermometer-half'],
            ['name' => 'Mikrofon', 'icon' => 'bi-mic'],
            ['name' => 'Speaker', 'icon' => 'bi-volume-up'],
            ['name' => 'TV/Monitor', 'icon' => 'bi-tv'],
            ['name' => 'Telepon', 'icon' => 'bi-telephone'],
            ['name' => 'Smart TV', 'icon' => 'bi-tv'],
            ['name' => 'Video Conference', 'icon' => 'bi-camera-video'],
            ['name' => 'Audio System', 'icon' => 'bi-volume-up'],
            ['name' => 'Coffee Machine', 'icon' => 'bi-cup-hot'],
            ['name' => 'Flipchart', 'icon' => 'bi-easel'],
            ['name' => 'Printer', 'icon' => 'bi-printer'],
            ['name' => 'Scanner', 'icon' => 'bi-upc-scan'],
        ];

        foreach ($facilities as $facility) {
            Facility::firstOrCreate(
                ['name' => $facility['name']],
                ['icon' => $facility['icon']]
            );
        }

        $this->command->info('Created ' . count($facilities) . ' facilities');
    }
}
