@extends('layouts.dashboard')
@section('title', 'Kelo<PERSON> Fasilitas')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><PERSON><PERSON><PERSON>lit<PERSON></h1>
        <a href="{{ route('admin.facilities.create') }}" class="btn btn-primary"><i class="bi bi-plus"></i> Tambah Fasilitas</a>
    </div>
    <div class="card shadow">
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
            @endif
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nama Fasilitas</th>
                            <th>Icon</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($facilities as $facility)
                        <tr>
                            <td>{{ $facility->name }}</td>
                            <td>@if($facility->icon)<i class="bi {{ $facility->icon }}"></i>@endif</td>
                            <td>
                                <a href="{{ route('admin.facilities.edit', $facility) }}" class="btn btn-sm btn-warning"><i class="bi bi-pencil"></i></a>
                                <form action="{{ route('admin.facilities.destroy', $facility) }}" method="POST" class="d-inline" onsubmit="return confirm('Hapus fasilitas ini?')">
                                    @csrf @method('DELETE')
                                    <button class="btn btn-sm btn-danger"><i class="bi bi-trash"></i></button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr><td colspan="3" class="text-center">Belum ada fasilitas</td></tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            {{ $facilities->links() }}
        </div>
    </div>
</div>
@endsection
