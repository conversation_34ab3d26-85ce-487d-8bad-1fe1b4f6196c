@extends('layouts.dashboard')

@section('title', 'Tambah Ruang Rapat - Admin')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">Tambah Ruang Rapat</h1>
                <p class="text-muted">Buat ruang rapat baru</p>
            </div>
            <div>
                        <a href="{{ route('admin.rooms.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-door-closed"></i> Informasi Ruangan
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.rooms.store') }}" method="POST" enctype="multipart/form-data" id="roomForm">
                                @csrf
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Nama Ruangan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" 
                                                   placeholder="Masukkan nama ruangan" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Lokasi <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                                   id="location" name="location" value="{{ old('location') }}" 
                                                   placeholder="Lantai 1, Gedung A" required>
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                                       id="capacity" name="capacity" value="{{ old('capacity') }}" 
                                                       min="1" max="1000" placeholder="0" required>
                                                <span class="input-group-text">orang</span>
                                            </div>
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hourly_rate" class="form-label">Tarif per Jam</label>
                                            <div class="input-group">
                                                <span class="input-group-text">Rp</span>
                                                <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror" 
                                                       id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate') }}" 
                                                       min="0" step="1000" placeholder="0">
                                            </div>
                                            <small class="form-text text-muted">Kosongkan jika gratis</small>
                                            @error('hourly_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Deskripsi</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3" 
                                              placeholder="Deskripsi ruangan (opsional)">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="image" class="form-label">Foto Ruangan</label>
                                    <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                           id="image" name="image" accept="image/jpeg,image/png,image/jpg">
                                    <small class="form-text text-muted">Format: JPEG, PNG, JPG. Maksimal 2MB</small>
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    
                                    <!-- Image Preview -->
                                    <div id="imagePreview" class="mt-2" style="display: none;">
                                        <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Fasilitas</label>
                                    <div class="row">
                                        @forelse($facilities as $facility)
                                            <div class="col-md-4">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="{{ $facility->id }}" id="facility{{ $facility->id }}">
                                                    <label class="form-check-label" for="facility{{ $facility->id }}">
                                                        @if($facility->icon)<i class="bi {{ $facility->icon }}"></i> @endif
                                                        {{ $facility->name }}
                                                    </label>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="col-12 text-muted">Belum ada fasilitas. <a href='{{ route('admin.facilities.create') }}'>Tambah Fasilitas</a></div>
                                        @endforelse
                                    </div>
                                    <a href="{{ route('admin.facilities.index') }}" class="btn btn-link p-0 mt-2">+ Kelola Fasilitas</a>
                                    @error('facilities')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.rooms.index') }}" class="btn btn-secondary">
                                        <i class="bi bi-x-lg"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg"></i> Simpan Ruangan
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle"></i> Panduan
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-lightbulb"></i> Tips:</h6>
                                <ul class="mb-0 small">
                                    <li>Gunakan nama ruangan yang mudah diingat</li>
                                    <li>Cantumkan lokasi yang spesifik (lantai, gedung)</li>
                                    <li>Pastikan kapasitas sesuai dengan ukuran ruangan</li>
                                    <li>Tambahkan foto untuk memudahkan identifikasi</li>
                                    <li>Pilih fasilitas yang tersedia di ruangan</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle"></i> Perhatian:</h6>
                                <ul class="mb-0 small">
                                    <li>Nama ruangan tidak boleh duplikat</li>
                                    <li>Ukuran foto maksimal 2MB</li>
                                    <li>Tarif dalam Rupiah per jam</li>
                                    <li>Ruangan akan aktif secara otomatis</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// Form validation
document.getElementById('roomForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const location = document.getElementById('location').value.trim();
    const capacity = document.getElementById('capacity').value;
    
    if (!name || !location || !capacity) {
        e.preventDefault();
        Toast.fire({
            icon: 'error',
            title: 'Mohon lengkapi semua field yang wajib diisi'
        });
        return false;
    }
    
    if (capacity < 1 || capacity > 1000) {
        e.preventDefault();
        Toast.fire({
            icon: 'error',
            title: 'Kapasitas harus antara 1-1000 orang'
        });
        return false;
    }
});

// Auto format number input
document.getElementById('hourly_rate').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value) {
        e.target.value = parseInt(value);
    }
});
</script>
@endpush
