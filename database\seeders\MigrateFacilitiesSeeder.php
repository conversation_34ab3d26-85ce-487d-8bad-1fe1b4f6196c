<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MeetingRoom;
use App\Models\Facility;

class MigrateFacilitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Mapping dari nama fasilitas lama ke nama fasilitas baru
        $facilityMapping = [
            'Proyektor 4K' => 'Proyektor',
            'Smart TV 75 inch' => 'Smart TV',
            'Smart TV 42 inch' => 'Smart TV',
            'TV LED 32 inch' => 'TV/Monitor',
            'Smart TV 85 inch' => 'Smart TV',
            'AC Central' => 'AC',
            'AC Premium' => 'AC',
            'AC VIP' => 'AC',
            'AC' => 'AC',
            'Whiteboard Digital' => 'Papan Tulis',
            'Whiteboard' => 'Papan Tulis',
            'Audio System' => 'Audio System',
            'Audio Premium' => 'Audio System',
            'Audio Surround' => 'Audio System',
            'WiFi Premium' => 'WiFi',
            'WiFi Fiber' => 'WiFi',
            'WiFi Outdoor' => 'WiFi',
            'WiFi' => 'WiFi',
            'Coffee Machine' => 'Coffee Machine',
            'Video Conference' => 'Video Conference',
            'Video Conference 4K' => 'Video Conference',
            'Microphone' => 'Mikrofon',
            'Sound System' => 'Speaker',
            'Sound Portable' => 'Speaker',
            'Proyektor HD' => 'Proyektor',
            'Proyektor 4K Laser' => 'Proyektor',
            'Flipchart Portable' => 'Flipchart',
        ];

        $rooms = MeetingRoom::all();
        
        foreach ($rooms as $room) {
            if ($room->facilities && is_array($room->facilities)) {
                $facilityIds = [];
                
                foreach ($room->facilities as $facilityName) {
                    // Cari mapping atau gunakan nama asli
                    $mappedName = $facilityMapping[$facilityName] ?? $facilityName;
                    
                    // Cari fasilitas berdasarkan nama
                    $facility = Facility::where('name', $mappedName)->first();
                    
                    if ($facility) {
                        $facilityIds[] = $facility->id;
                    } else {
                        // Jika tidak ditemukan, buat fasilitas baru
                        $newFacility = Facility::create([
                            'name' => $mappedName,
                            'icon' => 'bi-check-circle'
                        ]);
                        $facilityIds[] = $newFacility->id;
                        $this->command->info("Created new facility: {$mappedName}");
                    }
                }
                
                // Sync fasilitas ke ruangan
                if (!empty($facilityIds)) {
                    $room->facilities()->sync($facilityIds);
                    $this->command->info("Synced facilities for room: {$room->name}");
                }
            }
        }
        
        $this->command->info('Migration of facilities completed');
    }
}
