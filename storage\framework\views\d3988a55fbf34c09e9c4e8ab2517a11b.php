

<?php $__env->startSection('title', 'Edit Ruang Rapat - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">Edit Ruang Rapat</h1>
                <p class="text-muted">Edit informasi ruang rapat: <?php echo e($room->name); ?></p>
            </div>
            <div>
                <a href="<?php echo e(route('admin.rooms.index')); ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-door-closed"></i> Informasi <PERSON>uangan
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.rooms.update', $room->id)); ?>" method="POST" enctype="multipart/form-data" id="roomForm">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Ruangan <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="name" name="name" value="<?php echo e(old('name', $room->name)); ?>" 
                                           placeholder="Masukkan nama ruangan" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Lokasi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="location" name="location" value="<?php echo e(old('location', $room->location)); ?>" 
                                           placeholder="Masukkan lokasi ruangan" required>
                                    <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="capacity" name="capacity" value="<?php echo e(old('capacity', $room->capacity)); ?>" 
                                           min="1" max="1000" placeholder="Jumlah orang" required>
                                    <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hourly_rate" class="form-label">Tarif per Jam</label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp</span>
                                        <input type="number" class="form-control <?php $__errorArgs = ['hourly_rate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="hourly_rate" name="hourly_rate" value="<?php echo e(old('hourly_rate', $room->hourly_rate)); ?>" 
                                               min="0" step="1000" placeholder="0">
                                    </div>
                                    <?php $__errorArgs = ['hourly_rate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <small class="form-text text-muted">Kosongkan jika gratis</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi ruangan (opsional)"><?php echo e(old('description', $room->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fasilitas</label>
                            <div class="row">
                                <?php $__empty_1 = true; $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="<?php echo e($facility->id); ?>" id="facility<?php echo e($facility->id); ?>" <?php if(in_array($facility->id, $selectedFacilities)): ?> checked <?php endif; ?>>
                                            <label class="form-check-label" for="facility<?php echo e($facility->id); ?>">
                                                <?php if($facility->icon): ?><i class="bi <?php echo e($facility->icon); ?>"></i> <?php endif; ?>
                                                <?php echo e($facility->name); ?>

                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <div class="col-12 text-muted">Belum ada fasilitas. <a href='<?php echo e(route('admin.facilities.create')); ?>'>Tambah Fasilitas</a></div>
                                <?php endif; ?>
                            </div>
                            <a href="<?php echo e(route('admin.facilities.index')); ?>" class="btn btn-link p-0 mt-2">+ Kelola Fasilitas</a>
                            <?php $__errorArgs = ['facilities'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger small"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_active" 
                                       id="is_active" value="1" <?php echo e(old('is_active', $room->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    Aktifkan ruangan
                                </label>
                            </div>
                            <small class="form-text text-muted">Ruangan yang tidak aktif tidak akan muncul dalam daftar pemesanan</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.rooms.index')); ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Ruangan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-eye"></i> Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="room-preview">
                        <h6 id="preview-name"><?php echo e($room->name); ?></h6>
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt"></i> <span id="preview-location"><?php echo e($room->location); ?></span>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-people"></i> <span id="preview-capacity"><?php echo e($room->capacity); ?></span> orang
                        </p>
                        <?php if($room->hourly_rate): ?>
                        <p class="text-muted mb-2">
                            <i class="bi bi-currency-dollar"></i> Rp <span id="preview-rate"><?php echo e(number_format($room->hourly_rate, 0, ',', '.')); ?></span>/jam
                        </p>
                        <?php endif; ?>
                        <div class="mt-3">
                            <h6>Fasilitas:</h6>
                            <div id="preview-facilities">
                                <?php
                                    $facilitiesArray = $room->facilities;
                                    if (is_string($facilitiesArray)) {
                                        $facilitiesArray = json_decode($facilitiesArray, true) ?? [];
                                    }
                                    if (!is_array($facilitiesArray)) {
                                        $facilitiesArray = [];
                                    }
                                ?>
                                <?php if($facilitiesArray && count($facilitiesArray) > 0): ?>
                                    <?php $__currentLoopData = $facilitiesArray; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-info me-1"><?php echo e(ucfirst(str_replace('_', ' ', $facility))); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <span class="text-muted">Tidak ada fasilitas</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if($room->description): ?>
                        <div class="mt-3">
                            <h6>Deskripsi:</h6>
                            <p class="text-muted" id="preview-description"><?php echo e($room->description); ?></p>
                        </div>
                        <?php endif; ?>
                        <div class="mt-3">
                            <span class="badge <?php echo e($room->is_active ? 'bg-success' : 'bg-danger'); ?>" id="preview-status">
                                <?php echo e($room->is_active ? 'Aktif' : 'Tidak Aktif'); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i> Statistik
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item">
                        <span class="stat-label">Total Pemesanan:</span>
                        <span class="stat-value"><?php echo e($room->bookings()->count()); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Bulan Ini:</span>
                        <span class="stat-value"><?php echo e($room->bookings()->whereMonth('start_time', now()->month)->count()); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Pending:</span>
                        <span class="stat-value"><?php echo e($room->bookings()->where('status', 'pending')->count()); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.room-preview {
    font-size: 14px;
}

.stat-item {
    display: flex;
    justify-content: between;
    margin-bottom: 8px;
}

.stat-label {
    font-weight: 500;
    flex: 1;
}

.stat-value {
    font-weight: 600;
    color: var(--primary-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

#preview-facilities .badge {
    margin-bottom: 3px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const nameInput = document.getElementById('name');
    const locationInput = document.getElementById('location');
    const capacityInput = document.getElementById('capacity');
    const hourlyRateInput = document.getElementById('hourly_rate');
    const descriptionInput = document.getElementById('description');
    const activeCheckbox = document.getElementById('is_active');
    const facilityCheckboxes = document.querySelectorAll('input[name="facilities[]"]');
    
    // Update preview on input changes
    nameInput.addEventListener('input', () => {
        document.getElementById('preview-name').textContent = nameInput.value || 'Nama Ruangan';
    });
    
    locationInput.addEventListener('input', () => {
        document.getElementById('preview-location').textContent = locationInput.value || 'Lokasi';
    });
    
    capacityInput.addEventListener('input', () => {
        document.getElementById('preview-capacity').textContent = capacityInput.value || '0';
    });
    
    hourlyRateInput.addEventListener('input', () => {
        const rate = parseInt(hourlyRateInput.value) || 0;
        const rateElement = document.getElementById('preview-rate');
        if (rateElement) {
            rateElement.textContent = rate.toLocaleString('id-ID');
        }
    });
    
    descriptionInput.addEventListener('input', () => {
        document.getElementById('preview-description').textContent = descriptionInput.value || 'Tidak ada deskripsi';
    });
    
    activeCheckbox.addEventListener('change', () => {
        const statusElement = document.getElementById('preview-status');
        statusElement.textContent = activeCheckbox.checked ? 'Aktif' : 'Tidak Aktif';
        statusElement.className = activeCheckbox.checked ? 'badge bg-success' : 'badge bg-danger';
    });
    
    // Update facilities preview
    function updateFacilitiesPreview() {
        const selectedFacilities = Array.from(facilityCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value.replace('_', ' '));
            
        const facilitiesContainer = document.getElementById('preview-facilities');
        
        if (selectedFacilities.length > 0) {
            facilitiesContainer.innerHTML = selectedFacilities
                .map(facility => `<span class="badge bg-info me-1">${facility}</span>`)
                .join('');
        } else {
            facilitiesContainer.innerHTML = '<span class="text-muted">Tidak ada fasilitas</span>';
        }
    }
    
    facilityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateFacilitiesPreview);
    });
    
    // Form validation
    document.getElementById('roomForm').addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        const location = locationInput.value.trim();
        const capacity = parseInt(capacityInput.value);
        
        if (!name || !location || !capacity || capacity < 1) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Data Tidak Lengkap',
                text: 'Mohon lengkapi semua field yang wajib diisi!'
            });
            return;
        }
        
        // Show loading
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Menyimpan...';
        submitBtn.disabled = true;
    });
});

// Show success message if redirected from update
<?php if(session('success')): ?>
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: '<?php echo e(session('success')); ?>',
        timer: 3000,
        showConfirmButton: false
    });
<?php endif; ?>

// Show error message if any
<?php if(session('error')): ?>
    Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: '<?php echo e(session('error')); ?>'
    });
<?php endif; ?>
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Rapat\resources\views/admin/rooms/edit.blade.php ENDPATH**/ ?>