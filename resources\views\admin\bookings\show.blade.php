@extends('layouts.dashboard')

@section('title', 'Detail Pemesanan')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Detail Pemesanan #{{ $booking->id }}</h1>
            <p class="text-muted">Informasi lengkap pemesanan ruang rapat</p>
        </div>
        <div>
            <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
            @if($booking->status == 'pending')
                <button type="button" class="btn btn-success ms-2" onclick="approveBooking({{ $booking->id }})">
                    <i class="bi bi-check"></i> Setujui
                </button>
                <button type="button" class="btn btn-danger ms-2" onclick="rejectBooking({{ $booking->id }})">
                    <i class="bi bi-x"></i> Tolak
                </button>
            @endif
        </div>
    </div>

    <div class="row">
        <!-- Booking Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Pemesanan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Judul Acara</label>
                            <p class="form-control-plaintext">{{ $booking->title }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Status</label>
                            <div>
                                @if($booking->status == 'pending')
                                    <span class="badge bg-warning">Menunggu Persetujuan</span>
                                @elseif($booking->status == 'approved')
                                    <span class="badge bg-success">Disetujui</span>
                                @elseif($booking->status == 'rejected')
                                    <span class="badge bg-danger">Ditolak</span>
                                @elseif($booking->status == 'cancelled')
                                    <span class="badge bg-secondary">Dibatalkan</span>
                                @elseif($booking->status == 'completed')
                                    <span class="badge bg-info">Selesai</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label font-weight-bold">Deskripsi</label>
                            <p class="form-control-plaintext">{{ $booking->description ?: 'Tidak ada deskripsi' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Tanggal & Waktu Mulai</label>
                            <p class="form-control-plaintext">
                                {{ $booking->start_time->format('d F Y, H:i') }} WIB
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Tanggal & Waktu Selesai</label>
                            <p class="form-control-plaintext">
                                {{ $booking->end_time->format('d F Y, H:i') }} WIB
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Durasi</label>
                            <p class="form-control-plaintext">
                                {{ $booking->start_time->diffInHours($booking->end_time) }} jam
                                {{ $booking->start_time->diffInMinutes($booking->end_time) % 60 }} menit
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label font-weight-bold">Jumlah Peserta</label>
                            <p class="form-control-plaintext">{{ $booking->attendees_count }} orang</p>
                        </div>
                        @if($booking->notes)
                        <div class="col-md-12 mb-3">
                            <label class="form-label font-weight-bold">Catatan</label>
                            <p class="form-control-plaintext">{{ $booking->notes }}</p>
                        </div>
                        @endif
                        @if($booking->rejection_reason)
                        <div class="col-md-12 mb-3">
                            <label class="form-label font-weight-bold text-danger">Alasan Penolakan</label>
                            <p class="form-control-plaintext text-danger">{{ $booking->rejection_reason }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Required Facilities -->
            @if($booking->required_facilities)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Fasilitas yang Dibutuhkan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @php
                            $facilities = is_string($booking->required_facilities) ? 
                                json_decode($booking->required_facilities, true) : 
                                $booking->required_facilities;
                        @endphp
                        @if(is_array($facilities))
                            @foreach($facilities as $facility)
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <span>{{ $facility }}</span>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="col-12">
                                <p class="text-muted">Tidak ada fasilitas khusus yang dibutuhkan</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar Info -->
        <div class="col-lg-4">
            <!-- User Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Pemohon</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg mx-auto mb-3">
                            <div class="avatar-title rounded-circle bg-primary text-white fs-3">
                                {{ substr($booking->user->name, 0, 1) }}
                            </div>
                        </div>
                        <h5 class="mb-1">{{ $booking->user->name }}</h5>
                        <p class="text-muted mb-2">{{ $booking->user->email }}</p>
                        @if($booking->user->phone)
                        <p class="text-muted mb-2">{{ $booking->user->phone }}</p>
                        @endif
                        @if($booking->user->department)
                        <span class="badge bg-info">{{ $booking->user->department }}</span>
                        @endif
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="mb-1">{{ $booking->user->bookings()->count() }}</h5>
                                <small class="text-muted">Total Booking</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="mb-1">{{ $booking->user->bookings()->where('status', 'approved')->count() }}</h5>
                            <small class="text-muted">Disetujui</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Room Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Ruang</h6>
                </div>
                <div class="card-body">
                    <h5 class="mb-2">{{ $booking->meetingRoom->name }}</h5>
                    <p class="text-muted mb-3">{{ $booking->meetingRoom->description }}</p>
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="mb-1">{{ $booking->meetingRoom->capacity }}</h6>
                                <small class="text-muted">Kapasitas</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1">{{ $booking->meetingRoom->location }}</h6>
                            <small class="text-muted">Lokasi</small>
                        </div>
                    </div>

                    @if($booking->meetingRoom->facilities)
                    <div class="mb-3">
                        <h6 class="mb-2">Fasilitas Tersedia:</h6>
                        @php
                            $roomFacilities = is_string($booking->meetingRoom->facilities) ? 
                                json_decode($booking->meetingRoom->facilities, true) : 
                                $booking->meetingRoom->facilities;
                        @endphp
                        @if(is_array($roomFacilities))
                            @foreach($roomFacilities as $facility)
                            <span class="badge bg-light text-dark me-1 mb-1">{{ $facility }}</span>
                            @endforeach
                        @endif
                    </div>
                    @endif

                    <a href="{{ route('rooms.show', $booking->meetingRoom) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i> Lihat Detail Ruang
                    </a>
                </div>
            </div>

            <!-- Approval Info -->
            @if($booking->approved_by)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Persetujuan</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Disetujui oleh:</strong><br>
                        <span>{{ $booking->approvedBy ? $booking->approvedBy->name : 'Data approver tidak ditemukan' }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Tanggal persetujuan:</strong><br>
                        <span>{{ $booking->approved_at ? $booking->approved_at->format('d F Y, H:i') : '-' }} WIB</span>
                    </div>
                </div>
            </div>
            @endif

            <!-- Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Pemesanan Dibuat</h6>
                                <small class="text-muted">{{ $booking->created_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @if($booking->approved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Pemesanan Disetujui</h6>
                                <small class="text-muted">{{ $booking->approved_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @endif
                        @if($booking->status == 'rejected')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Pemesanan Ditolak</h6>
                                <small class="text-muted">{{ $booking->updated_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -40px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -35px;
    top: 10px;
    bottom: 10px;
    width: 2px;
    background-color: #e2e8f0;
}

.border-end {
    border-right: 1px solid #e2e8f0 !important;
}

.fs-3 {
    font-size: 1.75rem !important;
}
</style>
@endpush

@push('scripts')
<script>
// Approve booking dengan SweetAlert2
function approveBooking(bookingId) {
    Swal.fire({
        title: 'Setujui Pemesanan',
        text: 'Apakah Anda yakin ingin menyetujui pemesanan ini?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Setujui',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            performBookingAction(bookingId, 'approve');
        }
    });
}

// Reject booking dengan SweetAlert2
function rejectBooking(bookingId) {
    Swal.fire({
        title: 'Tolak Pemesanan',
        html: `
            <p>Apakah Anda yakin ingin menolak pemesanan ini?</p>
            <div class="mt-3">
                <label for="rejection-reason" class="form-label text-start d-block">Alasan Penolakan</label>
                <textarea id="rejection-reason" class="form-control" rows="3" placeholder="Masukkan alasan penolakan..."></textarea>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Tolak',
        cancelButtonText: 'Batal',
        preConfirm: () => {
            const rejectionReason = document.getElementById('rejection-reason').value.trim();
            if (!rejectionReason) {
                Swal.showValidationMessage('Alasan penolakan harus diisi');
                return false;
            }
            return rejectionReason;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performBookingAction(bookingId, 'reject', result.value);
        }
    });
}

// Perform booking action
function performBookingAction(bookingId, action, rejectionReason = null) {
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('_method', 'PATCH');
    
    if (rejectionReason) {
        formData.append('rejection_reason', rejectionReason);
    }

    // Show loading
    Swal.fire({
        title: 'Memproses...',
        text: 'Mohon tunggu sebentar',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(`/admin/bookings/${bookingId}/${action}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                window.location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Terjadi kesalahan sistem. Silakan coba lagi.'
        });
    });
}
</script>
@endpush
