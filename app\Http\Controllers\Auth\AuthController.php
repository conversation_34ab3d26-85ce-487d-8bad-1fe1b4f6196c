<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\SecurityLog;
use Carbon\Carbon;

class AuthController extends Controller
{
    /**
     * Show login form
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:255',
            'password' => 'required|string|min:8',
        ], [
            'username.required' => 'Username wajib diisi',
            'password.required' => 'Password wajib diisi',
            'password.min' => 'Password minimal 8 karakter',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $credentials = $request->only('username', 'password');
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        // Find user by username
        $user = User::where('username', $credentials['username'])->first();

        if (!$user) {
            SecurityLog::logAction(
                'failed_login_invalid_username',
                null,
                'medium',
                [
                    'username' => $credentials['username'],
                    'reason' => 'Username not found'
                ]
            );

            return back()->withErrors([
                'username' => 'Username atau password tidak valid'
            ])->withInput();
        }

        // Check if account is locked
        if ($user->isLocked()) {
            SecurityLog::logAction(
                'login_attempt_locked_account',
                $user->id,
                'high',
                ['locked_until' => $user->locked_until->toISOString()]
            );

            return back()->withErrors([
                'username' => 'Akun terkunci sampai ' . $user->locked_until->format('d/m/Y H:i')
            ])->withInput();
        }

        // Check if account is active
        if (!$user->is_active) {
            SecurityLog::logAction(
                'login_attempt_inactive_account',
                $user->id,
                'medium',
                ['username' => $credentials['username']]
            );

            return back()->withErrors([
                'username' => 'Akun tidak aktif. Hubungi administrator.'
            ])->withInput();
        }

        // Verify password
        if (!Hash::check($credentials['password'], $user->password)) {
            $user->incrementFailedAttempts();

            SecurityLog::logAction(
                'failed_login_invalid_password',
                $user->id,
                'medium',
                [
                    'failed_attempts' => $user->failed_login_attempts,
                    'locked' => $user->isLocked()
                ]
            );

            $message = 'Username atau password tidak valid';
            if ($user->failed_login_attempts >= 3) {
                $message = 'Akun terkunci karena 3x percobaan gagal';
            }

            return back()->withErrors([
                'username' => $message
            ])->withInput();
        }

        // Check email verification
        if (!$user->email_verified_at) {
            return back()->withErrors([
                'username' => 'Email belum diverifikasi. Silakan cek email Anda.'
            ])->withInput();
        }

        // Successful login
        $sessionId = session()->getId();
        
        // Authenticate user
        Auth::login($user);
        $sessionId = session()->getId();
        // Setelah login, cek session aktif lain (id != sessionId)
        if ($user->role !== 'admin') {
            $activeSession = \DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('id', '!=', $sessionId)
                ->where('last_activity', '>=', now()->subMinutes(config('session.lifetime'))->timestamp)
                ->first();
            if ($activeSession) {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route('login')->withErrors([
                    'username' => 'Akun Anda sedang aktif di perangkat lain. Silakan logout terlebih dahulu.'
                ])->with('sweetalert', [
                    'icon' => 'error',
                    'title' => 'Login Ditolak',
                    'text' => 'Akun Anda sedang aktif di perangkat lain. Silakan logout terlebih dahulu.'
                ])->withInput();
            }
        }
        // Update user login info
        $user->updateLastLogin($ipAddress, $sessionId);
        $user->resetFailedAttempts();

        // Cek session aktif lain sebelum login (khusus user biasa)
        if ($user->role !== 'admin') {
            $currentSessionId = session()->getId();
            $activeSession = \DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('id', '!=', $currentSessionId)
                ->where('last_activity', '>=', now()->subMinutes(config('session.lifetime'))->timestamp)
                ->first();
            if ($activeSession) {
                return back()->withErrors([
                    'username' => 'Akun Anda sedang aktif di perangkat lain. Silakan logout terlebih dahulu.'
                ])->with('sweetalert', [
                    'icon' => 'error',
                    'title' => 'Login Ditolak',
                    'text' => 'Akun Anda sedang aktif di perangkat lain. Silakan logout terlebih dahulu.'
                ])->withInput();
            }
        }

        // Hapus semua session lain milik user ini (kecuali session saat ini) jika bukan admin
        if ($user->role !== 'admin') {
            \DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('id', '!=', $sessionId)
                ->delete();
        }

        // Log successful login
        SecurityLog::logAction(
            'successful_login',
            $user->id,
            'low',
            [
                'login_method' => 'username_password',
                'session_id' => $sessionId
            ],
            $sessionId
        );

        return redirect()->intended('/dashboard')->with('success', 'Selamat datang, ' . $user->name . '!');
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username|alpha_dash',
            'email' => 'required|string|email|max:255|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'department' => 'nullable|string|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
                'confirmed'
            ],
        ], [
            'name.required' => 'Nama wajib diisi',
            'username.required' => 'Username wajib diisi',
            'username.unique' => 'Username sudah digunakan',
            'username.alpha_dash' => 'Username hanya boleh huruf, angka, dash, dan underscore',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah digunakan',
            'password.required' => 'Password wajib diisi',
            'password.min' => 'Password minimal 8 karakter',
            'password.regex' => 'Password harus mengandung huruf besar, huruf kecil, angka, dan simbol',
            'password.confirmed' => 'Konfirmasi password tidak cocok',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Create verification token
        $verificationToken = Str::random(64);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'phone' => $request->phone,
            'department' => $request->department,
            'password' => Hash::make($request->password),
            'email_verification_token' => $verificationToken,
            'email_verification_expires_at' => Carbon::now()->addHours(24),
            'is_active' => true,
        ]);

        // Log registration
        SecurityLog::logAction(
            'user_registered',
            $user->id,
            'low',
            [
                'username' => $user->username,
                'email' => $user->email
            ]
        );

        // Send verification email
        $this->sendEmailVerification($user, $verificationToken);

        return redirect()->route('login')->with('success', 
            'Registrasi berhasil! Silakan cek email Anda untuk verifikasi akun.');
    }

    /**
     * Send email verification
     */
    private function sendEmailVerification(User $user, string $token)
    {
        $verificationUrl = url('/email/verify/' . $token);
        
        // Send email using Laravel's mail system
        Mail::send('emails.verify', [
            'user' => $user,
            'verificationUrl' => $verificationUrl
        ], function ($message) use ($user) {
            $message->to($user->email);
            $message->subject('Verifikasi Email - Sistem Pemesanan Ruang Rapat');
        });
    }

    /**
     * Handle email verification
     */
    public function verifyEmail(Request $request, $token)
    {
        // Log for debugging
        \Log::info('Email verification attempt', [
            'token' => $token,
            'token_length' => strlen($token),
            'current_time' => Carbon::now()->toDateTimeString()
        ]);

        $user = User::where('email_verification_token', $token)
                   ->where('email_verification_expires_at', '>', Carbon::now())
                   ->first();

        \Log::info('User lookup result', [
            'user_found' => $user ? true : false,
            'user_id' => $user ? $user->id : null,
            'user_email' => $user ? $user->email : null
        ]);

        if (!$user) {
            // Try to find user without expiry check for debugging
            $userWithoutExpiry = User::where('email_verification_token', $token)->first();
            \Log::warning('Token verification failed', [
                'token' => $token,
                'user_without_expiry_check' => $userWithoutExpiry ? [
                    'id' => $userWithoutExpiry->id,
                    'email' => $userWithoutExpiry->email,
                    'expires_at' => $userWithoutExpiry->email_verification_expires_at ? $userWithoutExpiry->email_verification_expires_at->toDateTimeString() : null,
                    'is_expired' => $userWithoutExpiry->email_verification_expires_at ? $userWithoutExpiry->email_verification_expires_at->isPast() : null
                ] : null
            ]);

            return redirect()->route('login')->with('error', 
                'Token verifikasi tidak valid atau sudah expired.');
        }

        $user->update([
            'email_verified_at' => Carbon::now(),
            'email_verification_token' => null,
            'email_verification_expires_at' => null,
        ]);

        SecurityLog::logAction(
            'email_verified',
            $user->id,
            'low',
            ['verification_method' => 'email_token']
        );

        \Log::info('Email verification successful', [
            'user_id' => $user->id,
            'user_email' => $user->email
        ]);

        return view('auth.email-verified');
    }

    /**
     * Handle password reset request
     */
    public function sendPasswordResetEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ], [
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.exists' => 'Email tidak ditemukan',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::where('email', $request->email)->first();
        $resetToken = Str::random(64);

        $user->update([
            'password_reset_token' => $resetToken,
            'password_reset_expires_at' => Carbon::now()->addHours(1),
        ]);

        SecurityLog::logAction(
            'password_reset_requested',
            $user->id,
            'medium',
            ['email' => $user->email]
        );

        // Send reset email
        $this->sendPasswordResetEmailMessage($user, $resetToken);

        return back()->with('success', 
            'Link reset password telah dikirim ke email Anda.');
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmailMessage(User $user, string $token)
    {
        $resetUrl = url('/password/reset/' . $token);
        
        Mail::send('emails.password-reset', [
            'user' => $user,
            'resetUrl' => $resetUrl
        ], function ($message) use ($user) {
            $message->to($user->email);
            $message->subject('Reset Password - Sistem Pemesanan Ruang Rapat');
        });
    }

    /**
     * Show password reset form
     */
    public function showResetForm($token)
    {
        return view('auth.reset-password', ['token' => $token]);
    }

    /**
     * Handle password reset
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
                'confirmed'
            ],
        ], [
            'password.required' => 'Password wajib diisi',
            'password.min' => 'Password minimal 8 karakter',
            'password.regex' => 'Password harus mengandung huruf besar, huruf kecil, angka, dan simbol',
            'password.confirmed' => 'Konfirmasi password tidak cocok',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::where('password_reset_token', $request->token)
                   ->where('password_reset_expires_at', '>', Carbon::now())
                   ->first();

        if (!$user) {
            return redirect()->route('login')->with('error', 
                'Token reset password tidak valid atau sudah expired.');
        }

        $user->update([
            'password' => Hash::make($request->password),
            'password_reset_token' => null,
            'password_reset_expires_at' => null,
            'force_logout' => true, // Force logout from all sessions
        ]);

        SecurityLog::logAction(
            'password_reset_completed',
            $user->id,
            'medium',
            ['reset_method' => 'email_token']
        );

        return redirect()->route('login')->with('success', 
            'Password berhasil direset! Silakan login dengan password baru.');
    }

    /**
     * Show email verification notice
     */
    public function showVerificationNotice(Request $request)
    {
        return $request->user()->hasVerifiedEmail()
                    ? redirect()->intended(route('dashboard'))
                    : view('auth.verify-email');
    }

    /**
     * Resend email verification
     */
    public function resendVerificationEmail(Request $request)
    {
        // Validate email input
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ], [
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.exists' => 'Email tidak ditemukan dalam sistem'
        ]);

        // Find user by email
        $user = User::where('email', $request->email)->first();
        
        if (!$user) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email tidak ditemukan dalam sistem.'
                ], 404);
            }
            return back()->withErrors(['email' => 'Email tidak ditemukan dalam sistem.']);
        }

        // Check if already verified
        if ($user->hasVerifiedEmail()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email sudah terverifikasi. Anda dapat langsung login.'
                ], 400);
            }
            return back()->withErrors(['email' => 'Email sudah terverifikasi. Anda dapat langsung login.']);
        }

        // Generate new verification token
        $verificationToken = Str::random(64);
        
        $user->update([
            'email_verification_token' => $verificationToken,
            'email_verification_expires_at' => Carbon::now()->addHours(24),
        ]);

        // Send verification email
        try {
            $this->sendEmailVerification($user, $verificationToken);
            
            SecurityLog::logAction(
                'email_verification_resent',
                $user->id,
                'low',
                ['email' => $user->email]
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email verifikasi telah dikirim ke ' . $user->email . '. Silakan cek inbox atau folder spam Anda.'
                ]);
            }
            
            return back()->with('message', 'Email verifikasi telah dikirim ulang!');
            
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengirim email verifikasi. Silakan coba lagi nanti.'
                ], 500);
            }
            
            return back()->withErrors(['email' => 'Gagal mengirim email verifikasi. Silakan coba lagi nanti.']);
        }
    }

    /**
     * Handle logout
     */
    public function logout(Request $request)
    {
        $user = Auth::user();
        if ($user) {
            SecurityLog::logAction(
                'user_logout',
                $user->id,
                'low',
                ['logout_method' => 'manual'],
                session()->getId()
            );
            // Jangan update session_id ke null, biarkan session_id tetap untuk validasi double login
        }
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login')->with('logout_success', 'Anda telah berhasil logout.');
    }
}
