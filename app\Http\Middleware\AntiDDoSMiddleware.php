<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\SecurityLog;

class AntiDDoSMiddleware
{
    protected $ddosThresholds = [
        'requests_per_second' => 10,
        'requests_per_minute' => 200,
        'requests_per_hour' => 5000,
        'concurrent_connections' => 50
    ];

    public function handle(Request $request, Closure $next)
    {
        $ip = $request->ip();

        // Skip DDoS protection for development/localhost
        if ($this->isDevelopmentEnvironment($request, $ip)) {
            return $next($request);
        }

        // Skip DDoS protection for admin users
        if (Auth::check() && Auth::user()->role === 'admin') {
            return $next($request);
        }
        
        // Check for DDoS patterns
        $this->checkRequestPatterns($request, $ip);
        $this->checkConcurrentConnections($request, $ip);
        $this->checkSuspiciousUserAgents($request);
        $this->checkRequestSize($request);

        return $next($request);
    }

    protected function checkRequestPatterns(Request $request, $ip)
    {
        // Check requests per second
        $perSecondKey = "ddos_check_sec:{$ip}:" . now()->format('Y-m-d-H-i-s');
        $requestsPerSecond = Cache::increment($perSecondKey, 1);
        Cache::put($perSecondKey, $requestsPerSecond, 1); // Expire in 1 second

        if ($requestsPerSecond > $this->ddosThresholds['requests_per_second']) {
            $this->logAndBlock('ddos_requests_per_second', $request, [
                'requests_count' => $requestsPerSecond,
                'threshold' => $this->ddosThresholds['requests_per_second']
            ]);
        }

        // Check requests per minute
        $perMinuteKey = "ddos_check_min:{$ip}:" . now()->format('Y-m-d-H-i');
        $requestsPerMinute = Cache::increment($perMinuteKey, 1);
        Cache::put($perMinuteKey, $requestsPerMinute, 60); // Expire in 60 seconds

        if ($requestsPerMinute > $this->ddosThresholds['requests_per_minute']) {
            $this->logAndBlock('ddos_requests_per_minute', $request, [
                'requests_count' => $requestsPerMinute,
                'threshold' => $this->ddosThresholds['requests_per_minute']
            ]);
        }

        // Check requests per hour
        $perHourKey = "ddos_check_hour:{$ip}:" . now()->format('Y-m-d-H');
        $requestsPerHour = Cache::increment($perHourKey, 1);
        Cache::put($perHourKey, $requestsPerHour, 3600); // Expire in 3600 seconds

        if ($requestsPerHour > $this->ddosThresholds['requests_per_hour']) {
            $this->logAndBlock('ddos_requests_per_hour', $request, [
                'requests_count' => $requestsPerHour,
                'threshold' => $this->ddosThresholds['requests_per_hour']
            ]);
        }
    }

    protected function checkConcurrentConnections(Request $request, $ip)
    {
        $connectionKey = "concurrent_connections:{$ip}";
        $currentConnections = Cache::get($connectionKey, 0);
        
        // Increment connection count
        Cache::put($connectionKey, $currentConnections + 1, 30); // 30 seconds timeout

        if ($currentConnections > $this->ddosThresholds['concurrent_connections']) {
            $this->logAndBlock('ddos_concurrent_connections', $request, [
                'connections_count' => $currentConnections,
                'threshold' => $this->ddosThresholds['concurrent_connections']
            ]);
        }

        // Clean up connection count after request
        register_shutdown_function(function() use ($connectionKey, $currentConnections) {
            if ($currentConnections > 0) {
                Cache::put($connectionKey, $currentConnections - 1, 30);
            }
        });
    }

    protected function checkSuspiciousUserAgents(Request $request)
    {
        $userAgent = $request->userAgent();
        $suspiciousPatterns = [
            '/bot|crawler|spider|scraper/i',
            '/curl|wget|python|java|go-http/i',
            '/test|benchmark|stress|load/i',
            '/ddos|flood|attack/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $this->logSecurityEvent('suspicious_user_agent_ddos', $request, 'high', [
                    'user_agent' => $userAgent,
                    'pattern' => $pattern
                ]);
                
                // Apply stricter rate limiting for suspicious user agents
                $key = 'suspicious_ua_limit:' . $request->ip();
                if (RateLimiter::tooManyAttempts($key, 10)) {
                    abort(429, 'Aktivitas mencurigakan terdeteksi.');
                }
                RateLimiter::hit($key, 300); // 5 minutes
                break;
            }
        }
    }

    protected function checkRequestSize(Request $request)
    {
        $contentLength = $request->header('Content-Length', 0);
        $maxSize = 10 * 1024 * 1024; // 10MB

        if ($contentLength > $maxSize) {
            $this->logAndBlock('ddos_large_request', $request, [
                'content_length' => $contentLength,
                'max_allowed' => $maxSize
            ]);
        }
    }

    protected function logAndBlock($event, Request $request, $data = [])
    {
        $this->logSecurityEvent($event, $request, 'critical', $data);
        
        // Temporary IP ban
        $banKey = 'ip_banned:' . $request->ip();
        Cache::put($banKey, true, 300); // Ban for 5 minutes
        
        abort(429, 'Aktivitas DDoS terdeteksi. IP Anda sementara diblokir.');
    }

    protected function logSecurityEvent($event, Request $request, $severity = 'medium', $additional_data = [])
    {
        $data = array_merge([
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => Auth::id()
        ], $additional_data);

        SecurityLog::logAction(
            $event,
            Auth::id(),
            $severity,
            $data,
            session()->getId()
        );
    }

    /**
     * Check if running in development environment
     */
    protected function isDevelopmentEnvironment(Request $request, $ip): bool
    {
        // Check environment
        if (app()->environment('local', 'development', 'testing')) {
            return true;
        }

        // Development IP ranges
        $developmentRanges = [
            '*********/8',      // Localhost
            '10.0.0.0/8',       // Private network
            '**********/12',    // Private network
            '***********/16',   // Private network
        ];

        foreach ($developmentRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        // Check if accessing from localhost
        if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
            return true;
        }

        return false;
    }

    /**
     * Check if IP is in range
     */
    protected function ipInRange($ip, $range): bool
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }
}
