<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class SecurityController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'admin']);
    }

    public function index()
    {
        $whitelist = Cache::get('security_ip_whitelist', [
            '127.0.0.1',
            '::1',
            'localhost'
        ]);

        $stats = Cache::get('security_stats', []);

        return view('admin.security.index', compact('whitelist', 'stats'));
    }

    public function addToWhitelist(Request $request)
    {
        $request->validate([
            'ip' => 'required|ip'
        ]);

        $ip = $request->ip_address;
        $whitelist = Cache::get('security_ip_whitelist', []);

        if (!in_array($ip, $whitelist)) {
            $whitelist[] = $ip;
            Cache::put('security_ip_whitelist', $whitelist, 86400);
            
            // Clear any blocks for this IP
            Cache::forget("ip_banned:{$ip}");
            Cache::forget("ip_blocked:{$ip}");
        }

        return response()->json([
            'success' => true,
            'message' => "IP {$ip} berhasil ditambahkan ke whitelist.",
            'whitelist' => $whitelist
        ]);
    }

    public function removeFromWhitelist(Request $request)
    {
        $ip = $request->ip;
        $whitelist = Cache::get('security_ip_whitelist', []);

        $whitelist = array_filter($whitelist, function($whitelistIP) use ($ip) {
            return $whitelistIP !== $ip;
        });

        Cache::put('security_ip_whitelist', array_values($whitelist), 86400);

        return response()->json([
            'success' => true,
            'message' => "IP {$ip} berhasil dihapus dari whitelist.",
            'whitelist' => array_values($whitelist)
        ]);
    }

    public function clearCache()
    {
        // Clear security related caches
        Cache::forget('security_ip_whitelist');
        Cache::forget('security_stats');
        
        // Clear all IP blocks
        $pattern = 'ip_banned:*';
        Cache::flush(); // For simplicity, flush all cache

        return response()->json([
            'success' => true,
            'message' => 'Cache keamanan berhasil dibersihkan.'
        ]);
    }

    public function toggleSecurityMode(Request $request)
    {
        $mode = $request->mode; // 'strict', 'normal', 'development'
        
        Cache::put('security_mode', $mode, 86400);

        return response()->json([
            'success' => true,
            'message' => "Mode keamanan diubah ke: {$mode}"
        ]);
    }
}
