<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MeetingRoom;
use App\Models\SecurityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class RoomManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin', 'security']);
    }

    public function index()
    {
        $rooms = MeetingRoom::with(['bookings' => function($query) {
            $query->where('start_time', '>=', now()->startOfDay())
                  ->where('status', 'approved')
                  ->orderBy('start_time');
        }, 'facilities'])->get();

        foreach ($rooms as $room) {
            $room->total_bookings = $room->bookings()->count();
            $room->this_month_bookings = $room->bookings()
                ->whereMonth('start_time', now()->month)
                ->whereYear('start_time', now()->year)
                ->count();
        }

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_view_rooms',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return view('admin.rooms.index', compact('rooms'));
    }

    public function create()
    {
        $facilities = \App\Models\Facility::orderBy('name')->get();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_view_create_room_form',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return view('admin.rooms.create', compact('facilities'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:meeting_rooms,name',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1|max:1000',
            'description' => 'nullable|string',
            'facilities' => 'nullable|array',
            'facilities.*' => 'exists:facilities,id',
            'hourly_rate' => 'nullable|numeric|min:0|sometimes',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $data = $request->only(['name', 'location', 'capacity', 'description']);
        $data['hourly_rate'] = $request->filled('hourly_rate') ? (float)$request->hourly_rate : 0; // Default to 0 if empty
        $data['facilities'] = $request->facilities ? json_encode($request->facilities) : null;
        $data['is_active'] = true;

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('rooms', 'public');
            $data['image'] = $imagePath;
        }

        $room = MeetingRoom::create($data);
        if ($request->has('facilities')) {
            $room->facilities()->sync($request->facilities);
        }

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_create_room',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => "Created room: {$room->name}",
        ]);

        return redirect()->route('admin.rooms.index')
            ->with('success', 'Ruang rapat berhasil dibuat!');
    }

    public function show(MeetingRoom $room)
    {
        $room->load(['bookings' => function($query) {
            $query->with('user')
                  ->orderBy('start_time', 'desc')
                  ->take(10);
        }, 'facilities']);

        $stats = [
            'total_bookings' => $room->bookings()->count(),
            'this_month' => $room->bookings()
                ->whereMonth('start_time', now()->month)
                ->whereYear('start_time', now()->year)
                ->count(),
            'approved' => $room->bookings()->where('status', 'approved')->count(),
            'pending' => $room->bookings()->where('status', 'pending')->count(),
            'revenue' => 0, // Revenue calculation can be added later when total_cost column exists
        ];

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_view_room_details',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => "Viewed room: {$room->name}",
        ]);

        return view('admin.rooms.show', compact('room', 'stats'));
    }

    public function edit(MeetingRoom $room)
    {
        $facilities = \App\Models\Facility::orderBy('name')->get();
        // Use the relationship method to get selected facilities
        $selectedFacilities = $room->facilities()->pluck('facilities.id')->toArray();
        return view('admin.rooms.edit', compact('room', 'facilities', 'selectedFacilities'));
    }

    public function update(Request $request, MeetingRoom $room)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('meeting_rooms', 'name')->ignore($room->id)],
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1|max:1000',
            'description' => 'nullable|string',
            'facilities' => 'nullable|array',
            'facilities.*' => 'exists:facilities,id',
            'hourly_rate' => 'nullable|numeric|min:0|sometimes',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
        ]);

        $data = $request->only(['name', 'location', 'capacity', 'description']);
        $data['hourly_rate'] = $request->filled('hourly_rate') ? (float)$request->hourly_rate : 0; // Default to 0 if empty
        $data['facilities'] = $request->facilities ? json_encode($request->facilities) : null;
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('rooms', 'public');
            $data['image'] = $imagePath;
        }

        $room->update($data);
        $room->facilities()->sync($request->facilities ?? []);

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_update_room',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => "Updated room: {$room->name}",
        ]);

        return redirect()->route('admin.rooms.index')
            ->with('success', 'Ruang rapat berhasil diupdate!');
    }

    public function destroy(MeetingRoom $room)
    {
        // Check if room has future bookings
        $futureBookings = $room->bookings()
            ->where('start_time', '>', now())
            ->where('status', '!=', 'cancelled')
            ->count();

        if ($futureBookings > 0) {
            return redirect()->route('admin.rooms.index')
                ->with('error', 'Tidak dapat menghapus ruangan yang memiliki pemesanan aktif di masa depan.');
        }

        $roomName = $room->name;
        $room->delete();

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_delete_room',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => "Deleted room: {$roomName}",
        ]);

        return redirect()->route('admin.rooms.index')
            ->with('success', 'Ruang rapat berhasil dihapus!');
    }

    public function toggleStatus(MeetingRoom $room)
    {
        $oldStatus = $room->is_active;
        $room->is_active = !$room->is_active;
        $room->save();

        $status = $room->is_active ? 'diaktifkan' : 'dinonaktifkan';

        SecurityLog::create([
            'user_id' => Auth::id(),
            'action' => 'admin_toggle_room_status',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => "Room {$room->name} {$status}",
        ]);

        // Return JSON response for AJAX
        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Ruangan berhasil {$status}!",
                'is_active' => $room->is_active,
                'status_text' => $room->is_active ? 'Aktif' : 'Tidak Aktif'
            ]);
        }

        return redirect()->back()->with('success', "Ruangan berhasil {$status}!");
    }
}
