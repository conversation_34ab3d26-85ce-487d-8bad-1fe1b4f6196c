@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON><PERSON>')
@section('page-title', '<PERSON><PERSON><PERSON><PERSON>')

@push('styles')
<style>
    .bookings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .bookings-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .bookings-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .bookings-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        margin-left: auto;
    }

    .filter-dropdown {
        position: relative;
    }

    .filter-btn {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .new-booking-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .new-booking-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        color: white;
        text-decoration: none;
    }

    .bookings-grid {
        display: grid;
        gap: 1.5rem;
    }

    .booking-card {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 4px 20px var(--shadow);
        transition: all 0.2s;
    }

    .booking-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px var(--shadow);
    }

    .booking-header-card {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .booking-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .booking-room {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .booking-status {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-pending {
        background: rgba(251, 191, 36, 0.2);
        color: #f59e0b;
        border: 1px solid #f59e0b;
    }

    .status-approved {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid #22c55e;
    }

    .status-rejected {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border: 1px solid #ef4444;
    }

    .booking-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .booking-detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .booking-detail-icon {
        color: var(--primary-color);
    }

    .booking-description {
        background: var(--bg-secondary);
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        color: var(--text-secondary);
        font-style: italic;
    }

    .booking-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-edit {
        background: var(--info-color);
        color: white;
    }

    .btn-edit:hover {
        background: #0891b2;
        color: white;
        text-decoration: none;
    }

    .btn-delete {
        background: var(--error-color);
        color: white;
    }

    .btn-delete:hover {
        background: #dc2626;
    }

    .btn-view {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }

    .btn-view:hover {
        background: var(--primary-color);
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-secondary);
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .empty-subtitle {
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .bookings-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .bookings-actions {
            margin-left: 0;
            justify-content: space-between;
        }

        .booking-header-card {
            flex-direction: column;
            gap: 1rem;
        }

        .booking-details {
            grid-template-columns: 1fr;
        }

        .booking-actions {
            flex-wrap: wrap;
        }
    }
</style>
@endpush

@section('content')
<div class="bookings-container">
    <div class="bookings-header">
        <h1 class="bookings-title">Pemesanan Saya</h1>
        <div class="bookings-actions">
            <div class="filter-dropdown">
                <button class="filter-btn" id="filter-btn">
                    <i class="bi bi-funnel"></i>
                    <span>Semua Status</span>
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
            <a href="{{ route('bookings.create') }}" class="new-booking-btn">
                <i class="bi bi-plus-circle"></i>
                Buat Pemesanan
            </a>
        </div>
    </div>

    @if($bookings->count() > 0)
    <div class="bookings-grid">
        @foreach($bookings as $booking)
        <div class="booking-card" data-status="{{ $booking->status }}">
            <div class="booking-header-card">
                <div>
                    <h3 class="booking-title">{{ $booking->title }}</h3>
                    <div class="booking-room">
                        <i class="bi bi-door-open"></i>
                        {{ $booking->meetingRoom->name }}
                    </div>
                </div>
                <span class="booking-status status-{{ $booking->status }}">
                    @if($booking->status === 'pending')
                        <i class="bi bi-clock"></i> Menunggu
                    @elseif($booking->status === 'approved')
                        <i class="bi bi-check-circle"></i> Disetujui
                    @elseif($booking->status === 'rejected')
                        <i class="bi bi-x-circle"></i> Ditolak
                    @endif
                </span>
            </div>

            <div class="booking-details">
                <div class="booking-detail-item">
                    <i class="bi bi-calendar-event booking-detail-icon"></i>
                    <span>{{ $booking->start_time->format('d/m/Y') }}</span>
                </div>
                <div class="booking-detail-item">
                    <i class="bi bi-clock booking-detail-icon"></i>
                    <span>{{ $booking->start_time->format('H:i') }} - {{ $booking->end_time->format('H:i') }}</span>
                </div>
                <div class="booking-detail-item">
                    <i class="bi bi-people booking-detail-icon"></i>
                    <span>{{ $booking->participants ?? 1 }} orang</span>
                </div>
                <div class="booking-detail-item">
                    <i class="bi bi-hourglass-split booking-detail-icon"></i>
                    <span>
                        @php
                            $duration = $booking->start_time->diffInMinutes($booking->end_time);
                            $hours = floor($duration / 60);
                            $minutes = $duration % 60;
                        @endphp
                        {{ $hours > 0 ? $hours . 'j ' : '' }}{{ $minutes > 0 ? $minutes . 'm' : '' }}
                    </span>
                </div>
            </div>

            @if($booking->description)
            <div class="booking-description">
                <i class="bi bi-chat-quote"></i>
                {{ $booking->description }}
            </div>
            @endif

            @if($booking->status === 'rejected' && $booking->rejection_reason)
            <div class="booking-description" style="background: rgba(239, 68, 68, 0.1); border-left: 3px solid #ef4444;">
                <strong>Alasan penolakan:</strong><br>
                {{ $booking->rejection_reason }}
            </div>
            @endif

            <div class="booking-actions">
                <a href="{{ route('bookings.show', $booking) }}" class="action-btn btn-view">
                    <i class="bi bi-eye"></i>
                    Detail
                </a>
                
                @if($booking->status === 'pending')
                <a href="{{ route('bookings.edit', $booking) }}" class="action-btn btn-edit">
                    <i class="bi bi-pencil"></i>
                    Edit
                </a>
                @endif
                
                @if($booking->status !== 'approved' || $booking->start_time > now())
                <button class="action-btn btn-delete" onclick="deleteBooking({{ $booking->id }})">
                    <i class="bi bi-trash"></i>
                    Hapus
                </button>
                @endif
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    @if($bookings->hasPages())
    <div class="mt-4">
        {{ $bookings->links() }}
    </div>
    @endif

    @else
    <div class="empty-state">
        <div class="empty-icon">
            <i class="bi bi-calendar-x"></i>
        </div>
        <h3 class="empty-title">Belum Ada Pemesanan</h3>
        <p class="empty-subtitle">Anda belum memiliki pemesanan ruang rapat. Buat pemesanan pertama Anda sekarang!</p>
        <a href="{{ route('bookings.create') }}" class="new-booking-btn">
            <i class="bi bi-plus-circle"></i>
            Buat Pemesanan Pertama
        </a>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function deleteBooking(bookingId) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: 'Apakah Anda yakin ingin menghapus pemesanan ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Ya, Hapus',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/bookings/${bookingId}`;
            
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            
            const csrfField = document.createElement('input');
            csrfField.type = 'hidden';
            csrfField.name = '_token';
            csrfField.value = '{{ csrf_token() }}';
            
            form.appendChild(methodField);
            form.appendChild(csrfField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Filter functionality
const filterBtn = document.getElementById('filter-btn');
const bookingCards = document.querySelectorAll('.booking-card');

filterBtn.addEventListener('click', function() {
    // Create dropdown menu
    const existingDropdown = document.querySelector('.filter-dropdown-menu');
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }

    const dropdown = document.createElement('div');
    dropdown.className = 'theme-dropdown show';
    dropdown.style.cssText = 'position: absolute; top: 100%; left: 0; right: 0;';
    dropdown.innerHTML = `
        <button class="theme-option" onclick="filterBookings('all')">
            <span>Semua Status</span>
        </button>
        <button class="theme-option" onclick="filterBookings('pending')">
            <span>Menunggu Persetujuan</span>
        </button>
        <button class="theme-option" onclick="filterBookings('approved')">
            <span>Disetujui</span>
        </button>
        <button class="theme-option" onclick="filterBookings('rejected')">
            <span>Ditolak</span>
        </button>
    `;
    dropdown.className = 'filter-dropdown-menu';
    
    this.parentElement.appendChild(dropdown);
});

function filterBookings(status) {
    const filterText = {
        'all': 'Semua Status',
        'pending': 'Menunggu Persetujuan',
        'approved': 'Disetujui',
        'rejected': 'Ditolak'
    };
    
    filterBtn.querySelector('span').textContent = filterText[status];
    
    bookingCards.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
    
    document.querySelector('.filter-dropdown-menu')?.remove();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.filter-dropdown')) {
        document.querySelector('.filter-dropdown-menu')?.remove();
    }
});
</script>
@endpush
