@extends('layouts.dashboard')

@section('title', '<PERSON>')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit P<PERSON> #{{ $booking->id }}</h1>
            <p class="text-muted">Ubah informasi pemesanan ruang rapat</p>
        </div>
        <div>
            <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Form Edit Pemesanan</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.bookings.update', $booking) }}" method="POST" id="bookingForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="title" class="form-label">Judul Acara <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $booking->title) }}" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="meeting_room_id" class="form-label">Ruang Rapat <span class="text-danger">*</span></label>
                                <select class="form-select @error('meeting_room_id') is-invalid @enderror" 
                                        id="meeting_room_id" name="meeting_room_id" required>
                                    <option value="">Pilih Ruang Rapat</option>
                                    @foreach($rooms as $room)
                                    <option value="{{ $room->id }}" 
                                            {{ old('meeting_room_id', $booking->meeting_room_id) == $room->id ? 'selected' : '' }}
                                            data-capacity="{{ $room->capacity }}" 
                                            data-facilities="{{ $room->facilities }}">
                                        {{ $room->name }} (Kapasitas: {{ $room->capacity }} orang)
                                    </option>
                                    @endforeach
                                </select>
                                @error('meeting_room_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi acara (opsional)">{{ old('description', $booking->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_time" class="form-label">Waktu Mulai <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control @error('start_time') is-invalid @enderror" 
                                       id="start_time" name="start_time" 
                                       value="{{ old('start_time', $booking->start_time->format('Y-m-d\TH:i')) }}" required>
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="end_time" class="form-label">Waktu Selesai <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control @error('end_time') is-invalid @enderror" 
                                       id="end_time" name="end_time" 
                                       value="{{ old('end_time', $booking->end_time->format('Y-m-d\TH:i')) }}" required>
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="attendees_count" class="form-label">Jumlah Peserta <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('attendees_count') is-invalid @enderror" 
                                       id="attendees_count" name="attendees_count" min="1" 
                                       value="{{ old('attendees_count', $booking->attendees_count) }}" required>
                                @error('attendees_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text" id="capacityWarning"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status">
                                    <option value="pending" {{ old('status', $booking->status) == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ old('status', $booking->status) == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ old('status', $booking->status) == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                    <option value="cancelled" {{ old('status', $booking->status) == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                                    <option value="completed" {{ old('status', $booking->status) == 'completed' ? 'selected' : '' }}>Selesai</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fasilitas yang Dibutuhkan</label>
                            <div id="facilitiesContainer" class="border rounded p-3">
                                <!-- Facilities will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="mb-3" id="rejectionReasonContainer" style="display: none;">
                            <label for="rejection_reason" class="form-label">Alasan Penolakan</label>
                            <textarea class="form-control @error('rejection_reason') is-invalid @enderror" 
                                      id="rejection_reason" name="rejection_reason" rows="3" 
                                      placeholder="Masukkan alasan penolakan jika status ditolak">{{ old('rejection_reason', $booking->rejection_reason) }}</textarea>
                            @error('rejection_reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan Admin</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" 
                                      placeholder="Catatan internal (opsional)">{{ old('notes', $booking->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-secondary">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Room Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Ruang Saat Ini</h6>
                </div>
                <div class="card-body" id="roomInfo">
                    <div class="text-center">
                        <h5>{{ $booking->meetingRoom->name }}</h5>
                        <p class="text-muted">{{ $booking->meetingRoom->description }}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6>{{ $booking->meetingRoom->capacity }}</h6>
                                <small class="text-muted">Kapasitas</small>
                            </div>
                            <div class="col-6">
                                <h6>{{ $booking->meetingRoom->location }}</h6>
                                <small class="text-muted">Lokasi</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking History -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Riwayat Perubahan</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Pemesanan Dibuat</h6>
                                <small class="text-muted">{{ $booking->created_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @if($booking->approved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Disetujui</h6>
                                <small class="text-muted">{{ $booking->approved_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @endif
                        @if($booking->updated_at != $booking->created_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Terakhir Diupdate</h6>
                                <small class="text-muted">{{ $booking->updated_at->format('d F Y, H:i') }} WIB</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -40px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -35px;
    top: 10px;
    bottom: 10px;
    width: 2px;
    background-color: #e2e8f0;
}

.facility-checkbox {
    margin-bottom: 0.5rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roomSelect = document.getElementById('meeting_room_id');
    const attendeesInput = document.getElementById('attendees_count');
    const statusSelect = document.getElementById('status');
    const rejectionContainer = document.getElementById('rejectionReasonContainer');
    const facilitiesContainer = document.getElementById('facilitiesContainer');
    
    // Current booking facilities
    const currentFacilities = @json($booking->required_facilities ? (is_string($booking->required_facilities) ? json_decode($booking->required_facilities, true) : $booking->required_facilities) : []);
    
    // Handle status change
    statusSelect.addEventListener('change', function() {
        if (this.value === 'rejected') {
            rejectionContainer.style.display = 'block';
        } else {
            rejectionContainer.style.display = 'none';
        }
    });
    
    // Handle room change
    roomSelect.addEventListener('change', function() {
        updateRoomInfo();
        updateFacilities();
        checkCapacity();
    });
    
    // Handle attendees change
    attendeesInput.addEventListener('input', function() {
        checkCapacity();
    });
    
    function updateRoomInfo() {
        const selectedOption = roomSelect.options[roomSelect.selectedIndex];
        if (selectedOption.value) {
            const capacity = selectedOption.dataset.capacity;
            const roomName = selectedOption.textContent.split(' (')[0];
            
            document.getElementById('roomInfo').innerHTML = `
                <div class="text-center">
                    <h5>${roomName}</h5>
                    <div class="row text-center">
                        <div class="col-12">
                            <h6>${capacity}</h6>
                            <small class="text-muted">Kapasitas</small>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    function updateFacilities() {
        const selectedOption = roomSelect.options[roomSelect.selectedIndex];
        if (selectedOption.value) {
            const facilities = JSON.parse(selectedOption.dataset.facilities || '[]');
            let facilitiesHtml = '';
            
            if (facilities.length > 0) {
                facilities.forEach(facility => {
                    const isChecked = currentFacilities.includes(facility);
                    facilitiesHtml += `
                        <div class="form-check facility-checkbox">
                            <input class="form-check-input" type="checkbox" name="required_facilities[]" 
                                   value="${facility}" id="facility_${facility.replace(/\s+/g, '_')}"
                                   ${isChecked ? 'checked' : ''}>
                            <label class="form-check-label" for="facility_${facility.replace(/\s+/g, '_')}">
                                ${facility}
                            </label>
                        </div>
                    `;
                });
            } else {
                facilitiesHtml = '<p class="text-muted mb-0">Tidak ada fasilitas khusus tersedia</p>';
            }
            
            facilitiesContainer.innerHTML = facilitiesHtml;
        } else {
            facilitiesContainer.innerHTML = '<p class="text-muted mb-0">Pilih ruang terlebih dahulu</p>';
        }
    }
    
    function checkCapacity() {
        const selectedOption = roomSelect.options[roomSelect.selectedIndex];
        const attendees = parseInt(attendeesInput.value);
        const warningDiv = document.getElementById('capacityWarning');
        
        if (selectedOption.value && attendees) {
            const capacity = parseInt(selectedOption.dataset.capacity);
            
            if (attendees > capacity) {
                warningDiv.innerHTML = '<span class="text-danger">⚠️ Jumlah peserta melebihi kapasitas ruangan!</span>';
                warningDiv.className = 'form-text text-danger';
            } else {
                warningDiv.innerHTML = `<span class="text-success">✓ Kapasitas mencukupi (${capacity - attendees} tempat tersisa)</span>`;
                warningDiv.className = 'form-text text-success';
            }
        } else {
            warningDiv.innerHTML = '';
        }
    }
    
    // Initialize
    if (statusSelect.value === 'rejected') {
        rejectionContainer.style.display = 'block';
    }
    updateFacilities();
    checkCapacity();
    
    // Form validation
    document.getElementById('bookingForm').addEventListener('submit', function(e) {
        const startTime = new Date(document.getElementById('start_time').value);
        const endTime = new Date(document.getElementById('end_time').value);
        
        if (endTime <= startTime) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Waktu selesai harus lebih besar dari waktu mulai'
            });
            return false;
        }
        
        if (startTime < new Date()) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Waktu mulai tidak boleh di masa lalu'
            });
            return false;
        }
    });
});
</script>
@endpush
