<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::create([
            'name' => 'Administrator',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin123!'),
            'phone' => '081234567890',
            'department' => 'IT',
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => Carbon::now(),
        ]);

        // Create Regular Users
        $users = [
            [
                'name' => 'Budi <PERSON>o',
                'username' => 'budi.santoso',
                'email' => '<EMAIL>',
                'password' => Hash::make('User123!'),
                'phone' => '081234567891',
                'department' => 'Marketing',
                'role' => 'user',
            ],
            [
                'name' => 'Siti Nurhaliza',
                'username' => 'siti.nurhaliza',
                'email' => '<EMAIL>',
                'password' => Hash::make('User123!'),
                'phone' => '081234567892',
                'department' => 'HR',
                'role' => 'user',
            ],
            [
                'name' => 'Andi Wijaya',
                'username' => 'andi.wijaya',
                'email' => '<EMAIL>',
                'password' => Hash::make('User123!'),
                'phone' => '081234567893',
                'department' => 'Finance',
                'role' => 'user',
            ],
            [
                'name' => 'Maya Sari',
                'username' => 'maya.sari',
                'email' => '<EMAIL>',
                'password' => Hash::make('User123!'),
                'phone' => '081234567894',
                'department' => 'Operations',
                'role' => 'user',
            ],
            [
                'name' => 'Doni Hermawan',
                'username' => 'doni.hermawan',
                'email' => '<EMAIL>',
                'password' => Hash::make('User123!'),
                'phone' => '081234567895',
                'department' => 'IT',
                'role' => 'user',
            ],
        ];

        foreach ($users as $userData) {
            User::create(array_merge($userData, [
                'is_active' => true,
                'email_verified_at' => Carbon::now(),
            ]));
        }

        $this->command->info('Created ' . (count($users) + 1) . ' users (1 admin, ' . count($users) . ' regular users)');
    }
}
