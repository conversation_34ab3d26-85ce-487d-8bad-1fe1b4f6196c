@extends('layouts.dashboard')

@section('title', 'Edit Ruang Rapat - Admin')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">Edit Ruang Rapat</h1>
                <p class="text-muted">Edit informasi ruang rapat: {{ $room->name }}</p>
            </div>
            <div>
                <a href="{{ route('admin.rooms.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-door-closed"></i> Informasi Ruangan
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.rooms.update', $room->id) }}" method="POST" enctype="multipart/form-data" id="roomForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Ruangan <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $room->name) }}" 
                                           placeholder="Masukkan nama ruangan" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Lokasi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                           id="location" name="location" value="{{ old('location', $room->location) }}" 
                                           placeholder="Masukkan lokasi ruangan" required>
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                           id="capacity" name="capacity" value="{{ old('capacity', $room->capacity) }}" 
                                           min="1" max="1000" placeholder="Jumlah orang" required>
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hourly_rate" class="form-label">Tarif per Jam</label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp</span>
                                        <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror" 
                                               id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate', $room->hourly_rate) }}" 
                                               min="0" step="1000" placeholder="0">
                                    </div>
                                    @error('hourly_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Kosongkan jika gratis</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi ruangan (opsional)">{{ old('description', $room->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fasilitas</label>
                            <div class="row">
                                @forelse($facilities as $facility)
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="facilities[]" value="{{ $facility->id }}" id="facility{{ $facility->id }}" @if(in_array($facility->id, $selectedFacilities)) checked @endif>
                                            <label class="form-check-label" for="facility{{ $facility->id }}">
                                                @if($facility->icon)<i class="bi {{ $facility->icon }}"></i> @endif
                                                {{ $facility->name }}
                                            </label>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12 text-muted">Belum ada fasilitas. <a href='{{ route('admin.facilities.create') }}'>Tambah Fasilitas</a></div>
                                @endforelse
                            </div>
                            <a href="{{ route('admin.facilities.index') }}" class="btn btn-link p-0 mt-2">+ Kelola Fasilitas</a>
                            @error('facilities')
                                <div class="text-danger small">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_active" 
                                       id="is_active" value="1" {{ old('is_active', $room->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Aktifkan ruangan
                                </label>
                            </div>
                            <small class="form-text text-muted">Ruangan yang tidak aktif tidak akan muncul dalam daftar pemesanan</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.rooms.index') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Ruangan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-eye"></i> Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="room-preview">
                        <h6 id="preview-name">{{ $room->name }}</h6>
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt"></i> <span id="preview-location">{{ $room->location }}</span>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-people"></i> <span id="preview-capacity">{{ $room->capacity }}</span> orang
                        </p>
                        @if($room->hourly_rate)
                        <p class="text-muted mb-2">
                            <i class="bi bi-currency-dollar"></i> Rp <span id="preview-rate">{{ number_format($room->hourly_rate, 0, ',', '.') }}</span>/jam
                        </p>
                        @endif
                        <div class="mt-3">
                            <h6>Fasilitas:</h6>
                            <div id="preview-facilities">
                                @php
                                    $facilitiesArray = $room->facilities;
                                    if (is_string($facilitiesArray)) {
                                        $facilitiesArray = json_decode($facilitiesArray, true) ?? [];
                                    }
                                    if (!is_array($facilitiesArray)) {
                                        $facilitiesArray = [];
                                    }
                                @endphp
                                @if($facilitiesArray && count($facilitiesArray) > 0)
                                    @foreach($facilitiesArray as $facility)
                                        <span class="badge bg-info me-1">{{ ucfirst(str_replace('_', ' ', $facility)) }}</span>
                                    @endforeach
                                @else
                                    <span class="text-muted">Tidak ada fasilitas</span>
                                @endif
                            </div>
                        </div>
                        @if($room->description)
                        <div class="mt-3">
                            <h6>Deskripsi:</h6>
                            <p class="text-muted" id="preview-description">{{ $room->description }}</p>
                        </div>
                        @endif
                        <div class="mt-3">
                            <span class="badge {{ $room->is_active ? 'bg-success' : 'bg-danger' }}" id="preview-status">
                                {{ $room->is_active ? 'Aktif' : 'Tidak Aktif' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card shadow mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i> Statistik
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item">
                        <span class="stat-label">Total Pemesanan:</span>
                        <span class="stat-value">{{ $room->bookings()->count() }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Bulan Ini:</span>
                        <span class="stat-value">{{ $room->bookings()->whereMonth('start_time', now()->month)->count() }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Pending:</span>
                        <span class="stat-value">{{ $room->bookings()->where('status', 'pending')->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.room-preview {
    font-size: 14px;
}

.stat-item {
    display: flex;
    justify-content: between;
    margin-bottom: 8px;
}

.stat-label {
    font-weight: 500;
    flex: 1;
}

.stat-value {
    font-weight: 600;
    color: var(--primary-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

#preview-facilities .badge {
    margin-bottom: 3px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const nameInput = document.getElementById('name');
    const locationInput = document.getElementById('location');
    const capacityInput = document.getElementById('capacity');
    const hourlyRateInput = document.getElementById('hourly_rate');
    const descriptionInput = document.getElementById('description');
    const activeCheckbox = document.getElementById('is_active');
    const facilityCheckboxes = document.querySelectorAll('input[name="facilities[]"]');
    
    // Update preview on input changes
    nameInput.addEventListener('input', () => {
        document.getElementById('preview-name').textContent = nameInput.value || 'Nama Ruangan';
    });
    
    locationInput.addEventListener('input', () => {
        document.getElementById('preview-location').textContent = locationInput.value || 'Lokasi';
    });
    
    capacityInput.addEventListener('input', () => {
        document.getElementById('preview-capacity').textContent = capacityInput.value || '0';
    });
    
    hourlyRateInput.addEventListener('input', () => {
        const rate = parseInt(hourlyRateInput.value) || 0;
        const rateElement = document.getElementById('preview-rate');
        if (rateElement) {
            rateElement.textContent = rate.toLocaleString('id-ID');
        }
    });
    
    descriptionInput.addEventListener('input', () => {
        document.getElementById('preview-description').textContent = descriptionInput.value || 'Tidak ada deskripsi';
    });
    
    activeCheckbox.addEventListener('change', () => {
        const statusElement = document.getElementById('preview-status');
        statusElement.textContent = activeCheckbox.checked ? 'Aktif' : 'Tidak Aktif';
        statusElement.className = activeCheckbox.checked ? 'badge bg-success' : 'badge bg-danger';
    });
    
    // Update facilities preview
    function updateFacilitiesPreview() {
        const selectedFacilities = Array.from(facilityCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value.replace('_', ' '));
            
        const facilitiesContainer = document.getElementById('preview-facilities');
        
        if (selectedFacilities.length > 0) {
            facilitiesContainer.innerHTML = selectedFacilities
                .map(facility => `<span class="badge bg-info me-1">${facility}</span>`)
                .join('');
        } else {
            facilitiesContainer.innerHTML = '<span class="text-muted">Tidak ada fasilitas</span>';
        }
    }
    
    facilityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateFacilitiesPreview);
    });
    
    // Form validation
    document.getElementById('roomForm').addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        const location = locationInput.value.trim();
        const capacity = parseInt(capacityInput.value);
        
        if (!name || !location || !capacity || capacity < 1) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Data Tidak Lengkap',
                text: 'Mohon lengkapi semua field yang wajib diisi!'
            });
            return;
        }
        
        // Show loading
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Menyimpan...';
        submitBtn.disabled = true;
    });
});

// Show success message if redirected from update
@if(session('success'))
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: '{{ session('success') }}',
        timer: 3000,
        showConfirmButton: false
    });
@endif

// Show error message if any
@if(session('error'))
    Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: '{{ session('error') }}'
    });
@endif
</script>
@endpush
@endsection
