<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\MeetingRoomController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\BookingManagementController;
use App\Http\Controllers\Admin\SecurityLogController;
use App\Http\Controllers\Admin\RoomManagementController;
use App\Http\Controllers\ThemeController;

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/

// Redirect root to login
Route::get('/', function () {
    return redirect()->route('login');
});

// Guest routes (not authenticated)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/password/email', [AuthController::class, 'sendPasswordResetEmail'])->name('password.email');
    Route::get('/email/verify/{token}', [AuthController::class, 'verifyEmail'])->name('verification.verify');
    Route::get('/password/reset/{token}', [AuthController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [AuthController::class, 'resetPassword'])->name('password.update');
    // Allow guests to request verification emails
    Route::post('/email/verification-notification', [AuthController::class, 'resendVerificationEmail'])->name('verification.send');
    Route::post('/email/resend-verification', [AuthController::class, 'resendVerificationEmail'])->name('verification.resend');
});

// Email verification routes (accessible when authenticated but not verified)
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [AuthController::class, 'showVerificationNotice'])->name('verification.notice');
});

/*
|--------------------------------------------------------------------------
| Authenticated Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['auth', 'verified', 'prevent.double.login'])->group(function () {
    
    // Debug route untuk test notification
    Route::get('/test-notification', function() {
        if (auth()->user()->isAdmin()) {
            \App\Services\NotificationService::createForUser(
                auth()->id(),
                'test',
                'Test Notification',
                'Ini adalah test notification untuk debug'
            );
            return 'Test notification sent!';
        }
        return 'Only admin can test';
    })->name('test.notification');
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    
    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/', [ProfileController::class, 'update'])->name('update');
        Route::put('/password', [ProfileController::class, 'updatePassword'])->name('password.update');
    });
    
    // Theme Management
    Route::post('/theme/update', [ThemeController::class, 'updateTheme'])->name('theme.update');
    Route::get('/theme/debug', [ThemeController::class, 'debugTheme'])->name('theme.debug');
    
    // Meeting Rooms
    Route::prefix('rooms')->name('rooms.')->group(function () {
        Route::get('/', [MeetingRoomController::class, 'index'])->name('index');
        Route::get('/{room}', [MeetingRoomController::class, 'show'])->name('show');
        Route::get('/{room}/availability', [MeetingRoomController::class, 'checkAvailability'])->name('availability');
        
        // Admin only routes for room management
        Route::middleware('admin')->group(function () {
            Route::get('/create', [MeetingRoomController::class, 'create'])->name('create');
            Route::post('/', [MeetingRoomController::class, 'store'])->name('store');
            Route::get('/{room}/edit', [MeetingRoomController::class, 'edit'])->name('edit');
            Route::put('/{room}', [MeetingRoomController::class, 'update'])->name('update');
            Route::delete('/{room}', [MeetingRoomController::class, 'destroy'])->name('destroy');
        });
    });
    
    // Bookings
    Route::prefix('bookings')->name('bookings.')->group(function () {
        Route::get('/', [BookingController::class, 'index'])->name('index');
        Route::get('/create', [BookingController::class, 'create'])->name('create');
        Route::post('/', [BookingController::class, 'store'])->name('store');
        Route::get('/{booking}', [BookingController::class, 'show'])->name('show');
        Route::get('/{booking}/edit', [BookingController::class, 'edit'])->name('edit');
        Route::put('/{booking}', [BookingController::class, 'update'])->name('update');
        Route::delete('/{booking}', [BookingController::class, 'destroy'])->name('destroy');
        Route::post('/{booking}/cancel', [BookingController::class, 'cancel'])->name('cancel');
        
        // API route for conflict checking
        Route::post('/check-conflict', [BookingController::class, 'checkConflict'])->name('check-conflict');
        
        // Admin approval routes
        Route::middleware('admin')->group(function () {
            Route::post('/{booking}/approve', [BookingController::class, 'approve'])->name('approve');
            Route::post('/{booking}/reject', [BookingController::class, 'reject'])->name('reject');
        });
    });
    
    // Calendar API routes
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/calendar/events', [BookingController::class, 'getCalendarEvents'])->name('calendar.events');
        Route::get('/rooms/{room}/schedule', [MeetingRoomController::class, 'getSchedule'])->name('rooms.schedule');
    });
    
    // Admin routes
    Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
        // Admin Dashboard
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard/data', [AdminController::class, 'getDashboardData'])->name('dashboard.data');
        Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
        Route::get('/reports/data', [AdminController::class, 'getBookingStats'])->name('reports.data');
        
        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [UserManagementController::class, 'index'])->name('index');
            Route::get('/create', [UserManagementController::class, 'create'])->name('create');
            Route::post('/', [UserManagementController::class, 'store'])->name('store');
            Route::get('/{user}', [UserManagementController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [UserManagementController::class, 'edit'])->name('edit');
            Route::put('/{user}', [UserManagementController::class, 'update'])->name('update');
            Route::delete('/{user}', [UserManagementController::class, 'destroy'])->name('destroy');
            Route::match(['POST', 'PATCH'], '/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{user}/lock', [UserManagementController::class, 'lock'])->name('lock');
            Route::post('/{user}/unlock', [UserManagementController::class, 'unlock'])->name('unlock');
            Route::post('/{user}/reset-password', [UserManagementController::class, 'resetPassword'])->name('reset-password');
        });
        
        // Room Management
        Route::prefix('rooms')->name('rooms.')->group(function () {
            Route::get('/', [RoomManagementController::class, 'index'])->name('index');
            Route::get('/create', [RoomManagementController::class, 'create'])->name('create');
            Route::post('/', [RoomManagementController::class, 'store'])->name('store');
            Route::get('/{room}', [RoomManagementController::class, 'show'])->name('show');
            Route::get('/{room}/edit', [RoomManagementController::class, 'edit'])->name('edit');
            Route::put('/{room}', [RoomManagementController::class, 'update'])->name('update');
            Route::delete('/{room}', [RoomManagementController::class, 'destroy'])->name('destroy');
            Route::match(['POST', 'PATCH'], '/{room}/toggle-status', [RoomManagementController::class, 'toggleStatus'])->name('toggle-status');
        });
        
        // Booking Management
        Route::prefix('bookings')->name('bookings.')->group(function () {
            Route::get('/', [BookingManagementController::class, 'index'])->name('index');
            Route::get('/{booking}/edit', [BookingManagementController::class, 'edit'])->name('edit');
            Route::get('/{booking}', [BookingManagementController::class, 'show'])->name('show');
            Route::put('/{booking}', [BookingManagementController::class, 'update'])->name('update');
            Route::patch('/{booking}/approve', [BookingManagementController::class, 'approve'])->name('approve');
            Route::patch('/{booking}/reject', [BookingManagementController::class, 'reject'])->name('reject');
            Route::patch('/{booking}/change-status', [BookingManagementController::class, 'changeStatus'])->name('change-status');
            Route::delete('/{booking}', [BookingManagementController::class, 'destroy'])->name('destroy');
        });
        
        // Security Logs
        Route::prefix('security')->name('security.')->group(function () {
            Route::get('/logs', [SecurityLogController::class, 'index'])->name('logs');
            Route::get('/logs/data', [SecurityLogController::class, 'getStatsData'])->name('logs.data');
            Route::get('/logs/{log}', [SecurityLogController::class, 'show'])->name('logs.show');
            Route::delete('/logs/{log}', [SecurityLogController::class, 'destroy'])->name('logs.destroy');
            Route::post('/logs/cleanup', [SecurityLogController::class, 'cleanup'])->name('logs.cleanup');
        });
        
        // Security management routes
        Route::prefix('security')->name('security.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SecurityController::class, 'index'])->name('index');
            Route::post('/whitelist/add', [App\Http\Controllers\Admin\SecurityController::class, 'addToWhitelist'])->name('whitelist.add');
            Route::delete('/whitelist/remove', [App\Http\Controllers\Admin\SecurityController::class, 'removeFromWhitelist'])->name('whitelist.remove');
            Route::post('/cache/clear', [App\Http\Controllers\Admin\SecurityController::class, 'clearCache'])->name('cache.clear');
            Route::post('/mode/toggle', [App\Http\Controllers\Admin\SecurityController::class, 'toggleSecurityMode'])->name('mode.toggle');
        });
        
        // Facility Management
        Route::resource('facilities', \App\Http\Controllers\Admin\FacilityController::class);
    });
});

// Logout - accessible even without email verification
Route::middleware('auth')->post('/logout', [AuthController::class, 'logout'])->name('logout');

/*
|--------------------------------------------------------------------------
| API Routes for AJAX calls
|--------------------------------------------------------------------------
*/

// Notification API routes
Route::prefix('api')->middleware(['auth', 'verified'])->group(function () {
    Route::get('/notifications', [App\Http\Controllers\Api\NotificationController::class, 'index']);
    Route::post('/notifications/{id}/read', [App\Http\Controllers\Api\NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-read', [App\Http\Controllers\Api\NotificationController::class, 'markAllAsRead']);
    Route::get('/notifications/unread-count', [App\Http\Controllers\Api\NotificationController::class, 'getUnreadCount']);
    Route::delete('/notifications/{id}', [App\Http\Controllers\Api\NotificationController::class, 'destroy']);
});

/*
|--------------------------------------------------------------------------
| API Routes for Real-time Updates
|--------------------------------------------------------------------------
*/

Route::prefix('api/v1')->middleware(['auth:sanctum', 'security'])->group(function () {
    Route::get('/realtime/bookings', [BookingController::class, 'getRealtimeBookings']);
    Route::get('/realtime/rooms', [MeetingRoomController::class, 'getRealtimeRooms']);
    
    // Routes untuk testing real-time features (development only - remove in production)
    if (config('app.debug')) {
        Route::get('/test-pusher', function () {
            $booking = \App\Models\Booking::with(['user', 'meetingRoom'])->first();
            if ($booking) {
                event(new \App\Events\BookingCreated($booking));
                return response()->json([
                    'success' => true,
                    'message' => 'BookingCreated event dispatched!',
                    'booking' => $booking->title
                ]);
            }
            return response()->json([
                'success' => false,
                'message' => 'No bookings found in database'
            ]);
        })->name('test.pusher');

        Route::get('/test-status-update', function () {
            $booking = \App\Models\Booking::with(['user', 'meetingRoom'])->first();
            if ($booking) {
                event(new \App\Events\BookingStatusUpdated($booking));
                return response()->json([
                    'success' => true,
                    'message' => 'BookingStatusUpdated event dispatched!',
                    'booking' => $booking->title,
                    'status' => $booking->status
                ]);
            }
            return response()->json([
                'success' => false,
                'message' => 'No bookings found in database'
            ]);
        })->name('test.status.update');
    }
});

// Secure file access routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/secure/file/{path}', [App\Http\Controllers\SecureFileController::class, 'show'])->name('secure.file');
    Route::get('/secure/download/{path}', [App\Http\Controllers\SecureFileController::class, 'download'])->name('secure.download');
});
