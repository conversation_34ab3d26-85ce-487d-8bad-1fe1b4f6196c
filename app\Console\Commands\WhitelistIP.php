<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class WhitelistIP extends Command
{
    protected $signature = 'security:whitelist-ip {ip} {--remove}';
    protected $description = 'Add or remove IP from security whitelist';

    public function handle()
    {
        $ip = $this->argument('ip');
        $remove = $this->option('remove');

        // Get current whitelist from cache or create new
        $whitelist = Cache::get('security_ip_whitelist', [
            '127.0.0.1',
            '::1',
            'localhost'
        ]);

        if ($remove) {
            // Remove IP from whitelist
            $whitelist = array_filter($whitelist, function($whitelistIP) use ($ip) {
                return $whitelistIP !== $ip;
            });
            
            Cache::put('security_ip_whitelist', array_values($whitelist), 86400); // 24 hours
            
            $this->info("IP {$ip} telah dihapus dari whitelist.");
        } else {
            // Add IP to whitelist
            if (!in_array($ip, $whitelist)) {
                $whitelist[] = $ip;
                Cache::put('security_ip_whitelist', $whitelist, 86400); // 24 hours
                
                $this->info("IP {$ip} telah ditambahkan ke whitelist.");
            } else {
                $this->warn("IP {$ip} sudah ada dalam whitelist.");
            }
        }

        // Show current whitelist
        $this->info('Current IP Whitelist:');
        foreach ($whitelist as $whitelistIP) {
            $this->line("- {$whitelistIP}");
        }

        // Clear any existing blocks for this IP
        if (!$remove) {
            Cache::forget("ip_banned:{$ip}");
            Cache::forget("ip_blocked:{$ip}");
            $this->info("Cleared any existing blocks for IP {$ip}.");
        }
    }
}
