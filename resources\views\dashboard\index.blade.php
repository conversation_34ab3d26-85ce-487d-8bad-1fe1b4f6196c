@extends('layouts.dashboard')

@section('title', 'Dashboard - Sistem Pemesanan Ruang Rapat')

@section('content')
<div class="dashboard-container">
    <!-- Welcome Section -->
    <div class="welcome-section" id="welcome-section">
        <div class="welcome-content">
            <h1 class="welcome-title">
                <span class="greeting" id="greeting"></span>{{ auth()->user()->name }}! 👋
            </h1>
            <p class="welcome-subtitle">
                Selamat datang kembali di Sistem Pemesanan Ruang Rapat. 
                Hari ini adalah {{ Carbon\Carbon::now()->translatedFormat('l, d F Y') }}.
            </p>
        </div>
        <div class="welcome-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-calendar-check"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" data-stat="upcomingBookingsCount">{{ $upcomingBookings->count() }}</div>
                    <div class="stat-label">J<PERSON><PERSON> Mendatang</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" data-stat="pendingBookings">{{ $pendingBookings }}</div>
                    <div class="stat-label">Menunggu Persetujuan</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-door-open"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" data-stat="availableRooms">{{ $availableRooms }}</div>
                    <div class="stat-label">Ruang Tersedia</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h2 class="section-title">
            <i class="bi bi-lightning-charge"></i>
            Aksi Cepat
        </h2>
        <div class="action-grid">
            <a href="{{ route('bookings.create') }}" class="action-card action-primary">
                <div class="action-icon">
                    <i class="bi bi-plus-circle"></i>
                </div>
                <div class="action-content">
                    <h3>Buat Pemesanan</h3>
                    <p>Pesan ruang rapat untuk kebutuhan Anda</p>
                </div>
            </a>
            <a href="{{ route('rooms.index') }}" class="action-card action-secondary">
                <div class="action-icon">
                    <i class="bi bi-search"></i>
                </div>
                <div class="action-content">
                    <h3>Cari Ruangan</h3>
                    <p>Lihat ketersediaan ruang rapat</p>
                </div>
            </a>
            <a href="{{ route('bookings.index') }}" class="action-card action-info">
                <div class="action-icon">
                    <i class="bi bi-list-check"></i>
                </div>
                <div class="action-content">
                    <h3>Riwayat Booking</h3>
                    <p>Kelola pemesanan Anda</p>
                </div>
            </a>
            <a href="{{ route('profile.show') }}" class="action-card action-warning">
                <div class="action-icon">
                    <i class="bi bi-person-gear"></i>
                </div>
                <div class="action-content">
                    <h3>Pengaturan</h3>
                    <p>Update profil dan preferensi</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Today's Schedule -->
    @if($todayBookings->count() > 0)
    <div class="today-schedule">
        <h2 class="section-title">
            <i class="bi bi-calendar-day"></i>
            Jadwal Hari Ini
        </h2>
        <div class="schedule-list">
            @foreach($todayBookings as $booking)
            <div class="schedule-item status-{{ $booking->status }}">
                <div class="schedule-time">
                    <div class="time-start">{{ $booking->start_time->format('H:i') }}</div>
                    <div class="time-separator">-</div>
                    <div class="time-end">{{ $booking->end_time->format('H:i') }}</div>
                </div>
                <div class="schedule-content">
                    <h4 class="schedule-title">{{ $booking->title }}</h4>
                    <p class="schedule-room">
                        <i class="bi bi-geo-alt"></i>
                        {{ $booking->meetingRoom->name }}
                    </p>
                    <p class="schedule-attendees">
                        <i class="bi bi-people"></i>
                        {{ $booking->attendees_count }} peserta
                    </p>
                </div>
                <div class="schedule-status">
                    <span class="status-badge status-{{ $booking->status }}">
                        @switch($booking->status)
                            @case('approved')
                                <i class="bi bi-check-circle"></i> Disetujui
                                @break
                            @case('pending')
                                <i class="bi bi-clock"></i> Menunggu
                                @break
                            @case('rejected')
                                <i class="bi bi-x-circle"></i> Ditolak
                                @break
                            @default
                                {{ $booking->status }}
                        @endswitch
                    </span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Grid Layout for Cards -->
    <div class="dashboard-grid">
        <!-- Recent Bookings -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-clock-history"></i>
                    Pemesanan Terbaru
                </h3>
                <a href="{{ route('bookings.index') }}" class="card-action">
                    Lihat Semua <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-content">
                @if($userBookings->count() > 0)
                    <div class="booking-list">
                        @foreach($userBookings as $booking)
                        <div class="booking-item">
                            <div class="booking-info">
                                <h4 class="booking-title">{{ $booking->title }}</h4>
                                <p class="booking-details">
                                    <i class="bi bi-geo-alt"></i>
                                    {{ $booking->meetingRoom->name }}
                                </p>
                                <p class="booking-time">
                                    <i class="bi bi-calendar"></i>
                                    {{ $booking->start_time->format('d/m/Y H:i') }}
                                </p>
                            </div>
                            <div class="booking-status">
                                <span class="status-badge status-{{ $booking->status }}">
                                    {{ $booking->status }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="bi bi-calendar-x"></i>
                        <h4>Belum ada pemesanan</h4>
                        <p>Mulai dengan membuat pemesanan ruang rapat pertama Anda</p>
                        <a href="{{ route('bookings.create') }}" class="btn btn-primary">
                            Buat Pemesanan
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-calendar-event"></i>
                    Jadwal Mendatang
                </h3>
            </div>
            <div class="card-content">
                @if($upcomingBookings->count() > 0)
                    <div class="upcoming-list">
                        @foreach($upcomingBookings as $booking)
                        <div class="upcoming-item">
                            <div class="upcoming-date">
                                <div class="date-day">{{ $booking->start_time->format('d') }}</div>
                                <div class="date-month">{{ $booking->start_time->format('M') }}</div>
                            </div>
                            <div class="upcoming-info">
                                <h4 class="upcoming-title">{{ $booking->title }}</h4>
                                <p class="upcoming-time">
                                    {{ $booking->start_time->format('H:i') }} - {{ $booking->end_time->format('H:i') }}
                                </p>
                                <p class="upcoming-room">{{ $booking->meetingRoom->name }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="bi bi-calendar-plus"></i>
                        <h4>Tidak ada jadwal mendatang</h4>
                        <p>Semua pemesanan Anda sudah selesai atau belum ada</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Activity Log -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-activity"></i>
                    Aktivitas Terbaru
                </h3>
            </div>
            <div class="card-content">
                @if($recentActivity->count() > 0)
                    <div class="activity-list">
                        @foreach($recentActivity as $activity)
                        <div class="activity-item">
                            <div class="activity-icon severity-{{ $activity->severity }}">
                                @switch($activity->action)
                                    @case('successful_login')
                                        <i class="bi bi-box-arrow-in-right"></i>
                                        @break
                                    @case('user_logout')
                                        <i class="bi bi-box-arrow-right"></i>
                                        @break
                                    @case('email_verified')
                                        <i class="bi bi-envelope-check"></i>
                                        @break
                                    @default
                                        <i class="bi bi-info-circle"></i>
                                @endswitch
                            </div>
                            <div class="activity-content">
                                <p class="activity-description">
                                    @switch($activity->action)
                                        @case('successful_login')
                                            Login berhasil
                                            @break
                                        @case('user_logout')
                                            Logout dari sistem
                                            @break
                                        @case('email_verified')
                                            Email berhasil diverifikasi
                                            @break
                                        @default
                                            {{ $activity->action }}
                                    @endswitch
                                </p>
                                <p class="activity-time">{{ $activity->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="bi bi-shield-check"></i>
                        <h4>Tidak ada aktivitas</h4>
                        <p>Log aktivitas akan muncul di sini</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Admin Panel -->
        @if(auth()->user()->isAdmin() && !empty($adminStats))
        <div class="dashboard-card admin-panel">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-shield-shaded"></i>
                    Panel Administrator
                </h3>
                <a href="{{ route('admin.dashboard') }}" class="card-action">
                    Dashboard Admin <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-content">
                <div class="admin-stats">
                    <div class="admin-stat">
                        <div class="stat-value">{{ $adminStats['pending_approvals'] }}</div>
                        <div class="stat-label">Perlu Persetujuan</div>
                    </div>
                    <div class="admin-stat">
                        <div class="stat-value">{{ $adminStats['today_bookings'] }}</div>
                        <div class="stat-label">Booking Hari Ini</div>
                    </div>
                    <div class="admin-stat">
                        <div class="stat-value">{{ $adminStats['security_alerts'] }}</div>
                        <div class="stat-label">Alert Keamanan</div>
                    </div>
                    <div class="admin-stat">
                        <div class="stat-value">{{ $adminStats['active_users'] }}</div>
                        <div class="stat-label">User Aktif</div>
                    </div>
                </div>
                
                @if($adminStats['pending_approvals'] > 0)
                <div class="admin-alerts">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        Ada {{ $adminStats['pending_approvals'] }} pemesanan yang menunggu persetujuan Anda.
                        <a href="{{ route('admin.dashboard') }}" class="alert-link">Lihat sekarang</a>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<!-- Pusher Scripts -->
<script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
<script>
    // Initialize Pusher
    console.log('Initializing Pusher...');
    const pusher = new Pusher('{{ config("broadcasting.connections.pusher.key") }}', {
        cluster: '{{ config("broadcasting.connections.pusher.options.cluster") }}',
        encrypted: true
    });

    // Debug Pusher connection
    pusher.connection.bind('connected', function() {
        console.log('Pusher connected successfully!');
    });

    pusher.connection.bind('failed', function() {
        console.error('Pusher connection failed!');
    });

    pusher.connection.bind('disconnected', function() {
        console.log('Pusher disconnected');
    });

    // Subscribe to booking channels
    console.log('Subscribing to bookings channel...');
    const bookingChannel = pusher.subscribe('bookings');
    
    bookingChannel.bind('pusher:subscription_succeeded', function() {
        console.log('Successfully subscribed to bookings channel!');
    });

    bookingChannel.bind('pusher:subscription_error', function(status) {
        console.error('Subscription error:', status);
    });
    
    // Note: Private channel requires authentication, for now we'll use public channel

    // Listen for new bookings
    bookingChannel.bind('booking.created', function(data) {
        console.log('New booking created:', data);
        
        // Show notification
        if (window.Toast) {
            Toast.fire({
                icon: 'info',
                title: `Pemesanan baru: ${data.booking.title}`,
                text: `Oleh: ${data.booking.user.name}`
            });
        }

        // Update dashboard data
        updateDashboardData();
    });

    // Listen for booking status updates (we'll check if it's for current user)
    bookingChannel.bind('booking.status.updated', function(data) {
        console.log('Booking status updated:', data);
        
        // Only show notification if booking belongs to current user
        if (data.booking.user.id == {{ auth()->id() }}) {
            const booking = data.booking;
            let statusText = '';
            let iconType = 'info';
            
            switch(booking.status) {
                case 'approved':
                    statusText = 'disetujui';
                    iconType = 'success';
                    break;
                case 'rejected':
                    statusText = 'ditolak';
                    iconType = 'error';
                    break;
                default:
                    statusText = booking.status;
            }

            // Show notification
            if (window.Toast) {
                Toast.fire({
                    icon: iconType,
                    title: `Pemesanan ${statusText}`,
                    text: `${booking.title} telah ${statusText}`
                });
            }

            // Update the page content
            updateDashboardData();
        }
    });

    // Function to update dashboard data
    function updateDashboardData() {
        fetch('/dashboard/data', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            // Update stats
            updateStatNumber('upcomingBookingsCount', data.upcomingBookingsCount || data.upcomingBookings?.length || 0);
            updateStatNumber('pendingBookings', data.pendingBookings);
            updateStatNumber('availableRooms', data.availableRooms);

            // Update upcoming bookings list
            updateUpcomingBookings(data.upcomingBookings);

            // Update recent bookings list  
            updateRecentBookings(data.userBookings);

            // Update today's schedule
            updateTodaySchedule(data.todayBookings);
        })
        .catch(error => {
            console.error('Error updating dashboard:', error);
        });
    }

    // Helper function to update stat numbers with animation
    function updateStatNumber(statName, newValue) {
        const statElements = document.querySelectorAll('[data-stat="' + statName + '"]');
        if (statElements.length === 0) {
            // Fallback: find by class or content
            const allStats = document.querySelectorAll('.stat-number');
            allStats.forEach(stat => {
                const parent = stat.closest('.stat-card');
                if (parent) {
                    const title = parent.querySelector('.stat-title')?.textContent?.toLowerCase();
                    if ((statName === 'upcomingBookingsCount' && title?.includes('upcoming')) ||
                        (statName === 'pendingBookings' && title?.includes('pending')) ||
                        (statName === 'availableRooms' && title?.includes('ruang'))) {
                        animateStatUpdate(stat, newValue);
                    }
                }
            });
            return;
        }
        
        statElements.forEach(stat => animateStatUpdate(stat, newValue));
    }

    function animateStatUpdate(element, newValue) {
        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.1)';
        element.textContent = newValue;
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 300);
    }

    // Update upcoming bookings
    function updateUpcomingBookings(bookings) {
        const container = document.querySelector('.upcoming-list');
        if (!container) return;

        if (!bookings || bookings.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">Tidak ada rapat yang akan datang</p>';
            return;
        }

        container.innerHTML = bookings.map(booking => `
            <div class="upcoming-item" data-booking-id="${booking.id}">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">${booking.title}</h4>
                        <p class="text-sm text-gray-600">${booking.meeting_room?.name || booking.room?.name}</p>
                        <p class="text-xs text-gray-500">${formatDateTime(booking.start_time)} - ${formatTime(booking.end_time)}</p>
                    </div>
                    <span class="badge badge-${getStatusColor(booking.status)}">${getStatusText(booking.status)}</span>
                </div>
            </div>
        `).join('');
    }

    // Update recent bookings
    function updateRecentBookings(bookings) {
        const container = document.querySelector('.booking-list');
        if (!container) return;

        if (!bookings || bookings.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">Belum ada riwayat pemesanan</p>';
            return;
        }

        container.innerHTML = bookings.map(booking => `
            <div class="booking-item" data-booking-id="${booking.id}">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">${booking.title}</h4>
                        <p class="text-sm text-gray-600">${booking.meeting_room?.name || booking.room?.name}</p>
                        <p class="text-xs text-gray-500">${formatDateTime(booking.start_time)}</p>
                    </div>
                    <span class="badge badge-${getStatusColor(booking.status)}">${getStatusText(booking.status)}</span>
                </div>
            </div>
        `).join('');
    }

    // Update today's schedule
    function updateTodaySchedule(bookings) {
        const container = document.querySelector('.today-schedule');
        if (!container) return;

        if (!bookings || bookings.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">Tidak ada jadwal hari ini</p>';
            return;
        }

        container.innerHTML = bookings.map(booking => `
            <div class="schedule-item" data-booking-id="${booking.id}">
                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="font-medium text-gray-900">${booking.title}</h4>
                        <p class="text-sm text-gray-600">${booking.meeting_room?.name || booking.room?.name}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium">${formatTime(booking.start_time)} - ${formatTime(booking.end_time)}</p>
                        <span class="badge badge-${getStatusColor(booking.status)}">${getStatusText(booking.status)}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Helper functions for formatting
    function formatDateTime(dateTime) {
        return new Date(dateTime).toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function formatTime(dateTime) {
        return new Date(dateTime).toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function getStatusColor(status) {
        const colors = {
            'approved': 'success',
            'pending': 'warning', 
            'rejected': 'danger',
            'completed': 'info'
        };
        return colors[status] || 'secondary';
    }

    function getStatusText(status) {
        const texts = {
            'approved': 'Disetujui',
            'pending': 'Menunggu',
            'rejected': 'Ditolak',
            'completed': 'Selesai'
        };
        return texts[status] || status;
    }

            // Update today's schedule
            const newToday = doc.querySelector('.schedule-list');
            const currentToday = document.querySelector('.schedule-list');
            if (newToday && currentToday) {
                currentToday.innerHTML = newToday.innerHTML;
            }

            console.log('Dashboard updated successfully');
        })
        .catch(error => {
            console.log('Failed to update dashboard:', error);
        });
    }

    // Set greeting based on time
    function updateGreeting() {
        const hour = new Date().getHours();
        let greeting = '';
        
        if (hour < 10) {
            greeting = 'Selamat Pagi, ';
        } else if (hour < 15) {
            greeting = 'Selamat Siang, ';
        } else if (hour < 18) {
            greeting = 'Selamat Sore, ';
        } else {
            greeting = 'Selamat Malam, ';
        }
        
        const greetingElement = document.getElementById('greeting');
        if (greetingElement) {
            greetingElement.textContent = greeting;
        }
    }

    // Animate welcome section
    function animateWelcome() {
        const welcomeSection = document.getElementById('welcome-section');
        if (welcomeSection) {
            welcomeSection.style.opacity = '0';
            welcomeSection.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                welcomeSection.style.transition = 'all 0.6s ease';
                welcomeSection.style.opacity = '1';
                welcomeSection.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateGreeting();
        animateWelcome();
        
        // Update greeting every minute
        setInterval(updateGreeting, 60000);
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        pusher.disconnect();
    });
</script>
@endpush
